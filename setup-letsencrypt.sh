#!/bin/bash

# This script sets up Let's Encrypt certificates for the Jirani API

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"

echo "Ensuring VM has the correct network tags..."
gcloud compute instances add-tags $SERVER --tags=http-server,https-server --zone $ZONE --project $PROJECT

echo "Stopping all Docker containers on the server..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker stop \$(docker ps -q) || true"

echo "Updating Nginx configuration to use Let's Encrypt certificates..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "sudo bash -c 'cat > $REMOTE_DIR/nginx/conf/default.conf << EOF
# Default server configuration
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

# API Server
server {
    listen 80;
    listen [::]:80;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.jirani.tufiked.live/privkey.pem;
    
    # API Proxy
    location / {
        proxy_pass http://api:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
}

# Storage Server (MinIO API)
server {
    listen 80;
    listen [::]:80;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/storage.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/storage.jirani.tufiked.live/privkey.pem;
    
    # MinIO API Proxy
    location / {
        proxy_pass http://minio:9000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
}

# Admin Server (RabbitMQ Management)
server {
    listen 80;
    listen [::]:80;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/admin.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.jirani.tufiked.live/privkey.pem;
    
    # RabbitMQ Management Proxy
    location / {
        proxy_pass http://rabbitmq:15672;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Basic Authentication
        auth_basic "Restricted Access";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}

# Console Server (MinIO Console)
server {
    listen 80;
    listen [::]:80;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/console.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/console.jirani.tufiked.live/privkey.pem;
    
    # MinIO Console Proxy
    location / {
        proxy_pass http://minio:9001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # Basic Authentication
        auth_basic "Restricted Access";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
EOF'"

echo "Creating a new init-letsencrypt.sh script..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "sudo bash -c 'cat > $REMOTE_DIR/init-letsencrypt.sh << EOF
#!/bin/bash

# This script will obtain SSL certificates from Let's Encrypt for the domains
# specified in the domains array.

if ! [ -x "\$(command -v docker-compose)" ]; then
  echo "Error: docker-compose is not installed." >&2
  exit 1
fi

# Define domains and settings
domains=(api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live)
rsa_key_size=4096
data_path="./nginx/certbot"
email="<EMAIL>" # Adding a valid address is strongly recommended
staging=0 # Set to 1 if you're testing your setup to avoid hitting request limits

# Check if we should use staging environment for testing
if [ "\$1" = "--staging" ]; then
  staging=1
  echo "Using Let's Encrypt staging environment"
fi

# Check if we should force renewal
force_renewal=0
if [ "\$1" = "--force" ] || [ "\$2" = "--force" ]; then
  force_renewal=1
  echo "Forcing certificate renewal"
fi

# Create .htpasswd file for basic authentication
echo "### Creating .htpasswd file for basic authentication ..."
docker run --rm --entrypoint htpasswd nginx:alpine -Bbn admin admin123 > ./nginx/conf/.htpasswd

# Clean up existing certificates and create fresh directories
echo "### Cleaning up existing certificates ..."
rm -rf "\$data_path/conf"
rm -rf "\$data_path/www"

echo "### Creating fresh directories for Let's Encrypt certificates ..."
mkdir -p "\$data_path/conf/live"
mkdir -p "\$data_path/www"
echo

# Stop all containers
echo "### Stopping any running containers ..."
docker-compose -f docker-compose.prod.yml down
echo

# Start Nginx container for certificate validation
echo "### Starting Nginx for certificate validation ..."
docker-compose -f docker-compose.prod.yml up -d nginx
echo

# Wait for Nginx to start
echo "### Waiting for Nginx to start ..."
sleep 5
echo

# Request Let's Encrypt certificates
for domain in "\${domains[@]}"; do
  echo "### Requesting Let's Encrypt certificate for \$domain ..."
  
  # Join \$domains to -d args
  domain_args="-d \$domain"
  
  # Select appropriate email arg
  case "\$email" in
    "") email_arg="--register-unsafely-without-email" ;;
    *) email_arg="--email \$email" ;;
  esac
  
  # Enable staging mode if needed
  if [ \$staging != "0" ]; then staging_arg="--staging"; fi
  
  # Add force renewal if requested
  force_arg=""
  if [ \$force_renewal != "0" ]; then force_arg="--force-renewal"; fi
  
  # Try multiple times with increasing timeouts
  for attempt in 1 2 3; do
    echo "Attempt \$attempt for \$domain..."
    
    # Increase timeout for each attempt
    timeout=\$((30 * attempt))
    
    docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
      certbot certonly --webroot -w /var/www/certbot \
        \$staging_arg \
        \$email_arg \
        \$domain_args \
        --rsa-key-size \$rsa_key_size \
        --agree-tos \
        \$force_arg \
        --keep-until-expiring" certbot
    
    # Check if certificate was obtained successfully
    if docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
      certbot certificates --cert-name \$domain" certbot | grep -q "VALID:"; then
      echo "Certificate for \$domain obtained successfully!"
      break
    else
      echo "Failed to obtain certificate for \$domain on attempt \$attempt"
      if [ \$attempt -lt 3 ]; then
        echo "Waiting \$timeout seconds before next attempt..."
        sleep \$timeout
      else
        echo "All attempts failed for \$domain. Please check your DNS settings."
        exit 1
      fi
    fi
  done
  
  echo
done

# Restart all services
echo "### Starting all services with SSL certificates ..."
docker-compose -f docker-compose.prod.yml up -d
echo

echo "### Waiting for services to start ..."
sleep 10
echo

echo "### Reloading Nginx ..."
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload
echo

echo "### All done! Your services should now be accessible with proper SSL certificates."
EOF'"

echo "Making init-letsencrypt.sh executable..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "chmod +x $REMOTE_DIR/init-letsencrypt.sh"

echo "Running init-letsencrypt.sh..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && ./init-letsencrypt.sh --force"

echo "All done! Your services should now be accessible with proper Let's Encrypt SSL certificates."
