{"version": 2, "files": [{"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "4c0d1712c28161aae922d6fb6aa513f3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "03663b6e95c127f3c83bb84ed2994bb2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_pip-0.0.2/lib/src/turf_pip_base.dart", "hash": "79958d35073f8259b980f1e3118b091a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "8ca934b02a5298b8f21a63ed650739b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/invariant.dart", "hash": "7fce300f1155319b29f8f5ee3b84b36a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/common_callbacks.dart", "hash": "edeb3ef79020eb4835ccfc72c6b7a89a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/area.dart", "hash": "fa01af1a08dcec1ed6f3d2dfee6907b6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "799a4ef2a4bf9f5a72c65bac4ecf23a4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "5c89cc28cd666aa1708dffaff6cc8c35"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/animations/parallax_card.dart", "hash": "4be8a373dd9a1c208732b62de32a42cc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/parsers.dart", "hash": "2faf7d9b6110bf3fef7655742f7bfc23"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "c6cd5ec6babebe0af7224db7ef8fd680"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "70b3c5178a2900b73be78d52770fcd40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement3.dart", "hash": "e0417e8f067bf4a25edc299853bfe050"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "a753413d3971339169c4a103d7ee3f6a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/transitions/fly_viewport_transition.dart", "hash": "58af695aea474581953e324f719577d2"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon.svg", "hash": "23af77845f972812e3045c32bbde7405"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/lib/image_picker_android.dart", "hash": "007c2b99a7ab8b0ea0ed298ac83d52b1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "1a0a7a32ca4a38d882836e00d31cd8b7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "4a3809e55c89557f70a08060ea2102a8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/viewport_internal.dart", "hash": "e2d2720141018c82f2fc123b6ae78b47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "382f7c8ee5e19580005898427950e0b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/cluster_manager_updates.dart", "hash": "cab5ac94382c688bcf0c15a30113e860"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/fill_layer.dart", "hash": "4f98c377c1945433d37c79d0be5a6f76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/page.dart", "hash": "4ed1ea1e77b6220b80b81d0f65976c73"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "666d237daabc113b3ba78fa363d47017"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "2a90f95a9de0d2364fee5e1ddbab0c18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumvariant.dart", "hash": "ee434a4fa96c719b92f21bf8e27b42db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "7ba47d8d884d71e42aeabcf62344873c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/route.dart", "hash": "616d1f6d7bbfbc5eb47d0f76e263f840"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "934a432cbf7baeb2d81ef25a49c36e1f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/run_check.dart", "hash": "5833549e8a11118ad17fa4b0c690d14f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iknownfoldermanager.dart", "hash": "48d51a5672af342c6b1376d1ff04a4a5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid_windows.dart", "hash": "659cff14f1665a31dec63407d7839624"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/along.dart", "hash": "6172cb386fdd0c0cc40a632b88e997f8"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/rider_provider.dart", "hash": "86129b83d59d71bf0a1e4c2d5afc56f4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "887a4888dd10dc19020553757a12bf31"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemresources.dart", "hash": "6f452535b56a9cdc6bc36bd647963dca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "dd134142f6edb06d6ad1ebc0d27fb524"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "824dffb7b5c1cc401a975820f0085fa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/loaders.dart", "hash": "93f4454ad1a70085b8e548f17997a5bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "170fe4655f45b54388ab850399d92895"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/transitions/default_viewport_transition.dart", "hash": "915c4c5c5fcb57901cbc487675b0ed26"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "fdafd11afaf787fce66b7f5890d21241"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "37517f2ff904e2bb38bcdb87c3b2935c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/pages/cupertino.dart", "hash": "671e5f26fbf94b9d5a70b14c8c494760"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/rhumb_destination.dart", "hash": "3015582ac16d615050ff18cd022b201f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/utilities/_file_io.dart", "hash": "76964a546c84af33fb4bd8b2ba2fefda"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdockpattern.dart", "hash": "e05a31b36d602ae06ddd1979c05df7a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/crypt32.g.dart", "hash": "6848c6ac5c6c2b1b40f3dd8ec0bbe31c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/location.dart", "hash": "e613efc2193d2977daa4e19d197e636a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/node.dart", "hash": "e6a5dc364972db7e4f38b5b49947b979"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdeviceenumerator.dart", "hash": "f31bb216ea8990a64c2326c16fd2ea33"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "b90ed671e7e766e8a27de8544ddbdcf4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/empty_unmodifiable_set.dart", "hash": "d2e49f7a3cc02c7bd120dd5e4b9daa33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "0cb06ef1fbbec09f85b6b40cdeaa2f9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/dialogs.dart", "hash": "ca0e62303e3d1154ac7712e05d705c03"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/settable.dart", "hash": "71104e51eaff06217afc51575bb2848f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/close_guarantee_channel.dart", "hash": "1536ff203bc26bdb4841b82c51187a6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/stream_channel_transformer.dart", "hash": "bf9deb76f520208269fedb1ee05992bc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "00b3d6ec778c057356a8e0f99a4ff588"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "b5a5a0c9d78c8329f5d91094bfd4b820"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "9537dbc5437603a8b66a7dae56c9875c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/result.dart", "hash": "87ed94159c6cc382cbe369889adf2645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "59939c42d2baae074e7123d552a36deb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/slot_layer.dart", "hash": "f27887f621e6a08eac85001a1f9ce657"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/line_scanner.dart", "hash": "e8cea99d6204f5bfb4d0e59002c95e39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "5b273bbab831a700c166cfa57a2e089f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "3455df89df3647c70f9d2586275523b9"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/web-app-manifest-192x192.png", "hash": "38bda9a7fef1121ac8cc3a8ae9fd27e2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "5d9eccb0fcbc01b2c727d80f27c5631b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "45621326af8ac20233a5cf51cb1f05ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/overview_viewport_state.dart", "hash": "cf54dc689b63ba810a2b941fcd39c67e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/cluster_manager.dart", "hash": "dbeab7fac458b2f865b6cb5e5456847a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumresources.dart", "hash": "08a61adc8ecc7216c84a455539fd75ad"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "5bae94050956f893609adf91da0b7e13"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "e822107ed1c00c270f7e9ccfe670576c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/location_integration_adapter.dart", "hash": "86c31033ab29b07809732a22f2e49f5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "33ef56c795b9e96d640165cecb2f5032"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "4235d162e6bfe281428a9a62a1077806"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "58910ceafe966e76293cc267537cdc13"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "fb3f068735531a31f3d1253216051136"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersist.dart", "hash": "98911449216f1b1c1b092954bd6cebc5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "0b9b4bed20850a8e57b54642782a88b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/database.dart", "hash": "58b3ba88bbcf26534262ce680263e08d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "8d529903ab8e80df00c0859464bfbc46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/_initialize_tessellator_io.dart", "hash": "4aa9532707ae3709836dac6e154dd477"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/scroll_adapter.dart", "hash": "a1c59f1e780c22ec4f0e14f6a1eab0da"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/network_bound_resource.dart", "hash": "4c28b311e7853b24793bca14495310c4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "f75f31535e16b018e2a5f9a968b7254c"}, {"path": "/usr/local/flutter/packages/flutter/lib/rendering.dart", "hash": "72c648384f9af54c9963265d96c87278"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ibindctx.dart", "hash": "3af3fd07f4a1feeb62307f54d5bd0aaf"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon-96x96.png", "hash": "f5f274d90b2c4bf68ba0290938aebf53"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/line_intersect.dart", "hash": "4ec882927072a9b11188d4a34f436e17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "41bc035ab11c30618d860e3d24e2c4ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "8166d4859a89eef9e25697932c522bce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/vertices.dart", "hash": "5db8a9ae89b9f40979a35f8c0eb56638"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/callbacks.dart", "hash": "6ea609be06c440417711789df6e5530b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "84bcf5111497cf7e8f1447797932cbf0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "faf60c9ef2ac54223911b10e9cf69c29"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/annotation/annotation_manager.dart", "hash": "2aa1f060d9a18b72f478615d6b0bd5fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/debug.dart", "hash": "d3b50e217be5e58d53f746ba267e30e9"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/enhanced_location_selection_screen.dart", "hash": "c752380cb5296f4f67df0b92e42ad579"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/html_render_vector_graphics.dart", "hash": "2ac99a26ca075c8cd9f8f7ffb741f3ad"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "2df422a56d9988b696a9b0950f28bee3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/comdlg32.g.dart", "hash": "9821568488904c8c1566c46762278f16"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader5.dart", "hash": "a938094da69cf329b021d7351a0860fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/ui.dart", "hash": "bab602eb0688ea2e4ce1dee9902193f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "0127a67d8f96461d1bf6986dcb6260ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispvoice.dart", "hash": "ace74499f232b87549db3ce1828579ca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "9d63de715fbdfcbad9064ab771762145"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_o.dart", "hash": "d14d602c73240e571385abe6192469f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immendpoint.dart", "hash": "08f987c2f95b3e2a51c435bd8e8c588f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/animate_list.dart", "hash": "e2368d925d056674e11f6d6a98ca995d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "975c2fb9e84493af10e44456cad39bc4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "147fdd9503161f6606b625f0ed5c1272"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/tile_overlay_updates.dart", "hash": "6323a0368f25b450b38dd30b55a884b8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winscard.g.dart", "hash": "77ba184cb297cb10af7a4e645e26b0ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/mapbox_maps_options.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/parser.dart", "hash": "fe8bc1809af0e6213c525f120eeaa96b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.4/LICENSE", "hash": "9ff9055bd8ea99f417a45dd4a0a4c5b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "d6b57fe12b3df49b484687371c08487d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensor.dart", "hash": "1093e13de26d6c3dd606a01c451762ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/visitor.dart", "hash": "a109d3c954a67fad39c6808178d6eece"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "1e20ba56045ec479eee76a7a78127684"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/annotation/polygon_annotation_manager.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/guid.dart", "hash": "e5a79b510256712e5dbab68965722534"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "c1ee64b6c171e90ac92885883f0f9ce4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "ca2f231e73aa51c866ef096e66d46cf2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "62c1ce5453fdd075196695637e258b97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/pattern.dart", "hash": "75cd4f61b777cfcb29a03be16c612f1e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationvaluepattern.dart", "hash": "ede54fd11e6d44588748f07a8711f863"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/state.dart", "hash": "9a453418cc0baa3cf4c4a41655f4a113"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_match_rule.dart", "hash": "0298dac3221d4c6752b6207594e4f470"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersiststream.dart", "hash": "7c0ee8dc84c442f69b0970bb8534d740"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/flutter_shaders.dart", "hash": "e0dff416a9e6fd0bffe5c222b6e3831b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7fc0ae9c724cfdffdda5de63fa6d0972"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/usr/local/flutter/packages/flutter/lib/painting.dart", "hash": "443c2884ba8c15b3369e06601ffd88f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/centroid.dart", "hash": "3883e7736931b79b5456f230fef62db4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/websocket_message.dart", "hash": "143b717832923afbc65172c497fd3bb6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/turf.dart", "hash": "90b06053b40955eaa0e73448bf3d211f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "86ba004de80b95197e3dbcab1233743b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange3.dart", "hash": "1b5fd1f26a29d303d480169a8310b991"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "3c4c972da73c02c19c8597669d26a470"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/offline/offline_messenger.dart", "hash": "1d5ebe03e974652aa9e2a2093fa3f89b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "d113d4a197cc407c1012ca612db651ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "36059bfe2991ae3c2a5d18941ef03857"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ccf1ac4d4f2404b45b65e33a068b3e8d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iknownfolder.dart", "hash": "9805639600096c1f056657f418f6703d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "b2da3cfd818121daf62f41f3224c8a9f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "ed3dac05e5ff3d7e183323d52491c48e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "f22a66d83ebf3e0455559a16f49e70bd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/_tessellator_ffi.dart", "hash": "5d6c8b8c3579aa8f4922fb58003e4153"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "79ac8ad87caa659775def3b2860e5a87"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/src/basic_lock.dart", "hash": "1f54b1a8a2e83cb33b70d8106a275698"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/lib/rbush.dart", "hash": "0b746333f30c1c43cd652726379a3709"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_pip-0.0.2/lib/turf_pip.dart", "hash": "8223e9face2db3a1ef1849adf1a869b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionenumerator.dart", "hash": "befc59cd40e14d926671211e72495596"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "280f78984a3d21c2b797d427c12b4c4e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "hash": "87ee25bbef5b7cb7dcb056c3ec20f243"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/usr/local/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_p.dart", "hash": "af493bb7ab298cddebf04d46f7c5dc18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumnetworkconnections.dart", "hash": "ee244b933f07447928851d56170a8050"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/greedy.dart", "hash": "ea92027aa0f1a05330a4a0cfdfa5033f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "1b59b4ab9530ab7549ca3c53173f4814"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "76e270c31be8244f4a49b954bba9c76d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "8d7a3417ced5623477f0ae66b4693574"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/from_handlers.dart", "hash": "b631bb6ec102953c2b84347f00544869"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/line_overlap.dart", "hash": "0da2fbe71c1571e7273da16b63d95295"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/offline/offline_switch.dart", "hash": "182d51fc300d5cf714097cb0354c77d4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/variant.dart", "hash": "68048a53f754265a484cc5c4798db6af"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/screen_coordinate.dart", "hash": "f907f96f75316a11711b459bf6c7b747"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/overdraw_optimizer.dart", "hash": "8345fecf6626eef2657a9b1f940f5d47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/json_document_transformer.dart", "hash": "eade7bbc9bacbd78204c7ffdde55ddbd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/constant.dart", "hash": "7f01c223a9584977891a4a70396541d0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/offline/offline_manager.dart", "hash": "0f83663fd208a3f8bbaac81fa4450c10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/parser.dart", "hash": "650a69386b74cc44539e06ec0e7fa080"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_message.dart", "hash": "eb54a5ead5cb8ea548f36e4b8780e4b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/multi_channel.dart", "hash": "ea4058222908e51164c58ef9ec3ec7b7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "8ace72acd09f9f3961b7f3bec5be3056"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "824c7dfb7d95a905ec1ba5f0d88aaf20"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtablepattern.dart", "hash": "f0583593722d8dbc8d76df7f7df11dc7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "5b3185ef333a9582c4a7bba6185e7ed7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "0f46992262b5e9def5963ee29a6b9881"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/usr/local/flutter/packages/flutter/lib/services.dart", "hash": "046141d90f3922d04cc8e19212de421c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "3b98354978b0f9b4903f388399b3b8e0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "b32a871f85e9be997f09ce9bc7a7df32"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon.svg", "hash": "23af77845f972812e3045c32bbde7405"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/time.dart", "hash": "71462b0c828c47fad9b2ef1ef68cd9d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.15/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/algorithms.dart", "hash": "5fac07b9706002db32a4c5f6698cea58"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement2.dart", "hash": "a67676334dcb7629a485b52714780808"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "84a64086b6a56b4fac2100947a29c58d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "9651186d44281a8caacff14d50734011"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/idle_viewport_state.dart", "hash": "e03b70638ec6e21c14584f1d8916ef9d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemrefresher.dart", "hash": "54ba07d769f852b6c68fa2aafd4257c8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "5711ec38f26bd1c2412a9a943a40d4fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "e5bfa9fa388f0c5d8a91c7d5bd969b19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/web_gesture_handling.dart", "hash": "9c5414cf3e41339780448ad6093672e3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataimport.dart", "hash": "b8252455a884dfc13966cec360c9844d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "60c553948e23fc8fb2ba2dfb3e7ec153"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement.dart", "hash": "ce305fb96ca9a74ff549e6ff91795e10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "190d05ecf34fbb2fd698148bc68cd5b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/kernel32.g.dart", "hash": "edf7bceb5006082ec684ee177cdf3025"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/circle_updates.dart", "hash": "5cb1dc85dbeedb5da38de7a95b38e74a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "b09f09d05be41a57a141f88709700efd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "7bb75bf1bcc0aac68c67c939cfe2eab0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/sqflite_debug.dart", "hash": "ae7e464ca638c7f5aeb5c9d15eadb1ba"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "1f9d085b26323e03c82077418ccc0a4d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/extensions.dart", "hash": "8a481254b662f67357f40346435ea6a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/_initialize_path_ops_io.dart", "hash": "518899270d23bb8654ecbc26f12702a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/transform.dart", "hash": "2b2315ff747ffb254386875918f168fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/wrappers.dart", "hash": "91e47ed79ad65391642894923c520b26"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "6a35dac0f777e7dd228bde492c4089b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "0c553b8a000e02d64689984657b137a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiostreamvolume.dart", "hash": "eb9a74dc716d537ceafdd2a40b884df5"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/enhanced_location_service.dart", "hash": "158479a2cc27fba0e3930756c1231cef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/foundation.dart", "hash": "55fdc7e9835582d898780b018eaf85d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/clean_coords.dart", "hash": "ee83725c2ccfde68fdaea573c178bb29"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "7ade1fb93c1507812cb7e1f3023b3cd4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_concave.dart", "hash": "326036ff29c6b4c3ad0f55ee939e10b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/mapbox_styles.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "b7add65f976680b338d238c4db63c68c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_response.dart", "hash": "f29d1458f73f015dabefc27f98181f05"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "195ce88bb0f906dd500f3ee23c097a95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/parser.dart", "hash": "a54725bc16ee2ca993762c441542c1cc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/sql_builder.dart", "hash": "80794af0b4b1fe6f3b36e594121e3813"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/heatmap_layer.dart", "hash": "e8303c965a578c06dcb6b7fad1a3d6ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "hash": "2c21734ae994817f0963bcea30513c02"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/rate_limit.dart", "hash": "8fed4025dd6b2e77558d840be04059f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/web-app-manifest-512x512.png", "hash": "be4f8471fd9a5d2f57bd6850eeb9f19e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/tessellator.dart", "hash": "77c9544896f5a68ee56c9ca44d686cb6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader6.dart", "hash": "693ddae25fe758b1b3329d7d0ed5a005"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdragpattern.dart", "hash": "2d186bf86fb26df1aca63c78d1f3da0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/platform_interface/google_maps_flutter_platform.dart", "hash": "b503e86a5e7a9c1a8b909a72afd75238"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "9638263938be80353a032c8e789eb692"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "09ed119e94ae6dfad7c4d883a024f827"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "9113446eba03f06e109fba1b295bd26c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_server.dart", "hash": "8580846ee9612281791cc377a99d0581"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/about.dart", "hash": "3b10ed50f64576ade40f060080b86ff5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/LICENSE", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/feature.dart", "hash": "a5691f0ba45d808d555af3996df31515"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "8ac1b57ab29335fd50ea76229944a074"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "hash": "815ca599c9df247a0c7f619bab123dad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "a4d5362f2c8444701c1e293b85fd003a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/common/skeleton_loader.dart", "hash": "94f6fef5c4bc4becd692d85c5bb17738"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "e654c239899a91dab05b11cf0121129f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "8436323dbb52826a1c0e7b504bc6eb5e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immnotificationclient.dart", "hash": "1cf0553fea22eee05a0cbb29e299760a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "557fc80bee02db6922b6c67f0a40cd34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "4f36e38eaf3608ec18c70c13942510bd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5ebb4923442d1fcc7c91f9677e086467"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/matrix.dart", "hash": "3561421bda06180a972759e28276d069"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/interactive_features/standard_place_labels.dart", "hash": "8a75e049cab5591366cce29ca51b0af5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "0145529858ad246065f7145bac7aef99"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/continuation.dart", "hash": "68166eac4341d04e2ade4cf3462ae722"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "41bd294b2c2eb1b089ab65341e92fd83"}, {"path": "/usr/local/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "a4e3123f76e135cc40ea3aa0efd2e558"}, {"path": "/usr/local/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "a22f8a00622b28aaa715b7587b658468"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "a7a9eeb4bfc63b4f552162a16b62f70a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/line_to_polygon.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/copy/web_socket_impl.dart", "hash": "d4588095b4b65ea683333ba810e78c37"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "d4634256b002bc534042b99ffbdde402"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "b831e4cd07f0e2ad701fdf6ac1dafe19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionmanager2.dart", "hash": "0aea2ad4289b60950d9a467b0e03e80e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworklistmanagerevents.dart", "hash": "a403f9be5cc42dedca5208fa2c104dd3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_properties.dart", "hash": "953396d57b69e0e889d9dfcc4f7fdabe"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "29114a10bc26482a660a1114749838f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/site.webmanifest", "hash": "e23608421838ac7896bc797cd314a70d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation4.dart", "hash": "beb5454dc4d32af79b6177c6ef646714"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/error.dart", "hash": "056ba78280a44883e05c65a88771b4e8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "7826320e6f3daff8567f45add54c141d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/bearing.dart", "hash": "af7101dd876a6154d80555ddb327b905"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "3589ad5e816918a56c21cafcc6e5a611"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/bbox_polygon.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "7f7ca3170e520952778ebe749d3cef68"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "4f4fcae47233bec91490b2c694f410d3"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/location_provider.dart", "hash": "993b0b4b8d152e30094a9bb01f8b7c2d"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "dd40b7459a5001bd452d930c114918e5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "79c4abb58f097365e2627de32331bffe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/extensions/extensions.dart", "hash": "05c0832362de468466c4e1f2ace14527"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_m.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_w.dart", "hash": "36e5b08967f3abd15930bde25e9d2ccb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "5a625dd07fb7a6dc55fa0f5c063a2f91"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/heatmap.dart", "hash": "9283ec6b773c6a71ff375deeea0f0324"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/src/messages.g.dart", "hash": "3ff09a7edec90fdf07e59bc3514ea474"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "fd0e866e44796643d6fad18400ea6a77"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "7397bb1624b672abd4672aaec8334149"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "1f442d376af9a31939dd759498712154"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtreewalker.dart", "hash": "865471d167a94c3a9bad6cea64f10834"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/clock.dart", "hash": "1355e7c034fb490a3da4b604bf4a245e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/not.dart", "hash": "5bda4c1f149d153642bd503e97906b08"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/tint_effect.dart", "hash": "dbd8209d53e47e61541fe1c14e59c19e"}, {"path": "/usr/local/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "f7b634b150a8381c9b4c03482a0d6e6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/geojson_source.dart", "hash": "3cf5bbf52de201f86e882d418f86591e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/callbacks.dart", "hash": "f9df3d7b96b20e13c63afbf548575025"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/usr/local/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "7ffc0b0f4fae5d61fd47de2d122548eb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "53af78690831d6aeb88928d8270c21ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationandcondition.dart", "hash": "698f215aeb2c56fc2970fa91499d8b77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "be15261e73d394f99ecb0d5e609aafac"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "b00868737b95fe1ac169010b28a8d12b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/top_level.dart", "hash": "3418e2ba1365bf8820838eae0da072fc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/destination.dart", "hash": "2d19e691a1bf09fbffba99eaa5b0c98d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "5a00bebaee1a7921574914f7dbdfff66"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "289dd9662b900b118f3042be16a73987"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/copy/web_socket.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/combine_latest.dart", "hash": "abf3bd2ed039bc6a844d547e8039eae9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "c23351f27a693e0330fc77704443a83b"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/edit_profile_screen.dart", "hash": "4d04a8cbc2321388e181636604c43b75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/logging.dart", "hash": "5872689884d3985685f0239a1f89f71f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/swap_effect.dart", "hash": "d19b802a88e4155dc2e45f5e1101d15f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/arena.dart", "hash": "b9bf4c34257ba7cad08d8c7092c46e35"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "c7060506da9f5033615367bcfec355d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/animate.dart", "hash": "702e19f4fbffe2ac72379fb1da6bd477"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/irunningobjecttable.dart", "hash": "03e32ac40b7907db555eec5ac3a5dab5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/disconnector.dart", "hash": "c5ce308df8cd7f6cdc39e3d532a6e907"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/logging_service.dart", "hash": "fcc34f86346862dad0709c9cbf547357"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/fill_extrusion_layer.dart", "hash": "e97bf2e000f27ee87063c3596db861b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersistmemory.dart", "hash": "06bcab18a6206389adfe991144246ffc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "d0495513468d137435fad7178ad39b4f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iapplicationactivationmanager.dart", "hash": "88d299fd8892c37bab557a1ffb9cec20"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "6a7998938486af5d266f1b9072166647"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/filters/development_filter.dart", "hash": "823ba102e678952071f18c5e6694d597"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "bd943d36cd0bfe732ff0bcad174fbab7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "97fb5affb10598f0356958c2e7e542b2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "ee984ad6a59ef4e7fcf5caa40736878c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationgridpattern.dart", "hash": "142eee94af4418beb50a22e4c3970309"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/sweepline_intersections_base.dart", "hash": "664959ccc2776bb69653975afda355a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "e82d109f954c4a736896b202eba01ce1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "35b81a5b6064d11e63b86458f991fbf4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/service.dart", "hash": "11d13e0ab0e29b85c6530f970381de6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/inline.dart", "hash": "7f107258e4c6ceef750c5b59f287068f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "5e2ac9d4e8f1c78581cc8aefa3d34c63"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/settings.dart", "hash": "64973cbd6c69149f577c4a2dc2db7f3f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "c43806aa723e38c3c106b7655b02eabc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/nearest_point.dart", "hash": "2592ba9ae1d37a6928257cce910565f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworklistmanager.dart", "hash": "e165be390861acd35be3189fe414b105"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/ole32.g.dart", "hash": "c1527bbe7fe6973a697108d13c3da85b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "8217a1327affdcc17e4e9789ac490e7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "d37e33aaef71722417cb64537e97092d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumwbemclassobject.dart", "hash": "9419b7e38f497126339e8cd2ccba9e66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdevice.dart", "hash": "545e435076682f57181d79d48821ae5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "e797d0f85b6b031854f48a68e6d9f9de"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imoniker.dart", "hash": "e6febe06d728a39b4945898e0b1294d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclock2.dart", "hash": "36e63388665f9d5f335135824e300cae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/cluster.dart", "hash": "361a6978a64d43044754857130422be4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "6d10558fefdc90f0f18050bdc02f2447"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "34b8a771ced62ddab9512678e7fe3fd2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/map_configuration.dart", "hash": "64d014e5fa76a41e7a4eb54e9f319877"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/style.dart", "hash": "31151373b96683671f19a7ce65ea7e06"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/connectivity_plus.dart", "hash": "9b43d6f9384a837bbd0d8474e2365c7a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "7ae2142321839f48597dced1087444b1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "46d984bdb7a861219c238974d6bbade4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "d51d434283a0193da462cab610325483"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "8f3d0a0b22c3d0ada7405e15eb0e7fb5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/snapshotter/snapshotter.dart", "hash": "a986ed71411b84f3f6ffcc489d4af5bd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "435a09654d97bb25c33b68b9cde0a585"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "8d4c4f339184d3cd86b0dfb7d7321d51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelementarray.dart", "hash": "41baecfe75bc82e8dae966eba92c23b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/nm.dart", "hash": "7494ac5a5e8b9d56894cd383fa6e9d91"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "20fbf0ae1f42909e7806add12b2c6e3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/misc/extensions.dart", "hash": "033cc457821088f152cc31f4439f9f0d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "b31c6998027d8e37855bdc3be9702803"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "888c72929d9b3cd94975f06965e72976"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "cf064f126091733539cece8deeefa1d0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/enums.g.dart", "hash": "68763b18d67fc053a444301a9deffb33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "efb0eae05f940ace69b5be93759acd1a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "1b0a0fa63fc381a5f245af8f5d424f2b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "64b62181e86fc993a65f92bdd4a9bc5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geotypes-0.0.2/LICENSE", "hash": "da140ba564e169cd11ee010be8f62ef2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/annotation/point_annotation_manager.dart", "hash": "31389ee2794fcfa65f8afc51720a003c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "c214cda31dee52ae8cbe8853acc2b7ac"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "d012d47412ff8c4fe1cbe6ac1d53b333"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/circle_layer.dart", "hash": "59d1a6da3f14c7f0a6326ddb6b8c973b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "4e8c9de420d0f15c8d396497675bfeb3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/rasterarray_source.dart", "hash": "6db0cbf610482e437ad9f5f253b4eee3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatadispenserex.dart", "hash": "6ee584441f30f72cea8a75f9b861591c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "360dd05d7395f54f7f785d6f8c36a191"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/magnification.g.dart", "hash": "e950e207ecdcf1d767721554751c6673"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/helpers.dart", "hash": "d6456ddc14ed2140be6ff54c585a3c75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "bc414bb2ae027237bbff00c7b2f2e2a5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/app/app.dart", "hash": "ba777b670cce35f51f0bea48eb5d22b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/google_maps_flutter_ios.dart", "hash": "9a7b521bfb19ec72b88dd11238b903be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_server.dart", "hash": "0b4a237293e913152ca376cdcfbe752a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "15378441d5acd43ee5e67372183251e2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "78fbd8387fe15451421bbe059e3deeb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/rasterdem_source.dart", "hash": "c977df45a61c0b95d0fdce77370c6a47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/sqflite_database_factory.dart", "hash": "ac8623f54bf17085dd3500961389a642"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_interface_name.dart", "hash": "4f835012742ef22df8c85292594f9823"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "6d3415786ad91086c9ad871e33c72e31"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ac787747162d980c88c97f09723f31b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/exceptions.dart", "hash": "7c49607737f1eac9820d787b1f2854eb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid_linux.dart", "hash": "cc4abe2eecf823ea14c55f9c5c09e203"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "a8a5b571b1af5fc1c05c3941e3463592"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "55f6efa34626128dab35b6985aaf09db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemhiperfenum.dart", "hash": "2b344fedd3805594c1c2981f8c06f148"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isimpleaudiovolume.dart", "hash": "654b609384b7b69890219a8d8eb510ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_tree.dart", "hash": "eedac0b4fc9b2865aae62ba790f0e26a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "15951ad2d184fb64e0327b35f1ce65df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/rider_profile_bottom_sheet.dart", "hash": "b43f6d44050d6b85c9450b173b0be359"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iclassfactory.dart", "hash": "cd0faf95b7346ac8469c545bef368396"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "e2ecb5f050ac73e6ea80ccc06cb9d7d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuri.dart", "hash": "7531be50f5bc7d9a762e8842525fc199"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "c845d2f5cf6caad03afdca9d2aa141e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/idispatch.dart", "hash": "04722e21ad1b67baca7f75a984b0d2f6"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/app/theme.dart", "hash": "86157944b02cf81c6116b035eabd2d75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/transform_rotate.dart", "hash": "445daa529a5e533eec958d3c655f6131"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/background_layer.dart", "hash": "01df53aad49efcfdf45353daaafe842a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/no_internet_screen.dart", "hash": "f5fbabf588ca366840f7a85e2092082e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "0a2cf42cdd64530e5ca9a120eda90f12"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d374a7295ed13ae994b36d002890225f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiorenderclient.dart", "hash": "678125b16711755ee7950f73890a3360"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/app/routes.dart", "hash": "d6b427111dce1ef5fac58b8e33c31351"}, {"path": "/usr/local/flutter/packages/flutter/lib/widgets.dart", "hash": "8c6dc36f670f9b9a09f5f9747abd11e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/slide_effect.dart", "hash": "a0d4a9c1b68cc6dbd6b64e41b7e92ae0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winspool.g.dart", "hash": "0f22a1dc771ec0ad975c574b1ce5dd70"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/package_info.dart", "hash": "85212538acd85eae910a66f25c6ca3ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/src/fp16.dart", "hash": "63681ec4b0641faab6e783c1c213b5d8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "94f8cadcbb0bd065c1a70e483d849960"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "496982c4b90528a5360d8064ddd1373d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfilesenumerator.dart", "hash": "ffc5c2e273fa5a533521f5e67f6e183f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/misc/inherited_router.dart", "hash": "94325c70d85d9b1d588018f56c56adc8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "314b4e3c61b5f1998e51519f9d412beb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/colors.dart", "hash": "a385ed3a073e36a430c51f9641564853"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants_nodoc.dart", "hash": "1db40035d1ebf40cde9cd2a90c1a83e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/change_notifier_adapter.dart", "hash": "783943aba9b03b1cc54745609a61947a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/onboarding_screen.dart", "hash": "ebebe79dcf6dad66c315c4b028390a6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/custom_effect.dart", "hash": "49971663c818be6f5c1da5fe3bdf034b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "f8aff0b0ae4a957a0e3637de749e41a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dart_sort_queue-0.0.2+3/lib/src/dart_sort_queue_base.dart", "hash": "c79d31b97f92d5c8cd6ef74a9ea2e2b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/lib/geocoding_ios.dart", "hash": "947d836d21c937f210012d48c5535cfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "ce859dde3195c55b2efccee1bdc51a60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "a26d02dca4465f31f1b4ff143b615052"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "d23bcea39c8a0ddcf5600a01de0d4bf9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "af3cac4b25350f32615ddef14a0beb6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_touches.dart", "hash": "69a00942eaf8b3489610732fd610955d"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/api_config.dart", "hash": "58176adc37249067ac0d370978f65163"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "164687f5f7beb0842486a60729330e3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "7a582e4809f3b12a8eeb2aeafd69fa61"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/line_segment.dart", "hash": "bd7cb0c95e5d9631ff95421378203897"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "d6345e12daac6b13e85a13629c3fddc7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "203611b95394d042fae8bef59a4ebe40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionmanager.dart", "hash": "487d0d91f9dc55efcbc2a686bbf46b8d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "74ba66d118a95cad3da88b944da7a9dc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "006f80560d9594a875c5218518a765a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader7.dart", "hash": "f697b51a3a96ab52efa2c082f20a738a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf16.dart", "hash": "07d628617431f09942070c95c65d241f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-5.0.0/lib/src/method_channel_location.dart", "hash": "a9e7d517feff58e532839faf6cee70c8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "a0740954b44e7627eebd8a66325727c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/asset_manifest.dart", "hash": "604151cdbd54ee0f0f4681fc8840d827"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemservices.dart", "hash": "58ebbd139a7de7bef2e2e646cdb00d7e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/adapter.dart", "hash": "b3593abe51c68018faf79703c522773f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensordatareport.dart", "hash": "50a6a93f5f53543a005e436586f9e24b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "0a3c66e5de5f99b50a256aac5e4207e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "989e6d99a94ac903b2156c625ee30173"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/clusters.dart", "hash": "d68f18be4dd2649ed486b5673b2dca7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "hash": "32187ab06a29c3f5929b9f26fd5ccb8b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "a1ee439640dc3ff94786e7d96122b671"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/auth/login_screen.dart", "hash": "7c4d913a6bbec4ccb228951350587ed6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/elevation_effect.dart", "hash": "57e787ad7ab7e659258e3a0171634d3c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "594fc6041eebf4706476277d9d12e659"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "ece5ee01a742a658ea5b2ee2a328c418"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "80df17b3f631d4ab4bd83a5ccb76a875"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_level.dart", "hash": "a89825cb544a7bf5ead83ab803a426e0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "db1783b3083765425632b2ca451dbbc8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "0cfa4ee77923dd3607c3e3bf35f3e070"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6c4f7ea6fca95237c26fda84192bc406"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "21cd40fc2ea0defcdc048d54b77722c9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "2c6d411ab41668e38c9c7e50dff8980b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "2fd858cebb63269bf83de20ec9b36762"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "902f4e12222d1e5b34d0ec77b2628f25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensormanager.dart", "hash": "bf3a7e591cc9c80a09c1843209bdafdf"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/jirani_logo.png", "hash": "be4f8471fd9a5d2f57bd6850eeb9f19e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/status.dart", "hash": "36dc7e69fb658976511ae6de7407181c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "aca1e7d1415fbff33606680b7d276d47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/nearest_point_on_line.dart", "hash": "77b6fa261dbfae25424f48e62e7ae40c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfile.dart", "hash": "873012eaf19c72c50b8622e17c72106c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/span_scanner.dart", "hash": "e6c901b6ad02eac173f31cc971b5b703"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object.dart", "hash": "4f187fc37cb2a7eedf4681e2321792f0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "2f21ecaf225265e45135854c47dfed90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/ride_provider.dart", "hash": "c8737003730eb2f2620398d76dcca5b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/aggregate_sample.dart", "hash": "cd164203dbc14d5701b8940f084fd58c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon-96x96.png", "hash": "f5f274d90b2c4bf68ba0290938aebf53"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "92704d1a21b1793d6070f7bee27bfe68"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "3f3f810d0df6a4d8fa696fb14a522199"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/ride_tracking_screen.dart", "hash": "b8ead3296df94caa5895de4ff1d4391f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "043377dddf07af1face4788c64ab583f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/common/glassmorphic_container.dart", "hash": "05b140bfd6d01d7a8fd9f73d140d771b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "20ff58eb86f7132e7b2a18f0442305e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/invariant.dart", "hash": "5975c7fffcdd2f67d83958a73c378fea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/lib/location.dart", "hash": "e0a5f3f89948176174bfbd38bb2a03af"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/transitions/viewport_transition.dart", "hash": "02832b50cb34d9955b850c326ac6819b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isequentialstream.dart", "hash": "b59195eae40d21212bb7b532313e6480"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/distance.dart", "hash": "4c3302ce751f70c16e40bfd6ac8c7d7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/src/set_uniforms.dart", "hash": "287a2fec0c9a35af0cc66e7c73845774"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "c0fcec4e410b3a54af8d2c09e4f6ef77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/utilities/compute.dart", "hash": "75825be8545647641c245afbba6962e3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "4c75638ad31731ec9908b311ea075a5c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "f27209609f9689165f058b3ca18165d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/relative_span_scanner.dart", "hash": "142ba8c5793aa338f44b464329dd0699"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "f183c429d3db89b9c97dfacaa85f09c3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "393af2615234850ced0bb056b30fd133"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "bad351fb6af0e461426ed48dc007c6f2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "55380226455ea534ad3f21ab09fa4cae"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "a050a2931c4a02c78e8216226cee6eba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipropertystore.dart", "hash": "2e62c409a0c6ea9effbb7a045742e1b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "77aae98676ea4f6ece383935c23c1978"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "hash": "4a9b1f00f6665e425a008a2201361658"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/numbers.dart", "hash": "89c939c52ff938d3e903e0e67563d980"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement6.dart", "hash": "e2688ec0f1c08b36b90a60cddc63b384"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/render_vector_graphic.dart", "hash": "3f4bc5c1ecc7c8c1756db79f8be177b4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "6f281d35c9453eb6092c1addcb79055e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a7d0241b77157594463b3f72e282b2f3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/sky_layer.dart", "hash": "dafc0a76676d9accf67c72dcdbfd110f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_bus_name.dart", "hash": "9cf807e15d1e83af4f62cdeb36582a91"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/polyline_annotation_messenger.dart", "hash": "f73256dd83aed6fdc04191f251281e1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/LICENSE", "hash": "8123bbb1281fe63a86386b86808d1304"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "f5dfb544b24fa84a9f504c5b8be1d838"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "0f351e2163f0c6ea9f3ec5eb8881ea17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/LICENSE", "hash": "3ed618710d8d783996cde94952a8b83e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/image/image_info.dart", "hash": "55c2cf99367a0cfdd7ef8370df299c66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/polygon.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "c9c0ff593fcabc29c3234b4e1bf2ac38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdevicecollection.dart", "hash": "5c53c4dc5952c49c1b6ccb65674d9072"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "05ae77852a25316df035464cb7ff2509"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationscrollpattern.dart", "hash": "106d1bdb4f9d839cf4a65252635f965c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dart_sort_queue-0.0.2+3/LICENSE", "hash": "8123bbb1281fe63a86386b86808d1304"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "db8a81e510b416095ef477688165eee5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/util.dart", "hash": "d66f7ff750a1747331f6a8eff5de618f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcacherequest.dart", "hash": "bec9a4fa9a224f42d622cf676a494a2a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_polyline_points-2.1.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/channel.dart", "hash": "0a0d117cdab6f2b135f35a03c98e9c13"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "b869c4e930ab3313f9b1d196d532d3dc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_client.dart", "hash": "6b3c8cd4c0677edeb4fb8c22d923657c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "34517b36f5fc8d574ff2ffaadcd2b9a0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/flutter_animate.dart", "hash": "5049b7e67d8857ca6cf426d6ae4dc463"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "0eef32ab9b2cf423c48e89f2dcd9bd6b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "ddb79a905f493ffa11db04d575d1529f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationorcondition.dart", "hash": "037c1b4cc41d0a66ea6134bf054ac095"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/mapbox_service_new.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/utils.dart", "hash": "88c24cb06bdc7279bc361d0011de71a8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "ffe5391f3b53b542cdd817dcd85e2638"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/synchronized.dart", "hash": "4b2f30555392a9ce4849932c9ce4063c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9fa617fc06f3cc28a784af28fdfaf536"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/circle.dart", "hash": "d520761a1279ff9bdb4cf4c681762ed7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "fb7de9357b27176361434afe3ef378c4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellchecker2.dart", "hash": "60ed6e3dc269f179875fea840112bc4c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient2.dart", "hash": "47b806a0c94783b8af1876a42cb6d0cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/vector_graphics_compat.dart", "hash": "a017616228e06275ed5610a4368f8d84"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "f76941994ddf30e398313421f1588d85"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "22c35af71293a579bba619b03228367c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iinspectable.dart", "hash": "3fd143ba1c7f9f9098563ee5b342b240"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/raster_source.dart", "hash": "e74f831c7e03f979455ff72dd3879441"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/fade_effect.dart", "hash": "9b97c69821fcffb65a844fb60bc2a4d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader.dart", "hash": "bcd1f230f11ee46b0ed40d340d9591c9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "86b4658ee32f155efb8c122ef85911ef"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "53b46c9af9e99869b44e05cfefbd7d46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "a5f06e177b702e5089a03bb9d18fc7fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/events.dart", "hash": "6f84760aeebf17c2107a72b4b6471910"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/matches.dart", "hash": "e2bb1be234c319b0c09b51cd14f9ab51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "e0e33434911ce4e90380de00d4f00671"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "76e3e46c95f20cec7bf446ee56306fb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemconfigurerefresher.dart", "hash": "24c932dcdfa3c21be567bbe9dd305845"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/log_configuration.dart", "hash": "41d655b1846a46004cd3aef2292df296"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "0a90eefcfcb46a3f293d18d1142328ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/intersection.dart", "hash": "cfe0df009cd9cb4b3f8dd3524eee5393"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/effect.dart", "hash": "4f1f767acd1c594d93de02e64baa085b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/where.dart", "hash": "a8ed3dae38fb7fa7baacfa77ac9bd53c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "2b9a24c4f3c66c9847e794ddbd1e7249"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/annotation/circle_annotation_manager.dart", "hash": "05883692b7f588ba2347d7ca4b78bf3d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "b8e14be092255a62d690c39bef984338"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/rometadata.g.dart", "hash": "cad4664b0591b1060e3eb77fc3cfdfd9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/http/http_service.dart", "hash": "bfe128bcf358a0b5ed98cf5cbb561566"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialog.dart", "hash": "dd9bdb173b854917c11832f369e59479"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_uuid.dart", "hash": "c9efc107e2b16a48d4e132bfcc679af4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "40a19c80c10d11c793a04d0d81a8c16e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/clip_layer.dart", "hash": "109b6e71330222e3143cde8b993b7a1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationinvokepattern.dart", "hash": "1d7963ea64a6b7059dc1f694f23f0b98"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_call.dart", "hash": "da6f500c03c005a207d38c1daf24b00a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "86a6fc84462d9d59a64d1c32494e96a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/dbus.dart", "hash": "59ba4a85ea18ab7b3030f370a0e93450"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/align_effect.dart", "hash": "4eded024c142173030b0fe3116f30c6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechbasestream.dart", "hash": "095d62c8e0367fb3c65fa8c828e95c4e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/length.dart", "hash": "5af50d92640f48dac5b6e99bb76e6c04"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "479493da08b4e2137fc162ff23bef99b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/serialization.dart", "hash": "292651436f487b4c9b3352a39ab5247a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/grammar.dart", "hash": "e0633b7a48c9c4a43b84e885dc2049f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/advapi32.g.dart", "hash": "e88da16e1dcd78ac58401bf0c3134c89"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "1916c4e665bb480220d0e6dce3b9400f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitem2.dart", "hash": "b0c96b9383b0471bcadb2206daedef05"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "d10317bd2ff80b1a8f0f01907d62334c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/location_settings.dart", "hash": "23c9a15c0b4930f8fbf1d953b15b57b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/comctl32.g.dart", "hash": "d847eca68c58e6b76393b62dc26a7c0a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/bearing.dart", "hash": "f4682a42e17801a7d17bfda43d3ee947"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/setupapi.g.dart", "hash": "ec1ebd4c36e474539966b09b9d152fd0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "7256737065bf49dbff23e83665dfb80e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/ground_overlay.dart", "hash": "7dca15b2185e6a21cc843ed1f49bd0a6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "be313780ab3939da8cf5e43dd81e8ba8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "80ecd47b65e2d516101b69dd14c0fc8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/rotate_effect.dart", "hash": "f235e4dd0ff304f8ff7a0cb1e620f83d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/open_options.dart", "hash": "0c3c36af8e9790ab80e8790b68cd84a8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "c81b77e6c86772f05b86739d8ba68b14"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellfolder.dart", "hash": "a1616e35cb9fc80b351d84aea1626b36"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/auth/register_screen.dart", "hash": "a7b79f616545e86236be619c146f1997"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "d76b7e85f343a184365107019b8117b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "8ebc4ef8486c9875330658ed1a145020"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/web-app-manifest-512x512.png", "hash": "be4f8471fd9a5d2f57bd6850eeb9f19e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/raster_layer.dart", "hash": "b3c5f215952791e06f54aa3a78de1cd3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_clockwise.dart", "hash": "c7ea45adfa9bfbb80a92787d4c110616"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_u.dart", "hash": "de4ba796e7c200bdc07306e8b82e1f5a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "444589d7a3a418a8388003283b096007"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "3ff5c718c1926e8652d8ed393c236c7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_family_with_variant.dart", "hash": "1562c4a8bfee3d68c041674517ef436c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/shell32.g.dart", "hash": "77833f9ce93791f664316db43a55505c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/turf_adapters.dart", "hash": "61bfddf0e34105cf8d78791ec7eb74e2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessioncontrol2.dart", "hash": "18ce35bef6b656745428776b3aaaf4ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/factory_mixin.dart", "hash": "25c9f7df4c862471f534b041fc4213b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement5.dart", "hash": "e053a966b20fda12dc7d24e0f56c845a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "b5439c33692d13cbf7df2d19d9713345"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/date.dart", "hash": "467e7592ed2562b6ebc43d62b1015271"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "3b32647556f88ddd6d625ddc58c7691e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/src/network_manager_client.dart", "hash": "60838abe37c945cf06c1b5ccc5066fed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/reflection/iterable.dart", "hash": "a75fb12af280fb36882c184800a13fde"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "0eae8cad9d933f0478d8387400def317"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/lib/geocoding_android.dart", "hash": "bfbf16c2cc0c1b08b44c6fe479e45fc7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/misc/error_screen.dart", "hash": "72d27451431aeaf0b4f073a66bacf00f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "de3213b3d5bc998d1d921b4ce782f91f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/table.dart", "hash": "29e1858c5ebc2b4dc6d1528196bfb1b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/istream.dart", "hash": "752db229137baa4ff1a3eccbe3cf69b8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "6e800790e7858e8e1cdc73c8cc09d719"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/hillshade_layer.dart", "hash": "7d0e5513b330569cc3bd47fca81316e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "033f66ce41fadd1cb1e04ea24214f9c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/lints-5.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/midpoint.dart", "hash": "2a42b429a41cf9f965bdc9c47dc9ce88"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "53870a85433563b929e07964cff0d2c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/reference.dart", "hash": "3881ad72fbb323a843aa4bf47c99422d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "3bdf4135a561f156f34a8ce9375819ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/src/shader_builder.dart", "hash": "0e01c8fe91bc7e95b9b3244d2020e07c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "5b284216cdaff11bd25b0175739bf48b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "2568c82b17a02e29a61c5e03a4eacefe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "1ace25dadd2dacbee3c91f367c9429c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "d5cd032ed35d1180819dac9428cd1035"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/lookup.dart", "hash": "22d4076f2d38c3a2fed532fb53ecb1a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/cache.dart", "hash": "6e8886f8cef9ae40f50a953811fd6376"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "aef3722f9d145aea6daf824f7e02a840"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "385e7301c1c09d5c45f0531a5c375c6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/rhumb_bearing.dart", "hash": "aff5b092fc455a61448453f95afbc88a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/bbox_polygon.dart", "hash": "********************************"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/message_provider.dart", "hash": "c472d3a31b09411ea1e715b4d3d532f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/draw_command_builder.dart", "hash": "f1457454215dff1ed947363fcbf4aeba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/google_map_inspector_ios.dart", "hash": "4cc5c06032e753889957080fa13b0647"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib/google_maps_flutter_android.dart", "hash": "936badc0ac65400cd636d0064f84fbf3"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/.gitkeep", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/extensions/animation_controller_loop_extensions.dart", "hash": "6f060a03e28c25d797360a2375efa878"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/legacy_api.dart", "hash": "e62a8f39ad332b5e313b0be97f2d280f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "bd34896b1432d6f707498d3df7a7c3ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/polygon_to_line.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/utilities/file.dart", "hash": "cf3d93bae83850abf2c5e943a6b1ccbc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/string_scanner.dart", "hash": "07758299bbd2261712f35210ee2f645b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/distance.dart", "hash": "750751ea5ee4b8830d710586a94a02d4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/image_picker.dart", "hash": "0fa6597e197515cef31263aa53dedcf5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/gesture_listeners.dart", "hash": "15d57eec62165bdae0b50d2596cf11af"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_equality-0.1.0/lib/src/turf_equality_base.dart", "hash": "e814e72b74e1ad5115d5440f49969c00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationboolcondition.dart", "hash": "96cd038c21e3727eb6325be0268a7ed6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "4046f679f31351f52629d1b9f22e8b6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_queue.dart", "hash": "54e62d85cb0f15d21b510ba8342615aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "25f82e13ae2a60861c029aed5f4d8c92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "9c0e3742a2b56252c568e7f0a0af8810"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/auth/forgot_password_screen.dart", "hash": "1dc3c0e764018748b4f8d2a6266dfd6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "b2516cc7704e0c10a5f1d777ac857ea6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/LICENSE", "hash": "e6dc762235c3e4ced5e68ca3a2179f59"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/svg.dart", "hash": "c9811a3fa439088d3ac977d672a6bbc8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "b79eb4cc037c18412c9e228e1974783f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "1ea35c2990caf75b07d8a555f3f49191"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/structs.g.dart", "hash": "67f751cf689639227d5db57f73b92a2a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/utils.dart", "hash": "ee746523b6e3d70f4061a5750c37cac3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "21262cfe42ea14c1c1b45867971224d7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iconnectionpointcontainer.dart", "hash": "83f156972f99a181b244f428cdf134bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sqflite_darwin.dart", "hash": "00e989c051be91287e0da3bd26a753af"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib/shimmer.dart", "hash": "49d17d6f2c4de81e5dc0cf98d720333a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "2d7c9fe1f427f655c48bac29cbf4ac3c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/geom.dart", "hash": "1e76b4dc0fb68b607f9b9b6acbc8e52b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/listener.dart", "hash": "65ec707b3eed166828768c1ac1cc45f7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "69da3c88c748658e0c9e7216fd9398cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "d68d5ad46a579bc5f35d3da1c2f915ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetwork.dart", "hash": "d2bb1791822e1c17a18ea8f306180296"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "718652a205e0c90b9df973c701c36a02"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/collection_utils.dart", "hash": "2b1435db1169d8d724de87f5054c89c8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_buffer.dart", "hash": "99760254cc7c1941d4d7d7bb0fad045d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "14e0c453a6ee4b91f65cab141d997a88"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "d4b68da22867b9c51c88acc54eab3198"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/extensions.dart", "hash": "95c9d769838a04fc75f17f07205455f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "365a4e930595816e20608286252762dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxpackagereader.dart", "hash": "2c9b99820a7ba58eea5e30ca3585c24f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/usr/local/flutter/bin/internal/engine.version", "hash": "c172db412fa8acc0fefdc08c6feffed4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemfilter.dart", "hash": "3fb5dd9d7f42a9e619dd81d5bbead392"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "d8f8a80ad0c05f281d58e8f9e20b8b14"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/errors/no_result_found_exception.dart", "hash": "82401cd2748e687908e7b693aa5811ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_overlap.dart", "hash": "a122dded20b921437f8318354e00e0f9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/win32.dart", "hash": "a2afa1345e294f0beeb9a776908eab25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataimport2.dart", "hash": "9cea354b06cd8542da4dd38ff9fc01e9"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/enhanced_location_provider.dart", "hash": "f970ccf05dd5dbd74244f4cb47e8ebae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "hash": "96ed4c0b2ac486bba3db2c5d2a96afc4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/fare_estimation_screen.dart", "hash": "d5ce96be740630d39ee07219f7f4845c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "b294af2c4f13d55aba2bf44369a7ace3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "8518b1367156079d21cbecf7217d2152"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "d2c684f89d90661960c497d7f4faa906"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/symbol_layer.dart", "hash": "820e1558ae58c747e739d13f14c1a63a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/scale_effect.dart", "hash": "52deaf1c36d56ad77546bbc624e91b61"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "0a7ba6f4900ffac5152d0a67876d6016"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_extensions.dart", "hash": "040a16c5fccfea5a33d4c771c93003c2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_variant.dart", "hash": "ce58628e17748af44a93e252b9c54d1b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/file_io_desktop_and_mobile.dart", "hash": "a2f208880d92532a9d975bee2451eee6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "e8f704ef18663c53424e306c38d97d39"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/models.dart", "hash": "9097e5377036b3a0c428d105c016509d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/path_ops.dart", "hash": "9ddd18d11aa8b6e12c10e714c24de8e2"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/6f00e7a33c5475ad842f3d6cc1a04a1a/app.dill", "hash": "80ecd47b65e2d516101b69dd14c0fc8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_equal.dart", "hash": "fb82b0074c15fdb556f4d09ffa94d0ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "9003679f83073733248c1bd73d3097e3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dxva2.g.dart", "hash": "73ec60b4a67001fb2adfab990c932c6e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_helper.dart", "hash": "667c3966d5b230fa6ff9a3c232c50792"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/rider_card_horizontal.dart", "hash": "2aeb6186843e1a65d39bcd60de811e76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/ground_overlay.dart", "hash": "7cab7c6d70a64443b1bd47b35eda836e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "0381c11b6d4f8c985d498d475bc82822"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationannotationpattern.dart", "hash": "2a397f62f7c1670044f38d8f4af1ec92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/vector_graphics.dart", "hash": "671b26ea03821b4c63c0fe2fd64f9e87"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/ntdll.g.dart", "hash": "80549b960bc9c7fd9dad05aa69b7d9b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/line_segment.dart", "hash": "96f19a6b58945d8d6765782b551b0f65"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/lib/src/rbush.dart", "hash": "76e84425c91b4418a75f69fc68267a18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/camera_viewport_state.dart", "hash": "634559d2b2c62de39860332317c54c24"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/basic_types.dart", "hash": "62ed7bb0dcdc2c9125d24015145427a5"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "f2b507816a8a883b33c9deb1a9c1a346"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/flutter_animate.dart", "hash": "ac3c54b4fc84d74ca2726a43feb3c268"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/log_backend.dart", "hash": "766384005adbffc17b69352b5d0a63c8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "bf286075da7a9e92f62095e254609418"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation.dart", "hash": "e1980812801e0d89e39cfa0bb4cf7fb3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/tap.dart", "hash": "2174cee3aa85b6a1cb77f1e9f1f54f7b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "26e8edddc50361d04ffdac680bcfeeca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "576b6e090503e6ebeba30801482d3b66"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "51bd3df911d2f628879de56dcf93d74e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/loader.dart", "hash": "14d64729800391fb58a32643b7126984"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "dff97db228356561674b5f690cd54f41"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensorcollection.dart", "hash": "b43a69dd26a10426aeb7eed269b4cd51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/wtsapi32.g.dart", "hash": "d6db794e2f414caa48650e6bc2396e6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/follow_path_effect.dart", "hash": "04e0e4efa9726c234e992a024bca6209"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/connectivity_plus_linux.dart", "hash": "2aea038844961a04f31f81fbd8503cb2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/location_indicator_layer.dart", "hash": "fcddd76b4a6b75449b8e224b60dfe97c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dwmapi.g.dart", "hash": "607cef6a651964e0339d2e09df046c09"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwinhttprequest.dart", "hash": "b44c83e3276e2ebad7c43ed8d7beae72"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/rider_selection_screen.dart", "hash": "c34029aa56a433ddb0b670ed346054b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "0a1c3f1481c65ee1018b56fe8d8b84ef"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "5eaed6b1fcf32a11b53e5dcf27ae101c"}, {"path": "/usr/local/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_within.dart", "hash": "6d7b7c62a173f9c2b3916fb37d8b175d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "34f75dd4788c48a2a7b2ce2efe4c51fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifileopendialog.dart", "hash": "e1b16ab85c86942cde8fabfa972fba9e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_map.dart", "hash": "2e3907a6bf1a5ac452581b5e1d72eadd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/_connect_io.dart", "hash": "97d42b77a7fe92d562988b9fa3a3efbd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/fill_queue.dart", "hash": "83550f722ae723af02a289c1c8a7c660"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "32d8ff829d8956046c0a91c8ae4160a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/marker.dart", "hash": "be33b72abcb10e6aca9fc36bc210726f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/follow_puck_viewport_state.dart", "hash": "af7552c96ddd3004fb5f5b17a2717e93"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "e8106b34b25812580ba75dea86a5a096"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/platform_interface/google_maps_inspector_platform.dart", "hash": "664d7c196e26344f89d0d5b45f19c32b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "hash": "95bd0247422d589a2b39cd985a000749"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_n.dart", "hash": "8fe95cebce3f522e41f0bef51a1818b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifilesavedialog.dart", "hash": "a629548f10bfeaa42dfecec77c11b6f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "366f1ebf48ef3c69b4e7a9ddcaa8f3ca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "69e6b2a0e521311c4d86f8d7e93e29ed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "d9f9f2488723c1e03b8804bbeb41be03"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/placemark.dart", "hash": "ccbc13b0b7e37d98dadf9eeea885ae36"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/polyline.dart", "hash": "06d7ea1b3a4dde08c39605f560bb9b67"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/crossfade_effect.dart", "hash": "831f10cc3a34d37027e629ec7f71f563"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader2.dart", "hash": "a109a0fbd62a37b4cf3b416da4411578"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "eb295ba3319c0f2b1c910d981594bab7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "9fcf9265f470f44989cf4da88dd7cc0c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatadispenser.dart", "hash": "e653273473a891c0739e255d1b469d55"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "5d93a9e5daf8f93e7820b5a2d1fa89d3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "8c3499889c31838ff4de84d56ebbdebc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextpattern2.dart", "hash": "7c3e512b5c20c07ddded2fb71eadd848"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/stream_channel_completer.dart", "hash": "fbd23a681c22b782bdb58d139c07cca6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "60d167d34050e1468a18e6a768d9d2bc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/blur_effect.dart", "hash": "19ad609d2f555c552877714f4b07f150"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/flip_effect.dart", "hash": "860a0e6b23bd728899d9bbad3e966f2c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/path_parsing.dart", "hash": "498f254119e3d3c67475fe8ca026d01a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "b40780510c9b3d671dd86b07b1f812e9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/switch.dart", "hash": "5b436e60ead9eaf8b303aa72abc08744"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/explode.dart", "hash": "942304ca171ae4c9d3230c4b271aa1cd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "f3afc3b9324005ca112ccef93598cd89"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "959489b18fda284c434701586b43c66b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/possessive.dart", "hash": "e67b8312f8af090a5eae36871ab6b65b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/polyline.dart", "hash": "2beb6b3726f67d960c5c957f31c45579"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "25e50c0fbe710a5f7a590c3c26703f60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_t.dart", "hash": "8f142b64056bff3425661bf170728f45"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "e438b8b77c0b056309e25325952b64f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_k.dart", "hash": "de8b58c147e392ac3e1a5479f4941290"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/render_object_selection.dart", "hash": "dd006282a4ae0bc84d10206ea7441925"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "610943df3ed3669788eee55726cb7035"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "e62c6008d26fdd56ee11d82ca4f1d516"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "4a909f493f4dd8dfb93d3a3d4843bd77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_i.dart", "hash": "706f1120f2aad4e908056a2b4f16eb23"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_search-4.3.1/LICENSE", "hash": "********************************"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/rider_review.dart", "hash": "1e82b8ae087fdc00bbb4cfa87001a3c5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "605a2aef24ebcbd88042fc543e69a82a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclock.dart", "hash": "7c32424ef2aaa2f268fe177af2d4731f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geotypes-0.0.2/lib/src/geojson.dart", "hash": "9b450ac5bbbf8c46c4817895846f4d1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "hash": "1936d57a483f9894c5b5c3087b446809"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_pip-0.0.2/LICENSE", "hash": "8123bbb1281fe63a86386b86808d1304"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation2.dart", "hash": "34d140191c4affc37f3716de1b46854a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "cfad5d08fc946a2e0a67e46bf582130c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "fe75cb9d73a87bf59cabc3af4d5072cb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d6d7e025154dccf2e3d0b12eb073f93a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "2c3db13235dd0c924d1367692ec4ae1f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d235f51d48e43d80a46b35d3ac1a7135"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/place_result.dart", "hash": "532fd14ab4fcfe7ff220eb8c07119d30"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient3.dart", "hash": "025a4f4e26446bf3da88ee7d3cf3d0f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "b87bce461399faa5b57c569a2fbfdc0e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "99f994fae7b85fd2e6cfe48e211da58e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "1bb32014080277421d4d679148d11bb0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/map_events.dart", "hash": "fdf4cea9a3bac0322cf42bb15ad7ff02"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "c970404e32ab9a5917d955f66c830b1e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechaudioformat.dart", "hash": "36145af4fe8f10df91f98b13659a7b23"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/color_effect.dart", "hash": "aaf31c7a7690aa5b5980d454e534d168"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "02be76cbb0e6152f7162d22383971f51"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "db01043236a4e15ffd4e3d8fad4c7cac"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "1786653a5a86ec6255f79137a3a33755"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemarray.dart", "hash": "40abc849ae2322b6e6a63d567f952f1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllinkdatalist.dart", "hash": "68642e049d1aa7d3e55fc1382b0696c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/map_interfaces.dart", "hash": "6bef7a36de3e137e46b1aa331a7fa5fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_f.dart", "hash": "01acde6ab3416626c8fe453d99c13480"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "2dc7dd4788151c9148ad7b2a91058edf"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "cb19324d7400b29cab877e6fd6aa0289"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "8b6832f29637935d19be203efb2b1283"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_base.dart", "hash": "ba78ae31f8b033543921d261bbe60dca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "cc8236ed613332ed202cadb26db1c743"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/path_utils.dart", "hash": "228413c644cb471229965818da205c6f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "c95cc3d4d3122294945f603ec0b3132a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "27006efbb2180e9e1afb93a52e89dbe8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/flatten.dart", "hash": "32555fa5e63954e6fe4b4f07817914eb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "22fdfe2139eaf6759fc40d9fa5fafab4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/file_selector_macos.dart", "hash": "20f3c0d39cbc5c2fdb223745edcecdec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/google_maps_flutter_platform_interface.dart", "hash": "4c0416309d2dc70df436ce304ed86154"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/shake_effect.dart", "hash": "2fe2bbd579687d09b982afec08e8e688"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation5.dart", "hash": "3681275c274b0e2b2c9dc14ecc273c1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/_path_ops_ffi.dart", "hash": "9aad11ee7e005623c54a1c59038f9d4b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "34df6a85c7662f602e66bba150c4fab4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2783f528d559449fbd0b97561717c83d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/src/reentrant_lock.dart", "hash": "f3f1d126f0461776f6230c368eacdc70"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/char.dart", "hash": "5755449cdace9c88111718f61f6c25e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/configuration.dart", "hash": "1ca6e7022b3a950a205e316f7e3a2b1c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "9b9a244b67b67b84170d3c3ee4ec2c73"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "b5d6c349fa0259f1516951989e4d5bbc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dart_sort_queue-0.0.2+3/lib/dart_sort_queue.dart", "hash": "4fcc06ce52eafdbb0a70fcba1465ff4a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/location.dart", "hash": "836295c2aebc4951c13afe0014530125"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/raster_particle_layer.dart", "hash": "73233e7751a8c1c60c413f4b7baa0403"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "2c42d9101e59a8e036ec031c0aeaaf08"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "d64508f06238e79c4c99b1af662963f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_manager.dart", "hash": "5f173a5c0de15909e95d3275051138c1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "0b0f625bca76693cdeaa1f4358809351"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_descriptor.dart", "hash": "df2373fa53c57974996330429774683f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/stream_transform.dart", "hash": "2f811178fd6401d1399cf8b09cc1f9f4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta.dart", "hash": "8042ca366a626884c0e89628875af940"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_point_in_polygon.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "f4f97e64864383af2f259063e32bcf49"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "hash": "d631809a6f4e20b7aa9ea7e17a6581de"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/6f00e7a33c5475ad842f3d6cc1a04a1a/native_assets.dill", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifileisinuse.dart", "hash": "5abf40e886af8feb42ccc62d31044f48"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/flatten.dart", "hash": "c449b66cf3470d33bc31dad548dc5d4f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "1dc7dcdd70674a9f80245280f277e7ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iprovideclassinfo.dart", "hash": "c90759e0e90f88fd2b4f177ec55cb4f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/pages/material.dart", "hash": "61f9ae17975d4d233db25ee3f27633bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dbghelp.g.dart", "hash": "0eab209847ef951bd0a6ff1418f74ba1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/benchmark-0.3.0/LICENSE", "hash": "25abf086a5223a1e7f908ee2062b7c93"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispnotifysource.dart", "hash": "c126b73764228fafd6b80ed5e2d7ff0f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_disjoint.dart", "hash": "162e00f20fd924d22e9c28743689ef97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiocaptureclient.dart", "hash": "98c8a48ba3ece7573f6f3a9bfde19840"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/matcher.dart", "hash": "e03e435d13626ba1dd09f8da7bf824a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "629b25992db59361e5889a42c7de583c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "5c0db57261dd10e5f760ac757f4fcd76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/along.dart", "hash": "ca532ef5db0977fe597fec3bd8ca76fe"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "22dc080522882b010d90c7010b5aeba9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "fdb8e71a63736d013545f8830dc5c495"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "19ad3f559f8a8ac66bbf9a697588b5f2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "87f486583cb723b203e4e0d12d1958a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "173ed9cde7e32db3e5ff3867c12b7824"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "81ca0774ea5d4ff5f061ac7b53957e6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_client.dart", "hash": "3bc24109049f63bedd0393f75bc23503"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/line_overlap.dart", "hash": "4abbca4bd6da08717ca585f60f64e5a2"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/data/repositories/base_repository.dart", "hash": "047261ebad42503273ac18dbbfc821a0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "e7fa4c3593bdfbc187766bd200e60599"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackageid.dart", "hash": "74afb02c8643f153de3fb64ad8a466a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/stack_zone_specification.dart", "hash": "f49eb8c06db0ffcac4dfafeb964e24cf"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/pubspec.yaml", "hash": "8ece9f32400356e6ca9d73d8fdc4b611"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "70b3e472309bc151209b07238c849c38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "946edf55d16a6affc23769f4e73e5fee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "780826ab1f1e8af513298cd5b5bca297"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "5f94dbea71a53ba72600c479a41fa013"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ichannelaudiovolume.dart", "hash": "8ccaa7ec037755c10bf5586831be0fe1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "b3dd803ac13defc89af5ad811ea2ca31"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechvoicestatus.dart", "hash": "f242cfdba2fc6ad938c53befa4c2050c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "329b723b2cea0443e5ec2ccfb31fbfb8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "2aca9cb812aa2f6f590b65b326ed333e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_h.dart", "hash": "fcfe1d3dbdb081cdeca153aebf6667ab"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "76f393e988aadd48d101cbdb0255434f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "bf4d44ff5dca3de072782665509d0a7b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "9590cac03fb5c0de43845ee528380b4e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "682907a0e9e60ab53b752dde1e45381a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/maps_object.dart", "hash": "50640193af96b7fe69e71e24489e2f58"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "3c625e7383a98ccec8538457190e378f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/messages.g.dart", "hash": "5f926ede3679035dbc9b078b96518878"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/center.dart", "hash": "97bb6f319288e43e86d5980ebba412fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellingerror.dart", "hash": "b78ba1985c8ec9afaa7beaa601fa8e00"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "1f07e42c78eef32e5fa787ecd1e72049"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "793424ed524885eedef0340c067b865e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_write_buffer.dart", "hash": "63d2768cdd6ab5a282fbb6a86c237b78"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "ab825e70224c09e49073e60d462f88fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/sqflite.dart", "hash": "badfcf916ad8ce9a472579d404da90e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/nearest_point.dart", "hash": "5c9f5ef19ae323ab187d7b5cadccfdc0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b12f18fd97ffec06b763749adcd080be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/web_socket_channel.dart", "hash": "77227643b5a655ab1fd24917c5a4675c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/polygon_annotation_messenger.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "5528b93def00b5b750c964a10f323900"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/vector_graphics.dart", "hash": "199c06bd523145278bcd9ff9c6fe6a18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "942fbfca7541358613467c640e1ca6cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geotypes-0.0.2/lib/geotypes.dart", "hash": "a07491a8963893b63b960a7cd2c3cd92"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "ab99fd7503e96bfe98caf7abf357e186"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "d34b1e33e7604b54b656d4c7471ad8a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/line_slice.dart", "hash": "87ca98cc4de7f6486c59d1f53be0f54f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "5d34c419faa453f50535c81a93de00d0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/custom_text_field.dart", "hash": "7c7d90ecbaea4d2e5345b500ddfecd7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "3fb251a2c503ed05e490e8bf688e4fae"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "3126d82e17646add4e00c2099ec262ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/cluster.dart", "hash": "51b65a97c495155c6a5db02c31a05a6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/default.dart", "hash": "a485f5c7307db6bbba1d3388b528a770"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/fare_breakdown_card.dart", "hash": "241e9121426ca498aea8d447d3fec3c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/toggle_effect.dart", "hash": "959421bf7780ce3cb78244e6707be380"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/async.dart", "hash": "b0af2681de06f072c797fb66bab4213b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality.dart", "hash": "4cbe8ed92ec76b5cd80e685ba71acdb4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "83ddbf5c126feed94b2f90784c17d5b1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/effects.dart", "hash": "a6c66093a1bc730ec1593104a477649f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/6f00e7a33c5475ad842f3d6cc1a04a1a/program.dill", "hash": "80ecd47b65e2d516101b69dd14c0fc8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessioncontrol.dart", "hash": "db4827f3013417baab4977d3f19afb1b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/oleaut32.g.dart", "hash": "fae27a92131d4f2f2005c5312e499b8f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "34f1383424d8e23bc3463188bcf19dcc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/annotation/polyline_annotation_manager.dart", "hash": "ebb15c6aead69005778889d90e5083e3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "e7d84c68f69f7f105e4acca6946ded83"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/data/repositories/rider_repository.dart", "hash": "8200b494bb82a03025291716f4149b71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtableitempattern.dart", "hash": "d004b4e52622f42ec84eea09ede49f43"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/box_shadow_effect.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/merge.dart", "hash": "b011c1c4f56d867e644def305d52ba88"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "0a816e62151c063e79b644989abe3075"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspect.dart", "hash": "1d43aa18b7cd09879287a4e8ba5ea5ef"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "97af54574da94dbb0a8b5a5549e954b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/boolean.dart", "hash": "5f6fd43810dec8f6d40fb0803436fde5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/lazy.dart", "hash": "66b974c03e0f08d662d0bdfd2edb44f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/petitparser.dart", "hash": "6ed88e961d0aa7c9506e4fa969be1a92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationgriditempattern.dart", "hash": "f558b0876d2ee3eb7fc5b350a5ef85e7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "7e7b2010d6453107351c17753d81b0b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_s.dart", "hash": "2d79382537f3ba898ab7a80cd0fbf0ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_address.dart", "hash": "4ecc0e7678d4ed3bf62a04b3e383e424"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/meta.dart", "hash": "383ef2e85286e8a984fc10369353abf1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_g.dart", "hash": "edafd82e0b999bc51b79c8a3561ff1eb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/cache_manager.dart", "hash": "b0cd74c38be1298c724272199e62de37"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_valid.dart", "hash": "1e68706766672b3dc435def083f1988a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "679653d6c84dbbb523d31feca1431de5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/information_provider.dart", "hash": "e0e6a22d50cab6e16266023c58517b54"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/irestrictederrorinfo.dart", "hash": "6bca90e19560bd62e32b8e41c835d71d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-5.0.0/lib/location_platform_interface.dart", "hash": "78295bf4c95451a1ae3832cb7f3cd6cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/line_to_polygon.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "52e418c1649b02d8f12083c6ece3f93c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object.dart", "hash": "0cb51131f14d4d8df95aee83e4931780"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "e4bd74894ed2108af4000673a2a92326"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/vector_source.dart", "hash": "ec959493cc114a17419318caea3b83ca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "660924f2468f228b5a32596ae5bb66c5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "3fd3c4bcbbf54fbcad3b048e3c89d43f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "eb2a941e76ef3aaf9ff856a5d93e0f7e"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/ride.dart", "hash": "8edf7ee639c9ef209b230254b37cd308"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "65e6240e049c500eeb0bdff33155dfba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/src/path_parsing.dart", "hash": "ce49b6015a5f5b5bb716420efbef22c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/lib/src/quickselect.dart", "hash": "b3e11d4de6e44e0a6db17f21b071d204"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "b526e1fcb69f0ca9df233cd2fb6e69a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/user32.g.dart", "hash": "e2d16ea1496afeed39828f05f60debd2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "fa3cf16f88096f2239ac79afa6bf6c1d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "81c893b7b7339a7d3d6c16614e71163b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "51f656b4d880a885413a2c963bccfd3a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialogcustomize.dart", "hash": "2815892e3735c223c62476ddaf4cb27f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/allocation.dart", "hash": "7c8e196c6c96efaadb0e605ec3cb1cce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/scan.dart", "hash": "de155f165219b10ba272bd030794873f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "89cb61ecfad952e35daf52caac600894"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "9c3c2afae62dafae40a282af7f685943"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/uxtheme.g.dart", "hash": "30d51f71b24984c4980f3f3c13df8190"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/home_screen.dart", "hash": "db8e8909080e687a6afd2e9af193a04e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "0192533b9be6f394b49a75b38f8dc84d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrangearray.dart", "hash": "5a8ea03396d41d3b76a510289dee5d25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/heatmap_updates.dart", "hash": "ec27a08abecc719984d4d481b6b67db3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/cap.dart", "hash": "94dfc479f1eabd41b61c86556dbb1232"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "31caf5d9d4f0d5e2b373a2bf368290d6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_member_name.dart", "hash": "2ef397117616f6ff779ed0ab2dd0d61d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/center.dart", "hash": "c0c83ba05171b65c18bb4954765af929"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "642ff6f56b746c990445d42331e8ff81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/bluetoothapis.g.dart", "hash": "eeeb5875589f6bf64f72c094f0435b92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/circle.dart", "hash": "bca0bfe3ca41406b698e27501b16244f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "8df5a0fc260d13ce415e2262527a1f8c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "aeaa12c1af305eb8e588f3b7bec09ab1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/match.dart", "hash": "26dc3f636c8fa15ab2024c0b59d56ef2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "e06184900e9722a899299b08b5b1d95c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/take_until.dart", "hash": "6c873115296f0c3c72777a00c58437c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatatables2.dart", "hash": "bb9a6b8b9225608821735003ffdc8a5e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/mapbox_maps_flutter.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/package_config_subset", "hash": "7101a2a1216db4915a3f1ef822b05cac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iconnectionpoint.dart", "hash": "ed361e60fcf89da03b59c13d84579d0d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "e66727f5e99e5f6a3d58e1bf0c47aa97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement7.dart", "hash": "cd0365e9895a1f44235bcf2288a11f66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/concatenate.dart", "hash": "42804a1a3f9bec032c0743b86b0a5548"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "cee61ff4bc1494858ec39f8c4f09c1a6"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/rider.dart", "hash": "b363bcf6613bd8d9add2fb52ae5f82db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_d.dart", "hash": "2a101a9f7dc3955fa1a1cb93fde33565"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "932fb518b9857889d8182a4d9d78d2d9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "dd25c518d50a5334f0a231570f7c919b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/vector_graphics_codec.dart", "hash": "1cb908a37b3d1b606ce345ce629694b9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "537e9b325c6baa5e02649883f515cb86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "99f17d3841e146c27a0079f86b453123"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "8e493e051c6c0cbe792781a7574a8f26"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "820396e431a6e00298858ae0931f50a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "c662670b9313c57d5d61b85f539a5797"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_expand.dart", "hash": "edf98e44de04feefa196e98d41cb7813"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/site.webmanifest", "hash": "e23608421838ac7896bc797cd314a70d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "6b92d8f12a7fb46649297e25d2cf2b34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/helpers.dart", "hash": "089ff951ffc1aa83b6c74b47da31b898"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "72c0cf2358f026290fb717e74a910900"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/macros.dart", "hash": "61161beafb5147bd9216c091fbe776c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/offline/tile_store.dart", "hash": "c05b1bf2c7d109566ba5c714d9e737c5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "e41783201fd039e1336413dd423a5796"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/route_data.dart", "hash": "0677541103e00dec4824db19b5f450c8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/short_circuit.dart", "hash": "2bd5c87c03c7b34a092f2256b16bf6a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geotypes-0.0.2/lib/src/geojson.g.dart", "hash": "ad3b43002d3a2ee414a325f5eaf53c75"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "17c301dab5e5355a9c2683e7f3be7ede"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "aee97b3fb21f5d87cc7a4c1079c77aa4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "7e4502c6962965fa58f185d707a72afc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "8c3714021359cb875206d486f25ee2c9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "87e638fbc5e15e8d93ef84462a09bcf5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "2570eaf33e6ce252fa201989b9ee6af8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "a619d972c76b44bc8d1c3dc88c8445ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "537b299919c5bd3621a1af62db0c07a1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "212fbd7b9d63586beed8d0e16701bf05"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/map_widget.dart", "hash": "2c05de4d478d7edcc2979300f039282c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "1438fbc7f0f731cf5164a211928a377e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/app/transition_type.dart", "hash": "c0814ce9812bb85ad194fe16cdb4322b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/database_mixin.dart", "hash": "8643e5c3ed4cc326261faa439ab41eaa"}, {"path": "/usr/local/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/factory.dart", "hash": "90c345ec396d7f892f524ce659aa9b5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclockadjustment.dart", "hash": "d25601f97655927dc9fd147438eacfad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/lib/src/tinyqueue.dart", "hash": "d35ff7e1ea9d0c5fb51667c8974923b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/paint.dart", "hash": "dfbfb22ff7f0f68f8c6b4a1e84303d8f"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/rider_card.dart", "hash": "249638da8124da969be8bbd628607bbf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/viewport_state.dart", "hash": "47ae3f3786ab7ecae540000a59606aa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/google_fonts.dart", "hash": "885d6001f197c05de34b17e76acc7ed4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "89862172ecfdefb923b68111e9a86fa1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/tile.dart", "hash": "adcbdd4bbb3f5b1d1fbb842f6d761299"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "c46a3b47927574d4a8ab22690e395c2e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/websocket_provider.dart", "hash": "d194740d28409fd7915e018f37ae789e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "bee2e1aabab40248f6e4b4b8bccea509"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "872e5481eca8f8f757d139b5e4988053"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/resolver.dart", "hash": "8b864131bf90f8b83b363321907aadaf"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "e0da92f4435a19c3c666cb8f88cf4502"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/map_configuration_serialization.dart", "hash": "0392b2c674cd70455895a825597c92b1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/state_viewport_extension.dart", "hash": "4791496052dc6c102ca74234fe06bfa7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "bd7e1a7092d5b455dcd4ba18801ffb5f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "91f052d253200c68fd039b858db9d7ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechobjecttokens.dart", "hash": "2b6a616f4d89d2cc1f4b1004a5e58085"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "bb40d6ae10f99afb1b498e44b75f9a3b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient.dart", "hash": "e3cf86a21b6646a68ce37d952b5ecf5c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/visibility_effect.dart", "hash": "1198e07cfca6cdefe5e54638d507f31d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "002fd240f385a66281c63dea9b31c069"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants.dart", "hash": "ff053497281f063a2820da0b9ed4d260"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants_metadata.dart", "hash": "4d74fe38db99d0b2b135f8d4f81d6721"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/advanced_file_output.dart", "hash": "c00373b698001c5ecc9d5de61e542f5a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/circle_annotation_messenger.dart", "hash": "f40dfb58cad5837166efcc1221b59b73"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/glyph_set.dart", "hash": "8a451864f1a46f19700d46fc5d4cbd39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/bitmap.dart", "hash": "fe8cecadafbbb32b56bc974dbef5ae57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/exception.dart", "hash": "a8875f2b3b371e151aab119edb22855a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitem.dart", "hash": "2ea28d523e25da87fbda7e73bc2ffedf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextpattern.dart", "hash": "6e8a57cfea32b9c9f29b229edeacbd6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_l.dart", "hash": "90a6d35e7a7db7adff31af7c8aeb6182"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/copy/io_sink.dart", "hash": "32f085d3870d940e473ec08f067bd7ab"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "d28e7661d865b8f173e8d016a845d4fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "85a9bfffa1576a9d933113d39528e24b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib/src/google_maps_flutter_android.dart", "hash": "26418dacc1d1c37dea25bdaf16575aad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/image.dart", "hash": "abebf3289b53959c8e9375645eb33659"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/fonts/.gitkeep", "hash": "68b329da9893e34099c7d8ad5cb9c940"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/base_api_service.dart", "hash": "7396a852c4fe43d4389c2a290194dfe7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "2f510aaa9b6e989e33c72b16c8b5b040"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "e74977ba262a820189b7854350bf9af4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/shimmer_effect.dart", "hash": "c9b3f620c35ff8f48a223137f4bd0b2f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataassemblyimport.dart", "hash": "bcb3a959e03b0ba17fa42d5f919b2e00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "ce40486e207d35a8f247281a34f231b0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winmm.g.dart", "hash": "b670f26b5ebe125326b4ceadf218d1fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/vector_graphics_compiler.dart", "hash": "cc23c83ee1c9e80d4a9f0c262c69f17f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "84126bd35d5680f3c48903776fb5162e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "3a11b2e3469f6d7a7d722f6df2c59dd8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "d498388a21cc769d98cf664c575d4e04"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/builder.dart", "hash": "9f3c00f460ef40c2f330a850cde3101d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_b.dart", "hash": "bf5efe9b7f7e8bdc46aa542818534985"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/source/image_source.dart", "hash": "195bf5ab7b5084a135659b4053f88974"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "248365a073cb187f63e1c3dc8ece112d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/tile_overlay.dart", "hash": "e8d02355e5669eaf544832dc9807a50a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/area.dart", "hash": "355ab573414ae66b783d6b957a032897"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/lat_lng.dart", "hash": "980b1fedb646a740e20f92b7755a150c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworkconnection.dart", "hash": "51bc9f87faab4993239b12e26047c819"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/polyline.dart", "hash": "c56d215af6e0762f8edde256145042dc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/midpoint.dart", "hash": "c657c302985230d160bd88d64e377791"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/ride_service.dart", "hash": "9aea6ab280a44df772159e2669676609"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_v.dart", "hash": "4cb87d15a1cc8c482587425775418f04"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "df738d0b6e09c730283cc61d14ce2ded"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/marker.dart", "hash": "5ab420180c2bd936600cd9c994f43b33"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "a0a1a162853c04dfcdb92b1a910488b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/sqlite_api.dart", "hash": "0ced28711559707743bc05c76130e6e5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "0373ba3e37bb0de4333d914284042952"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "42808d0d2fbb61569f4cb043ee4ed594"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/favorites_provider.dart", "hash": "5434b402676a02c74cbc7b6815124d28"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "6c5adddb22296bd8e3a6b50a3a0ffbbe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/misc/errors.dart", "hash": "8cbd679f40c3f8e0bd00dbbd6bfb8f79"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechwaveformatex.dart", "hash": "919cc78cfaa28ec6b957a771cd0765ed"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/data/repositories/ride_repository.dart", "hash": "f396bb57d6b98f9e656426ba1ec45f6b"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/theme_provider.dart", "hash": "85cdd804011946dac7e4130ea50ba891"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "438d55cb1016f68c4db0da87b19ac82f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/sweepline_intersections.dart", "hash": "47341687b4477cd2fd10f0a977c8f53f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "86b06851f3ff5ee17bb39fd0d241cbb9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "28dc34f687478a2897fafbaafa053b92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/functions.dart", "hash": "a3aa36a805436731699f39e6bf524087"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "eded57cbe50a1ee3c706e7f683e68222"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellcheckerfactory.dart", "hash": "419b1d6dad30c44e241a804453f78d56"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/jirani_logo.svg", "hash": "23af77845f972812e3045c32bbde7405"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "2c45e8b9705774232ea4fe1cf5c47b30"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/mapbox_maps_platform.dart", "hash": "********************************"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/location_selection_screen.dart", "hash": "d06425c49704ae8ee1a0209c3277a743"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "9f13e4907afe8f2f9ed0576085d8d0c6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/color-3.0.0/LICENSE", "hash": "2104ff09a65bbd4e64f731e95ff15d0b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "75c38766ddb6a4505dc9271c6a9fec49"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactory.dart", "hash": "7068099dc46731641110788c3b3e1bdc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/heatmap.dart", "hash": "5a315ac9d2a60b5e5dd4487decf1d5cc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/bthprops.g.dart", "hash": "791b58d08f6e26165658bbd5ad0c5b2e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "7e3157d6dcf4be11dd91edc6077e6401"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/collection.dart", "hash": "476383869aff7b87579a7753e47722d7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/sqflite.dart", "hash": "b78501a68c4c0d06da54deaa232576a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "54a357c7c827b2616fd5e9ff6fccbfd7"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/custom_button.dart", "hash": "a54399b78bfdab4635a9254b5a8fee7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "fe0a1ebd99d59b4024033158607723bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "153449b4c65c20e74489d7853e4ee4ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechvoice.dart", "hash": "e4db97d8d7acb9a9585f38b0df246277"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "54d59a18ed489222e79e19304ca89cc9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "9559118068f6ba981881a07626ef2d3f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta_meta.dart", "hash": "8b83501f9451392bceda63c9281db57d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "449d44061d435c8bbc5366d6eacf065a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "86e192e4762ca754c4af48f3fea22501"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/subscription_stream.dart", "hash": "2a0b9a600ec09eae15ecb49408c970fe"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "9568f208277ebd33bf390ffdee55c753"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange2.dart", "hash": "afc3af0d3aaf8d64a82177a094097ee9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/adapters.dart", "hash": "fba8114f858c7bb3b433350fa69476da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemimagefactory.dart", "hash": "a966fe9730c6e36f9a0123b9eb1ae505"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumnetworks.dart", "hash": "c07567abbc3cd64d4f3175c3e142da55"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "e31765e74f6120b5bfdf059885643943"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/states/style_default_viewport_state.dart", "hash": "ea06cbef559296ca949daad033199ce6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "87fd57b3136ca33a1e97c5524b74e112"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_a.dart", "hash": "6f31150716f793ef18c1216f785c7e6e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/exception.dart", "hash": "5ee7523f60ca432b7896fbc746ea64b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/theme.dart", "hash": "88718f823c0de3f8398075d24b150ecf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/permute.dart", "hash": "a857d1061019a3f9214e8735890c300c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf8.dart", "hash": "3b21907d68a2e99afa8e4103f6a72f78"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "819e1a61d059020752de65cd6e5b8466"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effect_list.dart", "hash": "e7da1eb4d5cb971f14a5cc5d487e80a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/viewport/transitions/easing_viewport_transition.dart", "hash": "9347ad188e5c29f779bd797846dbe7e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/case_insensitive_map.dart", "hash": "b7daa46d4dace857514806769032077d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/propsys.g.dart", "hash": "769b47b5febf91e7831fd0040b4d3ed0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/geocoding_platform_interface.dart", "hash": "81079bb62160785044af3810b63a15b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/usr/local/flutter/packages/flutter/lib/cupertino.dart", "hash": "e093bedc58f3a92cb4ab66556b4ea9c8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "0713268d2f4a4fe1a926f69964bcd39a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/rider_service.dart", "hash": "106e4b0b5e716121e4c06df20e2a2a48"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ierrorinfo.dart", "hash": "aeb565e28b1e55ec3794a6b88d975aa5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isupporterrorinfo.dart", "hash": "0318359df96d8b438340156129fd1c68"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "aa6152a8dc858cd16cf240ebfa31d605"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/google_maps_flutter_ios.dart", "hash": "9574e53409e80d75909c679e35b6c2be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "3a8ae5977fc932c86b4b61e92db7a275"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/string.dart", "hash": "c59198554d2a2402363b7e7671fded95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/definition.dart", "hash": "1a391f72b5be0846aec18bcf1609b962"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_error_name.dart", "hash": "7398500b1824f6043f23e208cd993866"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "c8bb12d41ce48bb27184ddd257d2859b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/prop.dart", "hash": "b8c6b8c9dc856f26b3eba73cd68dd20e"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/.gitkeep", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/utils/error_handler.dart", "hash": "339f0a5f9a79fc5be2f0411389202b61"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/combase.dart", "hash": "10ffd776cef80532a293c5673825e655"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "2a64735d53a1dd225670c23206f09e60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_point_on_line.dart", "hash": "8d70acfbe38046d331a83b83333f3eab"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "8965e1b5627c77d3978ae3d08e32f982"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "6b06971a44e8eed786f2f06388d0580d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/value_adapter.dart", "hash": "9bc97679b42c8f9d40f4404ed4c09c7a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "2854423575c903818dc71d712b749e71"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "bee3197c0ad106b501694062a10e457a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/xinput1_4.g.dart", "hash": "110291e1a5dee5de6d06425145c9f53c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "11a634821b3bce05dac94f3dabe52a75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumspellingerror.dart", "hash": "c2b3370ba518e83a18e0be246f0e2ed4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "0759c0203bc8cd82a7b99d5683673a32"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "3f1936af23dbdc313352c2213f4c2dfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "ec8275100b9b1a1d880b8ddfe8100c9f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/polyline.dart", "hash": "363abbdec3328e713b9e3a9fab9d73e8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "b1ebe2e5cdcee36b19b606b37a12cc38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation6.dart", "hash": "3a7c0d6ff07fca442df7724d853dfbe0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "hash": "3d18e1306d78e114f98c9dc311fbf158"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/apple-touch-icon.png", "hash": "2802f86d4bfb74fc882fbb4525fea11e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/move_effect.dart", "hash": "a9e1c7b28293ccedf0594edcf59ec4b9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "ef07fd7a67d6cd5ac37c5c20863af323"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/repository_providers.dart", "hash": "799e581c9c4a3062cafd2022ac5afe93"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "f3a8fcfcd79b619cc886547b8ff9b25c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement4.dart", "hash": "a212841ba1b80a845ce3756241645d58"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/range.dart", "hash": "065ae19274f3691df673e4d2a11f5d52"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/logger/sqflite_logger.dart", "hash": "67d4b61f3e0a5ab742adbe76b7ec14a8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationwindowpattern.dart", "hash": "0d790476d9ddbae00b9e3f0076902498"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "2905f1d86a6968256c390e989a477413"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/pattern_item.dart", "hash": "a9c5607b57384fbd81c5ae26b312b50f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "6d4775307a2bf338997772562d4467cd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/idesktopwallpaper.dart", "hash": "74319ce8573194302792ea41f665838b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sqflite_android.dart", "hash": "ec562f20505ab846c07aae091a58b990"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestapplication.dart", "hash": "3dc4006aab4c069db52d46f59e8262fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemclassobject.dart", "hash": "fa0457adc89723d08bb20eddf3e89555"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "994baa57aed6041f4f36e7119938923a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "81f395ba7a262f5e3f75cc8ce6580d0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/polygon_smooth.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_equality-0.1.0/LICENSE", "hash": "8123bbb1281fe63a86386b86808d1304"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "46ffe5265ab96981a4304879f9999d5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a6c5b8639baab209a7d53dc7e0d1cd1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/segment.dart", "hash": "9762020a466340c42855623eebd25594"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "d2fafa799f53bac4cb844e60b40a10f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/stream_channel.dart", "hash": "26e6f44eaf764daf10da687139fd6396"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "f203c0a13342dd79b78b56ff66fe665b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/router.dart", "hash": "586f82935199530ba7ff15a9a7cbe00c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ab91622a9d9c558bb65f0f06b904d873"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "1325fce32c39a3792e3eeab612f942f1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "f6a7d78c53bba84751bcdff0a232e6a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation3.dart", "hash": "c5d7abe9da153df1f3d9d7754b91c0fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib/src/google_map_inspector_android.dart", "hash": "a7bd3e0a0c476b10193f63090f146a3f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialog2.dart", "hash": "f45b881803064da6852bd34e8ef7951c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "9583f92189dde339b1884f57e7b2f9b0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/decoder.dart", "hash": "dbff400b121e6f844298946531d490a3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "8635fbec89c2cc03404a2a3233d31bbc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllink.dart", "hash": "8b90b8fa4eae6234d9cdad3987f9faf3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "c0d8bc79666e26dcbc4f56428478d7b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/authentication_challenge.dart", "hash": "7bfefcc0929d945fa61bb7870de1f659"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/enhanced_location_selector.dart", "hash": "2136303f6b689ce5b08366ecc0994afc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-5.0.0/LICENSE", "hash": "9ff9055bd8ea99f417a45dd4a0a4c5b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iunknown.dart", "hash": "4c90e2a275589188bd8714dd9cc5650a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "327867a2784fcc92c5a1737cee7a3197"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/gdi32.g.dart", "hash": "3c738a2ffff7c2ec664bdd76408deff9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/mixin/factory.dart", "hash": "5b48bba3dfcba0ea1ad63f36aa8bf66c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "hash": "75290287531fff47b4eab2f25f050d57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/explode.dart", "hash": "2a38c18afa474bc5d1e706c3d97a0f18"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "d9e98b5a81cf86f8c1f595ff861eebe3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "d9b79784fbfdc586f6b715fb11537767"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "hash": "1c52a06a48033bea782314ca692e09cd"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "3b5bc5c0df6b6e1abb26877f612b2242"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/usr/local/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "cbe9b4803b3db39b5367784e37dbe387"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_signal.dart", "hash": "8596b58c127792783625b4b22a4d023c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "40c9adb59d6f9b10d60bbebbd42d57d8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/extensions.dart", "hash": "ad9cf7358ed19232bfc9b83e3f13a69e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "27fc926793fa8892a2acc5b7ebde35d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/interactive_features/standard_poi.dart", "hash": "cd0cefcf674d69a4d8e6a4cbf7ecfec6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "366aa23421c294b9ad3fa22271afbdb3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "c0fe6462e3a08d6d6afbf4f66130d494"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/pages/custom_transition_page.dart", "hash": "bd81c6cc5eb829742ceb3a955cd852d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/propertykey.dart", "hash": "6b00c4c5c720216a682e1018fb591aa3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "6c31b298eba9c0df399049d9072d5ede"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/category.dart", "hash": "804187ee6577f864349c3c46edb44f5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/listen_effect.dart", "hash": "222071fadae509fb3c28ca2e5fff8f06"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "f9d2ca632cbf0232de1a3b8826d4286f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "bd21408997d44d0bd83cf6d38bf3d2a2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "b7a56c40e458c896ddfcb63f1b7aad62"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "f90b22ce5704e97441e7e2265d0119e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/scarddlg.g.dart", "hash": "a40d6cf5dd5de2536012a2ab2690e67e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/map_widget_configuration.dart", "hash": "3b2aefbe75d694af4d40cc860af89919"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "facf2204744818c1a3a587be1d7b7645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "8f094e8fb77987b0922b558b2fd22102"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/line_slice.dart", "hash": "cdf88d8dbd39da885f60c89c63f95bf5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "70897dd932d388476b6a9a96fe349e25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/polygon.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/method_channel/method_channel_google_maps_flutter.dart", "hash": "e8ffa65f5f56a798da05e4ebe80f00b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/app.dart", "hash": "4a7939e729d46f63553f500041dc7672"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeventsource.dart", "hash": "761edf39926ba43b2d6c95d677bad6ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "fdbf119306639c731a4d90a5f56cee7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/polygon_smooth.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellservice.dart", "hash": "b7690366684d9173683d36992173f7a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/clean_coords.dart", "hash": "2f33a8a1ec144b8f1df97cb02b7322e9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "hash": "25c44b3908d2602e0df540ca5b17da27"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/filters/production_filter.dart", "hash": "5a28430581a3560d56d0014e44b89685"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/lib/src/messages.g.dart", "hash": "f1c7d23cd6db9504510e67e2957b4aef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "9ca011fc6acdcd04949fc9c6ec849043"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_q.dart", "hash": "e126494233cc791fd4f817e26948cb99"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "3f80a1a01d62cd07e1ae5d34f9547b69"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/camera.dart", "hash": "bd516d1b7ac50694e0595178d094b2ee"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "b5dae33166e22586a2d2fd15b559099b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "234f5667a312bcca30a59e788fe46424"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/utils.dart", "hash": "daed61faa3073bce4e45684eac245fa2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/src/_debug_io.dart", "hash": "118b6a62408f796e238c61d271e5146f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/parser.dart", "hash": "7f364a339192e22069e77ad3ff05d2cc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "7aa7a3f6fe7120292c1dd2eeb95eda39"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "fc74c3e85989d324a76e1a8a2d3f6dea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "e901ff12abcd3dd4ca3b79cc732d1b1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "7b53b9344345e99b1ec1c1e6247b0f78"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/unmodifiable_wrappers.dart", "hash": "4be4077b482b12a5ee202d859e8286df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "6656ba0c69fefef80b8cae101896c029"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "0491e1cca60da329c2e03c48abde07c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/snapshotter/snapshotter_messenger.dart", "hash": "558ee1c9f059e999629dfd46b3250054"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "b066cb536dada4801466a198ed505a8d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "e470881e8e68d9842928c6fa1a889596"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/details_screen.dart", "hash": "ed688b68dcb5c0c57f7599a052ff309c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "8a899256e5ac320579b269ee9b2567a8"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/jirani_logo.png", "hash": "be4f8471fd9a5d2f57bd6850eeb9f19e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumidlist.dart", "hash": "043bb1fa01132048a01458c6977636f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "44fe04a4d23a1e0df1470c5c0eacfddf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/eager_span_scanner.dart", "hash": "b71ae933bdc407aa375dba5f378198a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "67795683a1a7b3dac0854a1e5da146ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "hash": "88d5feb6f0a1ddf0cafe75a071bbcab2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "38e1d0509dc1ed42b630c2604c905593"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/choice.dart", "hash": "8c5e16920bcaf0c7e7b82d0d0871ee56"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-5.0.0/lib/src/types.dart", "hash": "26aa4b9fae3adef49aa295f8a6681056"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/io.dart", "hash": "1ad7532928963229cf586db9dcaef3ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/single_subscription_transformer.dart", "hash": "bb644b5f4cdf7ece840f06a3017bfe54"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_crosses.dart", "hash": "b9f46f2ce41c0703dcc974fc48694712"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/mock_data_service.dart", "hash": "af5aae6d74f1adbf9c84fd449e45d6f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/errors/errors.dart", "hash": "bc5858b686246ecfb23a51fad627b335"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "da6fd295116b361d1a0258580d3db629"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/src/default_theme.dart", "hash": "b310006c8257038eceb57e051139592b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/winmd_constants.dart", "hash": "0cfcbe0ce66e9725eacd8c5fbc6f604a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/cluster_manager.dart", "hash": "fe6cae604866a18501e51c7b676e8841"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/model_layer.dart", "hash": "39105a689396e1acf87a9a346c73501a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/src/inkwell_shader.dart", "hash": "e9efc19a02ae3195574f6fa743603b00"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/tile_provider.dart", "hash": "4297dd9c5fe4308f63ed78fd13ad9795"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/types.dart", "hash": "3098c320bfb4fb78df8b7b16aee58eef"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "f5b4267f1c1f72ab634a2be53517d1a1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "01f79859a59693addf84974953e7f9c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/LICENSE", "hash": "5b388500640099f7c700bff344f7bfa0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_x.dart", "hash": "8c0609f71af975bf4d5197e6e0352a40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement9.dart", "hash": "7339ec709c898b8e442a3a02e63f3e6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/performace_statistics.dart", "hash": "0437a863c3ff4189a7e1dcb402dfb39f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/expression.dart", "hash": "1eea4f679ac434a2b413372c524c8d00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib/src/serialization.dart", "hash": "758b51cbaf3686abb5bdac528fdae475"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_equality-0.1.0/lib/turf_equality.dart", "hash": "5f9f1d388489a1f720a6c7cfd2dbffe7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "6147de3bb8f9f335022d631c67c92536"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "d91b7655dec73b3db3a82040be7410f4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "e08b76d444c9753d483bf6975ce61fe3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationpropertycondition.dart", "hash": "82e0e5b8ffeefc064a87f7990e0585b0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "4af4d6cbc2123755fc589f044bac8d6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/powrprof.g.dart", "hash": "2a4b4b7e76f1c79a762ce636f6b730db"}, {"path": "/usr/local/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "hash": "0902c41eed709a7841f11130fac2a593"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/data/food_data.dart", "hash": "6cea342e508544cf95c01db394b5410b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "540497224c553a9b08b20397bd78ef69"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/trace.dart", "hash": "677d8642864682752cf5fa1727f11e45"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "c04dc7f29cc9a9696d55c176099bcce6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "493bf1de83d600106fe27cb741bd2012"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object_manager.dart", "hash": "69c08243f2f74c58d6ad38b17bb5cb9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/builder.dart", "hash": "7343264717127ebb7016260e9dc45319"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/callbacks.dart", "hash": "86781b32fca02e40f75c1b196e60fe4b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/vector_instructions.dart", "hash": "4a85886272faf2318070437f1c4be381"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/marker_updates.dart", "hash": "4eb47b4043ba70283d3d0b16b24746ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/adapters/value_notifier_adapter.dart", "hash": "a68ab99598e72a476132676fc97db138"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button.dart", "hash": "75c340e47044712f389010dc4a220a3f"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/connectivity_provider.dart", "hash": "41cb0253d41df8b806ed496ce8c8b319"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "fa4de83c5c418c49affda2b164993f99"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "347ca56667b68d9d66174f8200b4505e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellchecker.dart", "hash": "b868a7ab9e1be413c489dc9958bf907b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "ce2f733c9ef461c84bb85a93bae9e7a9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "eca62c60db96d71f3cae9b506875c03a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "30a8a2a67dcb5b0a7969ce9bf1509129"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/joint_type.dart", "hash": "4c74f3286505b2f61bc305bb025551b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "f71a5e0c2e702bd1f70b7f60ac19eec3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/maps_object_updates.dart", "hash": "66f9c57e1bbecf1c3d8b67a3a4d1fb71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationstylespattern.dart", "hash": "7326647ec0ab13c912ff9965ccfb4081"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "a22042c948166ba677133268fafc4b41"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "3c7543874ccaad16712efd4e0249db70"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/shader_effect.dart", "hash": "56b284328228e6d3eabf49bed958999e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_c.dart", "hash": "7f1486a2bf169b977f3be1524f930a6e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/warn.dart", "hash": "474476ae5db616e0ecf5e488efa202f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_e.dart", "hash": "012c3b670fb153803ce635838e1fa9ae"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "51f2a9874a4ed255b69aeb0c3feb1903"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/rhumb_distance.dart", "hash": "c87f42154814b039b46e391bdeef905c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "074b866f17aee09c76583b075e83cb8c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "1b935c75a1104936d71c6a3c0346c7a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_parallel.dart", "hash": "a162cff41ce206bac81a3cdb800d10ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "7e69fcdf387be2ef9513d34ba4bbbbdb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/iphlpapi.g.dart", "hash": "2426e2644b69a745c9d477194b9b572e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/wlanapi.g.dart", "hash": "29247603a535c298681d43412512fd53"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "67be4bdf31d93f8a5e654ec21d96ed5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "872203d79024fa48a492a03eb0708499"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemcontext.dart", "hash": "659397ba2b8ba2809c7855a21f2f60b2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "f37381ef6280c57820b3aa6cbccb1f31"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/.gitkeep", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "ad6bf1d7b3079f5be69fb40ada4fc145"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/fonts/.gitkeep", "hash": "68b329da9893e34099c7d8ad5cb9c940"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/netapi32.g.dart", "hash": "1c6d490a13baec49a9edb03d5fb8a00e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/destination.dart", "hash": "0b81c9edecf82db15ad224db4cd44ea2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/src/animated_sampler.dart", "hash": "6fbb6ca5fe49e7940262f00aca378c21"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "0c9067d0b8afe6ac1c8b326551773709"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ff44ff979a414bb8029a4e8b205d2479"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/length.dart", "hash": "5a3aad5a949d1ab01b55aab6306cb157"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "aca21d4a3c0075a4d0109e79e87d7f21"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "b064d05257802d1c2555867b7817d23b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "hash": "1c71712af9ddaeb93ab542740d6235fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_group.dart", "hash": "e6e960458fc831c2981997244673c139"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/layer/line_layer.dart", "hash": "0db6e96626ddfb9353aafa24c10f4a07"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "228535e61392d353222c93613f49eb9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/bbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/batch.dart", "hash": "34c2a18e24c9d0bc442ae2559a4d7479"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "be262921a4471e495202bfe1871b610e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "f1cfa3a69ee743157de8de4ccdf51b58"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/LICENSE", "hash": "a02789da8b51e7b039db4810ec3a7d03"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "7397ee35fbfd4feddf487df2023f0ffa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "1c43aa902b27d1a8936c77dcf231953b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement8.dart", "hash": "2598a130fc6437cc87f8efb150561b60"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "4eb5a88d2cb5e6153b082794c6d39409"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionpattern.dart", "hash": "89afb95565b4d1eca335d4b9b4790212"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "17f28ff1f00166c9ef3955185daf21e0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemlocator.dart", "hash": "0183b13f6e13fe4c255b09236e142882"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatatables.dart", "hash": "fbce92f0e78e457538005bcb0b9a79f6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "55ae9ec969fc6f2f12ba4b96872fc552"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "24cdd2cb365ef36394210a26c9fb1dda"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_y.dart", "hash": "853e7e8b3898f3c0055ae0ae1630e229"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "f0561e97f70c3b1cf5f113f21d51fa39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "11c860591b76d1994f12042408831c94"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/bstr.dart", "hash": "af04c2a11aa95d2e4b86600b33d0957c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/itypeinfo.dart", "hash": "2fe7a01e6cccd3fc371fd2d730935afe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationnotcondition.dart", "hash": "6aa37695d4ecfd1cd9514e2758da9f5c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/enhanced_map_view.dart", "hash": "52f148c9b872b50340f954f01518d93c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/LICENSE", "hash": "3af9ea7b1caf831ff725e6d7df02e4be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/clipping_optimizer.dart", "hash": "c77ae533b618bb24965504354af17bb3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "96bce067da5564de27f19f050360860b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "hash": "ce30848ef1f94b243d6094ee0d740597"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/auth/become_provider_screen.dart", "hash": "8685bfbcb83bb61cb676c2822fb011ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/scan.dart", "hash": "9ce6595770687511a1c77ace6f55bddc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_contains.dart", "hash": "a0996fd0afe58c7b6d00c69993d685bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "34ec8e649166b192586b754ce67094da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/guarantee_channel.dart", "hash": "f29acb052118f97a2f277c75f1f4ed66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/ground_overlay_updates.dart", "hash": "646cf56f9f2a0995871d49133dab7efb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/websocket_service.dart", "hash": "a4efe4e02b68cd8dfe8415f39b11e10c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/events.dart", "hash": "523961749d9447c9feac2b3dcb65d1ac"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/card.dart", "hash": "4d2acf9063a0c90341c7af78e293a937"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a8abf3052eb97ac137ee94d906fcbd08"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/canonicalized_map.dart", "hash": "889042dc1cc5b1f4e4e1572270920f54"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "hash": "f381ed91de52f40a7dff4d2f0f3f6d4c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf_equality-0.1.0/lib/src/round.dart", "hash": "8b144b38ce0d428697145d94ae0d1874"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "b2a01f75c9e7125dda3e9461005b5765"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/geocoding_platform_interface.dart", "hash": "1fdf07321ae164dc5820e81264e967cf"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/models/food_item.dart", "hash": "7eb42a9df47a4a1e36143c66b5296d88"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/polyline_updates.dart", "hash": "8edfa5faf19ec8b75ed313d7af3444c1"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/splash_screen.dart", "hash": "decc217769e5765c9f21b0c316667330"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "7776c5eaa171bb5e03d1945d85354f49"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/polygon_updates.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "374f899d15352be34ce61fd5243bed08"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "b794bf7c553a2a0acab8dbfef6b0af0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon.ico", "hash": "9bfd6c263fa57a55d6efb17770b64aa0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "0f366f928cc93bceb62726d94a7bc889"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/category_provider.dart", "hash": "d11093401492cf08b2c0d13899dfa1ca"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "a1497040765f0f7199f990aa00af5310"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "faf51c4fe1dc7af7fabc7c78a960305c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtogglepattern.dart", "hash": "b3d8ffb1e79fe86169ef197e01c7c79c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "62877c9dc6386e2c18c23df696e12753"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/rider_details_screen.dart", "hash": "c2204cdcbd79f07d6da562ef42243e0a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/action/where.dart", "hash": "4681dd3127db5960e33f162c004bf5d8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "c03374f2c27987a9929d2128490ae921"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "2464c23232ce73eb96d3fba785a62215"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/lib/src/utils.dart", "hash": "120d43e3e098902711591fbe7ef5319e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "0a1a80151674cfd91279677d9f016bf2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/widgets/rider_list_horizontal.dart", "hash": "80fe6c09038c4f36ed4b9b7d1f8d97b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/go_router.dart", "hash": "94124aa8c115b3bc8553ba80c419ceeb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "e3e4ea8878b2a7a9cc04137081ae1617"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "ada618567778b8fea2e8b455e9100a07"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/jirani_logo.svg", "hash": "23af77845f972812e3045c32bbde7405"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "66650a747992c52961dad0ac63628efe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "186ae135e697551ae5882b55892bbb61"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllinkdual.dart", "hash": "2e8ac7faef1638b9d8022b3da82c3588"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "2dd7e3b55dc8a0ddfeee22f0119b0082"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_read_buffer.dart", "hash": "fd517e61edeaf09f9e4cf9e9ba8af13c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/animations/animated_scale_button.dart", "hash": "24cd58c0185e072b93930e6373aa8073"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/shlwapi.g.dart", "hash": "bd016bc06a43b71c304daef7333df5cf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/version.g.dart", "hash": "b967c8105d10206324262df9fb1a662b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/core.dart", "hash": "2edce71d13a627858ee90577d3eae6f0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "1c0e59efab3b5ae568836b9fa5a3675d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/extensions/num_duration_extensions.dart", "hash": "5d4ad6ba64e70985334d2403cee52bc1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "hash": "75ba7e8a7322214ca6e449d0be23e2ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/LICENSE", "hash": "9ff9055bd8ea99f417a45dd4a0a4c5b3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_value.dart", "hash": "21beb4ff2c06d1edc806270e0bfac51f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "3907ade9ce8b9e16265c3ebdff6cc132"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "dfe858a6ed0de97f6c6de45176f474d4"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/search.dart", "hash": "51d9fc6dac581a413a064529ebca2aeb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/masking_optimizer.dart", "hash": "0a91e9bccfc0453430b69cfa8ff8caf9"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "c9391709a58b30c3f3812cc2b5e249d4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_j.dart", "hash": "998746037e3416b31d33881bf69a4148"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid.dart", "hash": "5261078afe15bcdc637478bb6d7f7e21"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/profile_screen.dart", "hash": "406562d36bab3720de663adc1a85ae91"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon.ico", "hash": "9bfd6c263fa57a55d6efb17770b64aa0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/booleans/boolean_intersects.dart", "hash": "ccd71b9e7d548044eec9b64719684efc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "04d4cbe0a7a40a31323cd39f2bb8d585"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/6f00e7a33c5475ad842f3d6cc1a04a1a/native_assets.yaml", "hash": "e7fd2fda36f01436b831ca47fe61fec3"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "dee4f18e2804e238c57a305ccd28eb85"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/usr/local/flutter/packages/flutter/lib/material.dart", "hash": "f3e5196336dc300b73d52ee30ae05c33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechobjecttoken.dart", "hash": "47cee6326ea5f9f09e1247e2930199e2"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/icons/.gitkeep", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/winrt_helpers.dart", "hash": "e2f61b143b6eaca3f6291b32388812df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumstring.dart", "hash": "e7c7233769f55e718ce22082f70db721"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib/src/messages.g.dart", "hash": "e32e89828058bc659cc9d901b326ef99"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "b9428aa8930bee03e47536cc9352bbc1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/truncate.dart", "hash": "0ef7ddcb2e01c58edfb67aa2fc0cd4eb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader4.dart", "hash": "c475dfaacb936bfc5773b55b5b7db7a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/callback_effect.dart", "hash": "7cf8d9762d1f2226ea94a1c8ce1f7265"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "4744aaec510cd9c8b07ca129362b8fb9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/food_home_screen.dart", "hash": "c9ab28f8b36ba63077ea2fef7d2783f0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_peer.dart", "hash": "681b70272ec68e757f2394c9e7fa9398"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "ac5fe86ab9ecbd33f878f0a580f3bfa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/method_channel/serialization.dart", "hash": "230370950ca07a138928c73df4c0ad52"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/cancelable_operation.dart", "hash": "fc9d1c858bc266cef12e9f365538835e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "8d4df3ef11f873038812b16364638706"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienummoniker.dart", "hash": "d80a4e0d1a5fe4aba72f8df70e8b660d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/events/map_event.dart", "hash": "c9ab535a7eafb8e75a6d6f78508fc7df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersistfile.dart", "hash": "d27d71d2351cdb9c560055671b5ad215"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "2f2ab157e82e8fdf20b247a00e5bde3c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "b2cf47ccd5a6cf4843108c3a9f821c55"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imodalwindow.dart", "hash": "7837848fa5cbb9801cfadd3856d0479e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "43a371c8c7e417fb0a445ee20360d434"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "909cb251e671fa02581480629642a663"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0/lib/geocoding.dart", "hash": "b8fbb5ba1698a9c959e21c22f97c479e"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/explore_screen.dart", "hash": "0e0f9eb0f4c8e055a3270f6caba4ac4a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/centroid.dart", "hash": "a5cb028ad59fbea2e69da398dc51743c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/src/sink_completer.dart", "hash": "84460105d53071b467ff0064e441b9c8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfactory.dart", "hash": "aa34ef78c82b66e4c309bd5f4973e3c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "ceca8c46e07b211bd755e480b1bd6b32"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "hash": "0f0fc7bc5c7c2c1581ed2ed245baf136"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/interactive_features/standard_buildings.dart", "hash": "001ff5c1c827eb62c3cd18f2edd9e3eb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "0cae216bb3fa19f2f716c7f416500acc"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "c4119b97c9830ac751fca7f2c7989f6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_z.dart", "hash": "991a163a470f64b0222de6290e39d538"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/main.dart", "hash": "b1498e4fca59988a1bb05113c1530203"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/mixin/import_mixin.dart", "hash": "cd0157df37359679426daecbd76dfcc5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemobjectaccess.dart", "hash": "cb5493b3fb9ca309e2cae9a641029cd0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/extensions/offset_copy_with_extensions.dart", "hash": "59b94bd26c48f22af40881f8128c41c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "03b0f3319b7390e1f3928ad3e3e544a8"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "bed0fb96275252e2297091fd19c45ee7"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "c5a71d0fdc87177c4ceabc19fbf2d251"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "fd48427e65c5910cbba1fc3e4e57cfcb"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "3ec0013bd7ba2e0f89cb963f867f0d96"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/line_intersect.dart", "hash": "d614129e240a930d270620f496401982"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "1d2e0b9bdfb7d8463b27b487bf96ad46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/auth_provider.dart", "hash": "d84f9bca6e7a3471fdc71438a5e73d9c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/functions.dart", "hash": "a12fc767bd933ecc3bbdd69f597ed3cf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "e50e0016353ca39f15e2e40e2221095e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "f179cf16ea560111839fc980420e3b18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/saturate_effect.dart", "hash": "eca10930a22c21b03e7217321ca2caba"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/README.md", "hash": "542d7bc57b0ec649ea40a3893fece275"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/comparators.dart", "hash": "d1410f48ac374235aaad55cba40bc4be"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "268c67f634fffcd852c6fc020d6ed0fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/auth_service.dart", "hash": "daf9d743b698c12e75c29bb11eb8aa40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "098ef2cc21af375e75e3fa80f2c8f12f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/style/interactive_features/interactive_features.dart", "hash": "2304110dc68782f2cfd3d34afccd903a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/dispatcher.dart", "hash": "44c4e1de5f30ee41e94e0017dbd5f035"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "fc064178d31756adc0c680657e2a10d6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader3.dart", "hash": "d71f66fa79f435e0e9b2a8152443a331"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "d97019cfa3be6371779fc0e65f2bc118"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/LICENSE", "hash": "cf96fa0d649f7c7b16616d95e7880a73"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/geometry/path.dart", "hash": "6aad1022a507304fcaf64ce514638a17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/bbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/services/location_service.dart", "hash": "cccce9fd0a01821760044f35f788694c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ivirtualdesktopmanager.dart", "hash": "83c5918696d44ca1be713318a4f5a6db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/truncate.dart", "hash": "4a5e774cd02824fd29492aac66f7f40d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "2c0bb1b05272ab6b5f631f34c4068679"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/icons/README.md", "hash": "542d7bc57b0ec649ea40a3893fece275"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/src/svg/color_mapper.dart", "hash": "5baf64b18f36d2e7620e01237c625a19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/utils/maps_object.dart", "hash": "f572011c4cc65498fc878731ddb338b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/lib/src/delegate.dart", "hash": "99eea31c698ade1eba0c70780f4c3c5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "e7651e730f1ce3e0a0b87ac950fcce68"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "831f900bdcad04be13af3338451e99ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8831386d40ad4cf7660a3266307625e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestproperties.dart", "hash": "3ec463d588e64344f9c833041d4c2e74"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "8aa656cf88546009f56e54df825c9915"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/enums.dart", "hash": "a71d2292a5f598a6eea9a8ce5f3c5783"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "02eac755aab5bcb73ac9a278a117ca1c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/src/path_segment_type.dart", "hash": "b46578a0f8f94ea4767f634b5235a54e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "da9ecd9bf1968692f391966d2c3c193e"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/apple-touch-icon.png", "hash": "2802f86d4bfb74fc882fbb4525fea11e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/mapbox_map.dart", "hash": "********************************"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "31f32173a8983ae7bddd822a3e0e48d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/delegating_stream_channel.dart", "hash": "d731e1b690975788d014e6df127b2a9a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/screens/boda_boda/boda_boda_screen.dart", "hash": "7e45a86149e1f377e5352e02803b5e7b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib/src/effects/then_effect.dart", "hash": "6f731bf22ec9178d15aabbcf3191af26"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "d1888a7dc676f48f137edbea755dd324"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "3fa4c89a1c19c846cce6950ff665c20a"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/providers/service_provider.dart", "hash": "6d6bdb9b46997f127de5f53383da6f7c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "2f92c28411483032fb7c0a851ebbbb5a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "d10f9a1cfa41452535b1fa21db8e1c8d"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/lib/widgets/auth/social_button.dart", "hash": "689c219acc47716a4ac256ff478eae9f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid.dart", "hash": "49d6d829ae481b2570a290401389d149"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/nearest_point_on_line.dart", "hash": "7ae2141d575787ac9013df1ad483640c"}, {"path": "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/web-app-manifest-192x192.png", "hash": "38bda9a7fef1121ac8cc3a8ae9fd27e2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/map_objects.dart", "hash": "2aa0272f8c4c9cc15ed4aa36b49f7181"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "bb393d088799db4aa3e456bd768f1687"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/src/stream_channel_controller.dart", "hash": "50298dec288c3e23499a59305365a9ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/polygon_to_line.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "8effe6176ace6ada9ad1db0370cf2e78"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "65b13c835848186d2090e77073d3c7ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/src/frame.dart", "hash": "75a750ac4a72e0cbc0915e941a5999b6"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "bd2a37206b73cbcb99406f0b1ac68a09"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib/src/types/tile_overlay.dart", "hash": "ad78654699d9a9692b5dc86353148343"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_r.dart", "hash": "c03845abf8fa02fedbc602853685d92a"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "c8266c3435b50929eb834df245aa2544"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "8288239ccc449f5dec9f381298c92c1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange.dart", "hash": "8f76417391b910fe0956d6404b59e144"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/lib/src/meta/coord.dart", "hash": "2b6945d86c57419f725fd2670ae2c197"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/lib/src/pigeons/point_annotation_messenger.dart", "hash": "1e67a51b1129202e636ec437a523019c"}, {"path": "/usr/local/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "90c8a81d181140ffdcdb8601cdf56207"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/LICENSE", "hash": "2890304873073d8f3814d3d6301b8de8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspectable.dart", "hash": "a8d03ee07caa5c7bca8609694786bbf0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}]}