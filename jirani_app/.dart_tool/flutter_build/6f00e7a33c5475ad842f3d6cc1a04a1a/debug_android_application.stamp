{"inputs": ["/home/<USER>/CODES/jirani-app/jirani_app/.dart_tool/flutter_build/6f00e7a33c5475ad842f3d6cc1a04a1a/app.dill", "/usr/local/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/usr/local/flutter/bin/internal/engine.version", "/usr/local/flutter/bin/internal/engine.version", "/usr/local/flutter/bin/internal/engine.version", "/usr/local/flutter/bin/internal/engine.version", "/home/<USER>/CODES/jirani-app/jirani_app/pubspec.yaml", "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/jirani_logo.png", "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/assets/images/jirani_logo.svg", "/home/<USER>/CODES/jirani-app/jirani_app/assets/icons/README.md", "/home/<USER>/CODES/jirani-app/jirani_app/assets/icons/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/assets/fonts/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon.svg", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon.ico", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/apple-touch-icon.png", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/site.webmanifest", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/favicon-96x96.png", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/web-app-manifest-192x192.png", "/home/<USER>/CODES/jirani-app/jirani_app/assets/jirani/favicon/web-app-manifest-512x512.png", "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/usr/local/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/usr/local/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/home/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/benchmark-0.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/color-3.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/dart_sort_queue-0.0.2+3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.15/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_polyline_points-2.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/geotypes-0.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/lints-5.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/location_platform_interface-5.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_search-4.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/rbush-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/sweepline_intersections-0.0.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/turf-0.0.10/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/turf_equality-0.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/turf_pip-0.0.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/usr/local/flutter/bin/cache/pkg/sky_engine/LICENSE", "/usr/local/flutter/packages/flutter/LICENSE", "/home/<USER>/CODES/jirani-app/jirani_app/DOES_NOT_EXIST_RERUN_FOR_WILDCARD811950953"], "outputs": ["/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/jirani_logo.png", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/images/jirani_logo.svg", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/README.md", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/icons/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/fonts/.gitkeep", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon.svg", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon.ico", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/apple-touch-icon.png", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/site.webmanifest", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/favicon-96x96.png", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/web-app-manifest-192x192.png", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/assets/jirani/favicon/web-app-manifest-512x512.png", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z"]}