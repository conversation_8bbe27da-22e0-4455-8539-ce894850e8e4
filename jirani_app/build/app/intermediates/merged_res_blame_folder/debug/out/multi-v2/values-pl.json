{"logs": [{"outputFile": "com.jirani.jirani_app-mergeDebugResources-59:/values-pl/values-pl.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/6396ab40196ff674c8bdd109b465e5ae/transformed/jetified-android-11.12.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,204", "endColumns": "78,69,71", "endOffsets": "129,199,271"}, "to": {"startLines": "74,75,76", "startColumns": "4,4,4", "startOffsets": "8115,8194,8264", "endColumns": "78,69,71", "endOffsets": "8189,8259,8331"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/91275c0994e081a2c942bb9a68e5e5a4/transformed/jetified-maps-logo-11.12.0/res/values-pl/values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "227", "endOffsets": "278"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "8589", "endColumns": "227", "endOffsets": "8812"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,82", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2817,2914,3016,3114,3213,3327,3432,9114", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "2909,3011,3109,3208,3322,3427,3549,9210"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/res/values-pl/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3554,3658,3826,3948,4058,4209,4334,4445,4684,4855,4964,5139,5267,5426,5587,5656,5722", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "3653,3821,3943,4053,4204,4329,4440,4539,4850,4959,5134,5262,5421,5582,5651,5717,5801"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/res/values-pl/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4544", "endColumns": "139", "endOffsets": "4679"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/b1367e19f7f662de81e60ed815670a68/transformed/appcompat-1.6.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,9031", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,9109"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/d044b00a91d190fbece524175dbf0d6a/transformed/preference-1.2.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "54,56,79,80,83,84,85", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5806,5976,8817,8897,9215,9384,9465", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "5871,6062,8892,9026,9379,9460,9537"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/793f49ecebd7ced863c4ef4c7dcc4dbe/transformed/jetified-maps-compass-11.12.0/res/values-pl/values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "120", "endOffsets": "171"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7994", "endColumns": "120", "endOffsets": "8110"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/fd9400ff6f11fcf8dc9237a4d3672247/transformed/browser-1.8.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5876,6067,6166,6281", "endColumns": "99,98,114,103", "endOffsets": "5971,6161,6276,6380"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/08f0239a77411af6e1e0dfc8d33947f8/transformed/jetified-maps-attribution-11.12.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,219,311,422,789,891,980,1099,1251,1336,1417,1493,1575,1664", "endColumns": "163,91,110,366,101,88,118,151,84,80,75,81,88,252", "endOffsets": "214,306,417,784,886,975,1094,1246,1331,1412,1488,1570,1659,1912"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6385,6549,6641,6752,7119,7221,7310,7429,7581,7666,7747,7823,7905,8336", "endColumns": "163,91,110,366,101,88,118,151,84,80,75,81,88,252", "endOffsets": "6544,6636,6747,7114,7216,7305,7424,7576,7661,7742,7818,7900,7989,8584"}}]}]}