{"logs": [{"outputFile": "com.jirani.jirani_app-mergeDebugResources-59:/values-uk/values-uk.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/res/values-uk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3664,3827,3954,4064,4218,4347,4462,4713,4881,4987,5149,5274,5421,5563,5633,5694", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "3659,3822,3949,4059,4213,4342,4457,4562,4876,4982,5144,5269,5416,5558,5628,5689,5777"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/res/values-uk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4567", "endColumns": "145", "endOffsets": "4708"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/08f0239a77411af6e1e0dfc8d33947f8/transformed/jetified-maps-attribution-11.12.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,191,285,390,581,683,776,888,1040,1121,1202,1279,1368,1443", "endColumns": "135,93,104,190,101,92,111,151,80,80,76,88,74,247", "endOffsets": "186,280,385,576,678,771,883,1035,1116,1197,1274,1363,1438,1686"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6389,6525,6619,6724,6915,7017,7110,7222,7374,7455,7536,7613,7702,8122", "endColumns": "135,93,104,190,101,92,111,151,80,80,76,88,74,247", "endOffsets": "6520,6614,6719,6910,7012,7105,7217,7369,7450,7531,7608,7697,7772,8365"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/6396ab40196ff674c8bdd109b465e5ae/transformed/jetified-android-11.12.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,210", "endColumns": "82,71,70", "endOffsets": "133,205,276"}, "to": {"startLines": "74,75,76", "startColumns": "4,4,4", "startOffsets": "7896,7979,8051", "endColumns": "82,71,70", "endOffsets": "7974,8046,8117"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/fd9400ff6f11fcf8dc9237a4d3672247/transformed/browser-1.8.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5856,6054,6161,6281", "endColumns": "109,106,119,107", "endOffsets": "5961,6156,6276,6384"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/793f49ecebd7ced863c4ef4c7dcc4dbe/transformed/jetified-maps-compass-11.12.0/res/values-uk/values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "118", "endOffsets": "169"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7777", "endColumns": "118", "endOffsets": "7891"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,82", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2829,2929,3031,3132,3233,3338,3443,8900", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "2924,3026,3127,3228,3333,3438,3551,8996"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/d044b00a91d190fbece524175dbf0d6a/transformed/preference-1.2.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "54,56,79,80,83,84,85", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5782,5966,8595,8676,9001,9170,9255", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "5851,6049,8671,8813,9165,9250,9333"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/91275c0994e081a2c942bb9a68e5e5a4/transformed/jetified-maps-logo-11.12.0/res/values-uk/values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "224", "endOffsets": "275"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "8370", "endColumns": "224", "endOffsets": "8590"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/b1367e19f7f662de81e60ed815670a68/transformed/appcompat-1.6.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,8818", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,8895"}}]}]}