{"logs": [{"outputFile": "com.jirani.jirani_app-mergeDebugResources-59:/values-pt/values-pt.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/793f49ecebd7ced863c4ef4c7dcc4dbe/transformed/jetified-maps-compass-11.12.0/res/values-pt/values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "128", "endOffsets": "179"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5778", "endColumns": "128", "endOffsets": "5902"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,64", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2843,2940,3042,3141,3241,3348,3458,6905", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "2935,3037,3136,3236,3343,3453,3573,7001"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/d044b00a91d190fbece524175dbf0d6a/transformed/preference-1.2.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "36,38,61,62,65,66,67", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3578,3763,6589,6668,7006,7175,7262", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "3643,3845,6663,6814,7170,7257,7338"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/08f0239a77411af6e1e0dfc8d33947f8/transformed/jetified-maps-attribution-11.12.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,299,403,778,883,976,1082,1248,1331,1411,1486,1578,1666", "endColumns": "148,94,103,374,104,92,105,165,82,79,74,91,87,234", "endOffsets": "199,294,398,773,878,971,1077,1243,1326,1406,1481,1573,1661,1896"}, "to": {"startLines": "42,43,44,45,46,47,48,49,50,51,52,53,54,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4167,4316,4411,4515,4890,4995,5088,5194,5360,5443,5523,5598,5690,6135", "endColumns": "148,94,103,374,104,92,105,165,82,79,74,91,87,234", "endOffsets": "4311,4406,4510,4885,4990,5083,5189,5355,5438,5518,5593,5685,5773,6365"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/6396ab40196ff674c8bdd109b465e5ae/transformed/jetified-android-11.12.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,212", "endColumns": "83,72,70", "endOffsets": "134,207,278"}, "to": {"startLines": "56,57,58", "startColumns": "4,4,4", "startOffsets": "5907,5991,6064", "endColumns": "83,72,70", "endOffsets": "5986,6059,6130"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/fd9400ff6f11fcf8dc9237a4d3672247/transformed/browser-1.8.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3648,3850,3949,4061", "endColumns": "114,98,111,105", "endOffsets": "3758,3944,4056,4162"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/91275c0994e081a2c942bb9a68e5e5a4/transformed/jetified-maps-logo-11.12.0/res/values-pt/values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "218", "endOffsets": "269"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6370", "endColumns": "218", "endOffsets": "6584"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/b1367e19f7f662de81e60ed815670a68/transformed/appcompat-1.6.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,6819", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,6900"}}]}]}