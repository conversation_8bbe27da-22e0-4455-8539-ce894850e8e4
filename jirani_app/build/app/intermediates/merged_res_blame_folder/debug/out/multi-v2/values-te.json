{"logs": [{"outputFile": "com.jirani.jirani_app-mergeDebugResources-59:/values-te/values-te.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/res/values-te/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4587", "endColumns": "137", "endOffsets": "4720"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/fd9400ff6f11fcf8dc9237a4d3672247/transformed/browser-1.8.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5876,6086,6194,6305", "endColumns": "110,107,110,106", "endOffsets": "5982,6189,6300,6407"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/b1367e19f7f662de81e60ed815670a68/transformed/appcompat-1.6.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,6637", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,6715"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/d044b00a91d190fbece524175dbf0d6a/transformed/preference-1.2.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5803,5987,6412,6491,6821,6990,7077", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "5871,6081,6486,6632,6985,7072,7156"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2859,2961,3069,3171,3272,3378,3485,6720", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2956,3064,3166,3267,3373,3480,3604,6816"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3609,3721,3875,4005,4120,4257,4382,4487,4725,4874,4986,5139,5271,5422,5585,5649,5719", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "3716,3870,4000,4115,4252,4377,4482,4582,4869,4981,5134,5266,5417,5580,5644,5714,5798"}}]}]}