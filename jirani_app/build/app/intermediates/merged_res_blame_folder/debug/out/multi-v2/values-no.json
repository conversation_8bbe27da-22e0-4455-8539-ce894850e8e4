{"logs": [{"outputFile": "com.jirani.jirani_app-mergeDebugResources-59:/values-no/values-no.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/6396ab40196ff674c8bdd109b465e5ae/transformed/jetified-android-11.12.0/res/values-no/values-no.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,209", "endColumns": "76,76,69", "endOffsets": "127,204,274"}, "to": {"startLines": "16,17,18", "startColumns": "4,4,4", "startOffsets": "1662,1739,1816", "endColumns": "76,76,69", "endOffsets": "1734,1811,1881"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/91275c0994e081a2c942bb9a68e5e5a4/transformed/jetified-maps-logo-11.12.0/res/values-no/values-no.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "192", "endOffsets": "243"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "2108", "endColumns": "192", "endOffsets": "2296"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/08f0239a77411af6e1e0dfc8d33947f8/transformed/jetified-maps-attribution-11.12.0/res/values-no/values-no.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,275,374,697,796,887,987,1142,1220,1299,1369,1453,1534", "endColumns": "124,94,98,322,98,90,99,154,77,78,69,83,80,221", "endOffsets": "175,270,369,692,791,882,982,1137,1215,1294,1364,1448,1529,1751"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,275,374,697,796,887,987,1142,1220,1299,1369,1453,1886", "endColumns": "124,94,98,322,98,90,99,154,77,78,69,83,80,221", "endOffsets": "175,270,369,692,791,882,982,1137,1215,1294,1364,1448,1529,2103"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/793f49ecebd7ced863c4ef4c7dcc4dbe/transformed/jetified-maps-compass-11.12.0/res/values-no/values-no.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "127", "endOffsets": "178"}, "to": {"startLines": "15", "startColumns": "4", "startOffsets": "1534", "endColumns": "127", "endOffsets": "1657"}}]}]}