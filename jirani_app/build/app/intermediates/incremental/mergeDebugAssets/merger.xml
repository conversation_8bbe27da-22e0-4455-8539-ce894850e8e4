<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.mapbox.common:common:24.12.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/assets"><file name="sdk_versions/com.mapbox.common.core" path="/home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/assets/sdk_versions/com.mapbox.common.core"/></source></dataSet><dataSet config="com.mapbox.maps:android-core:11.12.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/assets"><file name="sdk_versions/com.mapbox.maps.core" path="/home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/assets/sdk_versions/com.mapbox.maps.core"/></source></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/url_launcher_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/sqflite_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/shared_preferences_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/path_provider_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":location" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/geocoding_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/flutter_secure_storage/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/flutter_plugin_android_lifecycle/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":mapbox_maps_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/mapbox_maps_flutter/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":google_maps_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/google_maps_flutter_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/connectivity_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/CODES/jirani-app/jirani_app/build/app/intermediates/shader_assets/debug/out"/></dataSet></merger>