1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jirani.jirani_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:8:5-67
15-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:8:22-64
16    <!-- Location permissions -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:3:5-79
17-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:3:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:4:5-81
18-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
19-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:5:5-85
19-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:5:22-82
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:6:5-68
20-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:6:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:7:5-77
21-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:7:22-74
22    <!--
23 Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:51:5-56:15
30        <intent>
30-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:52:9-55:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:53:13-72
31-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:53:21-70
32
33            <data android:mimeType="text/plain" />
33-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:54:13-50
33-->/home/<USER>/CODES/jirani-app/jirani_app/android/app/src/main/AndroidManifest.xml:54:19-48
34        </intent>
35        <!-- Needs to be explicitly declared on Android R+ -->
36        <package android:name="com.google.android.apps.maps" />
36-->[com.google.android.gms:play-services-maps:18.2.0] /home/<USER>/.gradle/caches/transforms-3/44b63826b9f4662c93bbf87c1b4351e9/transformed/jetified-play-services-maps-18.2.0/AndroidManifest.xml:33:9-64
36-->[com.google.android.gms:play-services-maps:18.2.0] /home/<USER>/.gradle/caches/transforms-3/44b63826b9f4662c93bbf87c1b4351e9/transformed/jetified-play-services-maps-18.2.0/AndroidManifest.xml:33:18-61
37    </queries>
38
39    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
39-->[:connectivity_plus] /home/<USER>/CODES/jirani-app/jirani_app/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
39-->[:connectivity_plus] /home/<USER>/CODES/jirani-app/jirani_app/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:22-76
40
41    <uses-feature
41-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:8:5-10:35
42        android:glEsVersion="0x00030000"
42-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:9:9-41
43        android:required="true" />
43-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:10:9-32
44    <uses-feature
44-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:11:5-13:36
45        android:name="android.hardware.wifi"
45-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:12:9-45
46        android:required="false" />
46-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:13:9-33
47
48    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
48-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:17:5-76
48-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:17:22-73
49
50    <permission
50-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
51        android:name="com.jirani.jirani_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.jirani.jirani_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
55
56    <application
57        android:name="android.app.Application"
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.13.1] /home/<USER>/.gradle/caches/transforms-3/53fb1d51a9803265c4ab9aa0bd3f1fbf/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
59        android:debuggable="true"
60        android:extractNativeLibs="true"
61        android:icon="@mipmap/ic_launcher"
62        android:label="jirani_app" >
63        <activity
64            android:name="com.jirani.jirani_app.MainActivity"
65            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
66            android:exported="true"
67            android:hardwareAccelerated="true"
68            android:launchMode="singleTop"
69            android:taskAffinity=""
70            android:theme="@style/LaunchTheme"
71            android:windowSoftInputMode="adjustResize" >
72
73            <!--
74                 Specifies an Android theme to apply to this Activity as soon as
75                 the Android process has started. This theme is visible to the user
76                 while the Flutter UI initializes. After that, this theme continues
77                 to determine the Window background behind the Flutter UI.
78            -->
79            <meta-data
80                android:name="io.flutter.embedding.android.NormalTheme"
81                android:resource="@style/NormalTheme" />
82
83            <intent-filter>
84                <action android:name="android.intent.action.MAIN" />
85
86                <category android:name="android.intent.category.LAUNCHER" />
87            </intent-filter>
88        </activity>
89        <!-- Mapbox configuration -->
90        <meta-data
91            android:name="com.mapbox.token"
92            android:value="********************************************************************************************" />
93
94        <!--
95             Don't delete the meta-data below.
96             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
97        -->
98        <meta-data
99            android:name="flutterEmbedding"
100            android:value="2" />
101
102        <provider
102-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
103            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
103-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
104            android:authorities="com.jirani.jirani_app.flutter.image_provider"
104-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
105            android:exported="false"
105-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
106            android:grantUriPermissions="true" >
106-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
107            <meta-data
107-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-16:75
108                android:name="android.support.FILE_PROVIDER_PATHS"
108-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-67
109                android:resource="@xml/flutter_image_picker_file_paths" />
109-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-72
110        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
111        <service
111-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
112            android:name="com.google.android.gms.metadata.ModuleDependencies"
112-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
113            android:enabled="false"
113-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
114            android:exported="false" >
114-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
115            <intent-filter>
115-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
116                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
116-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
116-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
117            </intent-filter>
118
119            <meta-data
119-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
120                android:name="photopicker_activity:0:required"
120-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
121                android:value="" />
121-->[:image_picker_android] /home/<USER>/CODES/jirani-app/jirani_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
122        </service>
123        <service
123-->[:location] /home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/merged_manifest/debug/AndroidManifest.xml:11:9-15:56
124            android:name="com.lyokone.location.FlutterLocationService"
124-->[:location] /home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-71
125            android:enabled="true"
125-->[:location] /home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-35
126            android:exported="false"
126-->[:location] /home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-37
127            android:foregroundServiceType="location" />
127-->[:location] /home/<USER>/CODES/jirani-app/jirani_app/build/location/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-53
128
129        <activity
129-->[:url_launcher_android] /home/<USER>/CODES/jirani-app/jirani_app/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
130            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
130-->[:url_launcher_android] /home/<USER>/CODES/jirani-app/jirani_app/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
131            android:exported="false"
131-->[:url_launcher_android] /home/<USER>/CODES/jirani-app/jirani_app/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
132            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
132-->[:url_launcher_android] /home/<USER>/CODES/jirani-app/jirani_app/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
133        <uses-library
133-->[com.google.android.gms:play-services-maps:18.2.0] /home/<USER>/.gradle/caches/transforms-3/44b63826b9f4662c93bbf87c1b4351e9/transformed/jetified-play-services-maps-18.2.0/AndroidManifest.xml:39:9-41:40
134            android:name="org.apache.http.legacy"
134-->[com.google.android.gms:play-services-maps:18.2.0] /home/<USER>/.gradle/caches/transforms-3/44b63826b9f4662c93bbf87c1b4351e9/transformed/jetified-play-services-maps-18.2.0/AndroidManifest.xml:40:13-50
135            android:required="false" />
135-->[com.google.android.gms:play-services-maps:18.2.0] /home/<USER>/.gradle/caches/transforms-3/44b63826b9f4662c93bbf87c1b4351e9/transformed/jetified-play-services-maps-18.2.0/AndroidManifest.xml:41:13-37
136
137        <provider
137-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:20:9-28:20
138            android:name="androidx.startup.InitializationProvider"
138-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:21:13-67
139            android:authorities="com.jirani.jirani_app.androidx-startup"
139-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:22:13-68
140            android:exported="false" >
140-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:23:13-37
141            <meta-data
141-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:25:13-27:52
142                android:name="com.mapbox.maps.loader.MapboxMapsInitializer"
142-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:26:17-76
143                android:value="androidx.startup" />
143-->[com.mapbox.maps:android-core:11.12.0] /home/<USER>/.gradle/caches/transforms-3/68268b2ca406e687dfb7e5cca93dd8b7/transformed/jetified-android-core-11.12.0/AndroidManifest.xml:27:17-49
144            <!-- This entry makes MapboxSDKCommonInitializer discoverable. -->
145            <meta-data
145-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:19:13-21:52
146                android:name="com.mapbox.common.MapboxSDKCommonInitializer"
146-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:20:17-76
147                android:value="androidx.startup" />
147-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:21:17-49
148            <meta-data
148-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/80792792bfd43b8e4eae15666771fa7d/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
149                android:name="androidx.emoji2.text.EmojiCompatInitializer"
149-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/80792792bfd43b8e4eae15666771fa7d/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
150                android:value="androidx.startup" />
150-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/80792792bfd43b8e4eae15666771fa7d/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
151            <meta-data
151-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/75ad43be2c37903450d8fecbfe2a2560/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
152                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
152-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/75ad43be2c37903450d8fecbfe2a2560/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
153                android:value="androidx.startup" />
153-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/75ad43be2c37903450d8fecbfe2a2560/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
155                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
156                android:value="androidx.startup" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
157        </provider>
158
159        <receiver
159-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:24:9-31:20
160            android:name="com.mapbox.common.location.LocationUpdatesReceiver"
160-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:25:13-78
161            android:enabled="true"
161-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:26:13-35
162            android:exported="false" >
162-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:27:13-37
163            <intent-filter>
163-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:28:13-30:29
164                <action android:name="com.mapbox.common.location.LocationUpdatesReceiver.ACTION_PROCESS_LOCATION_UPDATES" />
164-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:29:17-125
164-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:29:25-122
165            </intent-filter>
166        </receiver>
167
168        <service
168-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:33:9-35:40
169            android:name="com.mapbox.common.LifecycleService"
169-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:34:13-62
170            android:exported="false" />
170-->[com.mapbox.common:common:24.12.0] /home/<USER>/.gradle/caches/transforms-3/5234d87a8dd8f0fadbed298b77979764/transformed/jetified-common-24.12.0/AndroidManifest.xml:35:13-37
171
172        <activity
172-->[com.google.android.gms:play-services-base:18.5.0] /home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.5.0] /home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.5.0] /home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.google.android.gms:play-services-base:18.5.0] /home/<USER>/.gradle/caches/transforms-3/f3e25c817a459f821760ec15aaf19f2e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.4.0] /home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.4.0] /home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.4.0] /home/<USER>/.gradle/caches/transforms-3/db5f16d65ad28ce32a779c2f754643a0/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
180
181        <uses-library
181-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
182            android:name="androidx.window.extensions"
182-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
183            android:required="false" />
183-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
184        <uses-library
184-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
185            android:name="androidx.window.sidecar"
185-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
186            android:required="false" />
186-->[androidx.window:window:1.2.0] /home/<USER>/.gradle/caches/transforms-3/f448d122d8a6e8dc4556225fcbbc7392/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
187
188        <receiver
188-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
189            android:name="androidx.profileinstaller.ProfileInstallReceiver"
189-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
190            android:directBootAware="false"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
191            android:enabled="true"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
192            android:exported="true"
192-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
193            android:permission="android.permission.DUMP" >
193-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
195                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
195-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
195-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
198                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
198-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
198-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
201                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
201-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
204                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
204-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
204-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/567abc6110aff3e25dbe563f1ae5d8af/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
205            </intent-filter>
206        </receiver>
207    </application>
208
209</manifest>
