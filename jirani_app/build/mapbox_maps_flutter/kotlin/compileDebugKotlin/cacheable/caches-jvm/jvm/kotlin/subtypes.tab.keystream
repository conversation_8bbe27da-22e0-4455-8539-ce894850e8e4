8io.flutter.plugin.common.MethodChannel.MethodCallHandler+androidx.lifecycle.DefaultLifecycleObserver3com.mapbox.maps.plugin.viewport.state.ViewportState<EMAIL>.mapbox_maps.pigeons.********************************.mapbox.maps.mapbox_maps.pigeons._CameraManager<com.mapbox.maps.mapbox_maps.pigeons.CompassSettingsInterface"com.google.gson.TypeAdapterFactorycom.google.gson.JsonSerializercom.google.gson.TypeAdapter=com.mapbox.maps.mapbox_maps.pigeons.GesturesSettingsInterfaceGcom.mapbox.maps.mapbox_maps.pigeons._LocationComponentSettingsInterface"com.mapbox.common.LogWriterBackend9com.mapbox.maps.mapbox_maps.pigeons.LogoSettingsInterface1com.mapbox.maps.mapbox_maps.pigeons._MapInterface.com.mapbox.maps.mapbox_maps.pigeons.Projection'io.flutter.plugin.platform.PlatformView!androidx.lifecycle.LifecycleOwner.io.flutter.plugin.platform.PlatformViewFactory1io.flutter.embedding.engine.plugins.FlutterPlugin:io.flutter.embedding.engine.plugins.activity.ActivityAware6com.mapbox.maps.mapbox_maps.pigeons._MapboxMapsOptions2com.mapbox.maps.mapbox_maps.pigeons._MapboxOptions=com.mapbox.maps.mapbox_maps.pigeons._PerformanceStatisticsApi=com.mapbox.maps.mapbox_maps.pigeons.ScaleBarSettingsInterface0com.mapbox.maps.mapbox_maps.pigeons.StyleManager6com.mapbox.maps.mapbox_maps.pigeons._ViewportMessenger=com.mapbox.maps.plugin.viewport.transition.ViewportTransition9com.mapbox.maps.mapbox_maps.annotation.ControllerDelegate>com.mapbox.maps.mapbox_maps.pigeons._CircleAnnotationMessenger=com.mapbox.maps.mapbox_maps.pigeons._PointAnnotationMessenger?<EMAIL>.mapbox_maps.pigeons._PolylineAnnotationMessenger1com.mapbox.common.HttpServiceInterceptorInterface3com.mapbox.maps.mapbox_maps.pigeons._OfflineManager>com.mapbox.maps.mapbox_maps.pigeons._OfflineMapInstanceManager=com.mapbox.maps.mapbox_maps.pigeons._TileStoreInstanceManager2com.mapbox.maps.mapbox_maps.pigeons._OfflineSwitch.com.mapbox.maps.mapbox_maps.pigeons._TileStorekotlin.Throwable9com.mapbox.maps.mapbox_maps.pigeons._SnapshotterMessenger?com.mapbox.maps.mapbox_maps.pigeons._SnapshotterInstanceManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          