import '../../models/lat_lng.dart' as custom;
import '../../models/ride.dart';
import '../../services/network_bound_resource.dart';
import '../../services/ride_service.dart';
import 'base_repository.dart';

/// Ride repository for handling ride data
class RideRepository extends BaseRepository {
  /// Cache key for user ride history
  static const String _userRideHistoryCacheKey = 'user_ride_history';

  /// Creates a new instance of [RideRepository]
  RideRepository({
    super.cacheDuration = const Duration(hours: 1),
  });

  /// Create a ride request
  Future<Ride?> createRideRequest({
    required String userId,
    required RideLocation pickup,
    required RideLocation dropoff,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      final ride = await RideService.createRideRequest(
        userId: userId,
        pickup: pickup,
        dropoff: dropoff,
        paymentMethod: paymentMethod,
      );

      if (ride != null) {
        // Clear user ride history cache
        await clearCache('${_userRideHistoryCacheKey}_$userId');

        // Cache the ride
        await saveToCache('ride_${ride.id}', ride.toJson());
      }

      return ride;
    } catch (e, stackTrace) {
      logError('Error creating ride request', e, stackTrace);
      return null;
    }
  }

  /// Get ride by ID
  Future<Ride?> getRideById(String rideId) async {
    try {
      final cacheKey = 'ride_$rideId';

      // Try to get from cache first
      final cachedRide = await getFromCache<Ride>(
        cacheKey,
        (data) => Ride.fromJson(data),
      );

      if (cachedRide != null) {
        logInfo('Using cached ride data for $rideId');
        return cachedRide;
      }

      // Fetch from network
      final ride = await RideService.getRideById(rideId);

      if (ride != null) {
        // Cache the ride
        await saveToCache(cacheKey, ride.toJson());
      }

      return ride;
    } catch (e, stackTrace) {
      logError('Error getting ride by ID', e, stackTrace);
      return null;
    }
  }

  /// Get user ride history
  Stream<Resource<List<Ride>>> getUserRideHistory(String userId) {
    final cacheKey = '${_userRideHistoryCacheKey}_$userId';

    return getNetworkBoundResource<List<Ride>>(
      cacheKey: cacheKey,
      fetchFromNetwork: () => RideService.getUserRideHistory(userId),
      parseResponse: (data) {
        if (data is List) {
          return data.map((item) => Ride.fromJson(item)).toList();
        } else if (data is Map<String, dynamic>) {
          if (data.containsKey('rides') && data['rides'] is List) {
            return (data['rides'] as List)
                .map((item) => Ride.fromJson(item))
                .toList();
          }
        }

        // If the data is already a List<Ride>, return it
        if (data is List<Ride>) {
          return data;
        }

        return [];
      },
      shouldFetchFromNetwork: (cachedData) {
        // Always fetch from network if we have no cached data
        if (cachedData == null || cachedData.isEmpty) {
          return true;
        }

        // Otherwise, check if we should refresh based on cache duration
        return true;
      },
    );
  }

  /// Update ride status
  Future<Ride?> updateRideStatus({
    required String rideId,
    required RideStatus status,
  }) async {
    try {
      final ride = await RideService.updateRideStatus(
        rideId: rideId,
        status: status,
      );

      if (ride != null) {
        // Update cache
        await saveToCache('ride_$rideId', ride.toJson());

        // Clear user ride history cache
        await clearCache('${_userRideHistoryCacheKey}_${ride.userId}');
      }

      return ride;
    } catch (e, stackTrace) {
      logError('Error updating ride status', e, stackTrace);
      return null;
    }
  }

  /// Rate ride
  Future<bool> rateRide({
    required String rideId,
    required int rating,
    String? review,
  }) async {
    try {
      final success = await RideService.rateRide(
        rideId: rideId,
        rating: rating,
        review: review,
      );

      if (success) {
        // Clear ride cache
        await clearCache('ride_$rideId');

        // Get the ride to clear user ride history cache
        final ride = await getRideById(rideId);
        if (ride != null) {
          await clearCache('${_userRideHistoryCacheKey}_${ride.userId}');
        }
      }

      return success;
    } catch (e, stackTrace) {
      logError('Error rating ride', e, stackTrace);
      return false;
    }
  }

  /// Get estimated fare
  Future<double?> getEstimatedFare({
    required custom.LatLng pickup,
    required custom.LatLng dropoff,
  }) async {
    try {
      final cacheKey =
          'estimated_fare_${pickup.latitude}_${pickup.longitude}_${dropoff.latitude}_${dropoff.longitude}';

      // Try to get from cache first
      final cachedFare = await getFromCache<double>(
        cacheKey,
        (data) => (data as num).toDouble(),
      );

      if (cachedFare != null) {
        logInfo('Using cached estimated fare');
        return cachedFare;
      }

      // Fetch from network
      final fare = await RideService.getEstimatedFare(
        pickup: pickup,
        dropoff: dropoff,
      );

      if (fare != null) {
        // Cache the fare with a shorter duration
        await saveToCache(
          cacheKey,
          fare,
          customCacheDuration: const Duration(minutes: 15),
        );
      }

      return fare;
    } catch (e, stackTrace) {
      logError('Error getting estimated fare', e, stackTrace);
      return null;
    }
  }

  /// Complete ride
  Future<Ride?> completeRide(String rideId) async {
    return updateRideStatus(
      rideId: rideId,
      status: RideStatus.completed,
    );
  }

  /// Cancel ride
  Future<Ride?> cancelRide(String rideId) async {
    return updateRideStatus(
      rideId: rideId,
      status: RideStatus.cancelled,
    );
  }
}
