import '../../services/cache_manager.dart';
import '../../services/logging_service.dart';
import '../../services/network_bound_resource.dart';

/// Base repository for handling data sources
abstract class BaseRepository {
  /// Cache duration
  final Duration cacheDuration;
  
  /// Creates a new instance of [BaseRepository]
  BaseRepository({
    this.cacheDuration = const Duration(hours: 1),
  });
  
  /// Get data from network bound resource
  Stream<Resource<T>> getNetworkBoundResource<T>({
    required String cacheKey,
    required Future<T> Function() fetchFromNetwork,
    required T Function(dynamic) parseResponse,
    bool Function(T?)? shouldFetchFromNetwork,
    Duration? customCacheDuration,
  }) {
    try {
      final resource = NetworkBoundResource<T>(
        cacheKey: cacheKey,
        fetchFromNetwork: fetchFromNetwork,
        parseResponse: parseResponse,
        shouldFetchFromNetwork: shouldFetchFromNetwork,
        cacheDuration: customCacheDuration ?? cacheDuration,
      );
      
      return resource.fetch();
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error getting network bound resource',
        error: e,
        stackTrace: stackTrace,
        tag: 'BaseRepository',
      );
      
      return Stream.value(Resource.error(e, stackTrace));
    }
  }
  
  /// Get data from cache
  Future<T?> getFromCache<T>(
    String cacheKey,
    T Function(dynamic) parseResponse,
  ) async {
    try {
      final cachedData = await AppCacheManager.getCachedApiResponse(cacheKey);
      
      if (cachedData != null) {
        return parseResponse(cachedData);
      }
      
      return null;
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error getting data from cache',
        error: e,
        stackTrace: stackTrace,
        tag: 'BaseRepository',
      );
      
      return null;
    }
  }
  
  /// Save data to cache
  Future<void> saveToCache<T>(
    String cacheKey,
    T data, {
    Duration? customCacheDuration,
  }) async {
    try {
      await AppCacheManager.cacheApiResponse(
        cacheKey,
        data,
        duration: customCacheDuration ?? cacheDuration,
      );
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error saving data to cache',
        error: e,
        stackTrace: stackTrace,
        tag: 'BaseRepository',
      );
    }
  }
  
  /// Clear cache for a specific key
  Future<void> clearCache(String cacheKey) async {
    try {
      await AppCacheManager.clearApiCacheForEndpoint(cacheKey);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error clearing cache',
        error: e,
        stackTrace: stackTrace,
        tag: 'BaseRepository',
      );
    }
  }
  
  /// Clear all cache
  Future<void> clearAllCache() async {
    try {
      await AppCacheManager.clearApiCache();
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error clearing all cache',
        error: e,
        stackTrace: stackTrace,
        tag: 'BaseRepository',
      );
    }
  }
  
  /// Log error
  void logError(String message, dynamic error, StackTrace stackTrace) {
    LoggingService.e(
      message,
      error: error,
      stackTrace: stackTrace,
      tag: runtimeType.toString(),
    );
  }
  
  /// Log info
  void logInfo(String message) {
    LoggingService.i(
      message,
      tag: runtimeType.toString(),
    );
  }
  
  /// Log debug
  void logDebug(String message) {
    LoggingService.d(
      message,
      tag: runtimeType.toString(),
    );
  }
  
  /// Log warning
  void logWarning(String message, {dynamic error, StackTrace? stackTrace}) {
    LoggingService.w(
      message,
      error: error,
      stackTrace: stackTrace,
      tag: runtimeType.toString(),
    );
  }
}
