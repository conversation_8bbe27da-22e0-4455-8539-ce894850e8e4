import '../../models/lat_lng.dart' as custom;
import '../../models/rider.dart';
import '../../models/rider_review.dart';
import '../../services/network_bound_resource.dart';
import '../../services/rider_service.dart';
import 'base_repository.dart';

/// Rider repository for handling rider data
class RiderRepository extends BaseRepository {
  /// Cache key for nearby riders
  static const String _nearbyRidersCacheKey = 'nearby_riders';

  /// Creates a new instance of [RiderRepository]
  RiderRepository({
    super.cacheDuration = const Duration(minutes: 5),
  });

  /// Get nearby riders
  Stream<Resource<List<Rider>>> getNearbyRiders({
    required custom.LatLng userLocation,
    double radiusInKm = 5.0,
  }) {
    final cacheKey =
        '${_nearbyRidersCacheKey}_${userLocation.latitude}_${userLocation.longitude}_$radiusInKm';

    return getNetworkBoundResource<List<Rider>>(
      cacheKey: cacheKey,
      fetchFromNetwork: () => RiderService.getNearbyRiders(
        userLocation: userLocation,
        radiusInKm: radiusInKm,
      ),
      parseResponse: (data) {
        if (data is List) {
          return data.map((item) => Rider.fromJson(item)).toList();
        } else if (data is Map<String, dynamic>) {
          if (data.containsKey('riders') && data['riders'] is List) {
            return (data['riders'] as List)
                .map((item) => Rider.fromJson(item))
                .toList();
          }
        }

        // If the data is already a List<Rider>, return it
        if (data is List<Rider>) {
          return data;
        }

        return [];
      },
      shouldFetchFromNetwork: (cachedData) {
        // Always fetch from network if we have no cached data
        if (cachedData == null || cachedData.isEmpty) {
          return true;
        }

        // Otherwise, check if we should refresh based on cache duration
        return true;
      },
      customCacheDuration: const Duration(minutes: 5),
    );
  }

  /// Get rider by ID
  Future<Rider?> getRiderById(String riderId) async {
    try {
      final cacheKey = 'rider_$riderId';

      // Try to get from cache first
      final cachedRider = await getFromCache<Rider>(
        cacheKey,
        (data) => Rider.fromJson(data),
      );

      if (cachedRider != null) {
        logInfo('Using cached rider data for $riderId');
        return cachedRider;
      }

      // Fetch from network
      final rider = await RiderService.getRiderById(riderId);

      if (rider != null) {
        // Cache the rider
        await saveToCache(cacheKey, rider.toJson());
      }

      return rider;
    } catch (e, stackTrace) {
      logError('Error getting rider by ID', e, stackTrace);
      return null;
    }
  }

  /// Get rider rating
  Future<double?> getRiderRating(String riderId) async {
    try {
      final cacheKey = 'rider_rating_$riderId';

      // Try to get from cache first
      final cachedRating = await getFromCache<double>(
        cacheKey,
        (data) => (data as num).toDouble(),
      );

      if (cachedRating != null) {
        logInfo('Using cached rider rating for $riderId');
        return cachedRating;
      }

      // In a real implementation, this would call the API
      // For now, we'll simulate it by getting the rider's rating
      final rider = await getRiderById(riderId);
      final rating = rider?.rating;

      if (rating != null) {
        // Cache the rating
        await saveToCache(cacheKey, rating);
      }

      return rating;
    } catch (e, stackTrace) {
      logError('Error getting rider rating', e, stackTrace);
      return null;
    }
  }

  /// Get rider reviews
  Future<List<RiderReview>> getRiderReviews(String riderId) async {
    try {
      final cacheKey = 'rider_reviews_$riderId';

      // Try to get from cache first
      final cachedReviews = await getFromCache<List<RiderReview>>(
        cacheKey,
        (data) {
          if (data is List) {
            return data.map((item) => RiderReview.fromJson(item)).toList();
          }
          return [];
        },
      );

      if (cachedReviews != null && cachedReviews.isNotEmpty) {
        logInfo('Using cached rider reviews for $riderId');
        return cachedReviews;
      }

      // In a real implementation, this would call the API
      // For now, we'll simulate it with mock data
      final reviews = List.generate(
        5,
        (index) => RiderReview(
          id: 'review_${riderId}_$index',
          riderId: riderId,
          userId: 'user_$index',
          userName: 'User ${index + 1}',
          userImageUrl:
              'https://randomuser.me/api/portraits/${index % 2 == 0 ? 'men' : 'women'}/${index + 1}.jpg',
          rating: 3 + (index % 3),
          review: index % 2 == 0 ? 'Great rider, very professional!' : null,
          date: DateTime.now().subtract(Duration(days: index * 2)),
        ),
      );

      if (reviews.isNotEmpty) {
        // Cache the reviews
        await saveToCache(
          cacheKey,
          reviews.map((review) => review.toJson()).toList(),
        );
      }

      return reviews;
    } catch (e, stackTrace) {
      logError('Error getting rider reviews', e, stackTrace);
      return [];
    }
  }

  /// Rate rider
  Future<bool> rateRider({
    required String riderId,
    required int rating,
    String? review,
  }) async {
    try {
      // In a real implementation, this would call the API
      // For now, we'll simulate a successful rating
      final success = true;

      if (success) {
        // Clear cache for rider and reviews
        await clearCache('rider_$riderId');
        await clearCache('rider_rating_$riderId');
        await clearCache('rider_reviews_$riderId');
      }

      return success;
    } catch (e, stackTrace) {
      logError('Error rating rider', e, stackTrace);
      return false;
    }
  }

  /// Calculate ETA for a rider to reach a destination
  Future<int?> calculateEta({
    required RiderLocation riderLocation,
    required custom.LatLng destination,
  }) async {
    try {
      // In a real implementation, this would use the repository to call the API
      // For now, we'll use the service directly
      return await RiderService.calculateEta(
        riderLocation: riderLocation,
        destination: destination,
      );
    } catch (e, stackTrace) {
      logError('Error calculating ETA', e, stackTrace);
      return null;
    }
  }
}
