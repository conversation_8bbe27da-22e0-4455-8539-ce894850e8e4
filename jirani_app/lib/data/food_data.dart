import 'package:flutter/material.dart';
import '../models/food_item.dart';

/// Mock data for food categories
final List<FoodCategory> foodCategories = [
  const FoodCategory(
    id: 'cat1',
    name: 'Burgers',
    icon: Icons.lunch_dining,
    imageUrl: 'https://picsum.photos/seed/burger/500/300',
  ),
  const FoodCategory(
    id: 'cat2',
    name: 'Pizza',
    icon: Icons.local_pizza,
    imageUrl: 'https://picsum.photos/seed/pizza/500/300',
  ),
  const FoodCategory(
    id: 'cat3',
    name: 'Sushi',
    icon: Icons.set_meal,
    imageUrl: 'https://picsum.photos/seed/sushi/500/300',
  ),
  const FoodCategory(
    id: 'cat4',
    name: 'Pasta',
    icon: Icons.ramen_dining,
    imageUrl: 'https://picsum.photos/seed/pasta/500/300',
  ),
  const FoodCategory(
    id: 'cat5',
    name: '<PERSON><PERSON><PERSON>',
    icon: Icons.cake,
    imageUrl: 'https://picsum.photos/seed/dessert/500/300',
  ),
  const FoodCategory(
    id: 'cat6',
    name: 'Drinks',
    icon: Icons.local_bar,
    imageUrl: 'https://picsum.photos/seed/drinks/500/300',
  ),
];

/// Mock data for food items
final List<FoodItem> foodItems = [
  const FoodItem(
    id: 'food1',
    name: 'Classic Cheeseburger',
    description: 'Juicy beef patty with melted cheese, lettuce, tomato, and special sauce on a toasted bun.',
    price: 12.99,
    imageUrl: 'https://picsum.photos/seed/burger1/500/300',
    rating: 4.7,
    reviewCount: 128,
    prepTimeMinutes: 15,
    deliveryTimeMinutes: 30,
    category: 'Burgers',
    isFeatured: true,
    isPopular: true,
  ),
  const FoodItem(
    id: 'food2',
    name: 'Margherita Pizza',
    description: 'Classic pizza with tomato sauce, fresh mozzarella, basil, and olive oil on a thin crust.',
    price: 14.99,
    imageUrl: 'https://picsum.photos/seed/pizza1/500/300',
    rating: 4.5,
    reviewCount: 95,
    prepTimeMinutes: 20,
    deliveryTimeMinutes: 35,
    category: 'Pizza',
    isFeatured: false,
    isPopular: true,
  ),
  const FoodItem(
    id: 'food3',
    name: 'Salmon Sushi Roll',
    description: 'Fresh salmon, avocado, and cucumber wrapped in sushi rice and seaweed.',
    price: 16.99,
    imageUrl: 'https://picsum.photos/seed/sushi1/500/300',
    rating: 4.8,
    reviewCount: 76,
    prepTimeMinutes: 25,
    deliveryTimeMinutes: 40,
    category: 'Sushi',
    isFeatured: true,
    isPopular: false,
  ),
  const FoodItem(
    id: 'food4',
    name: 'Spaghetti Carbonara',
    description: 'Al dente spaghetti with creamy egg sauce, pancetta, and parmesan cheese.',
    price: 15.99,
    imageUrl: 'https://picsum.photos/seed/pasta1/500/300',
    rating: 4.6,
    reviewCount: 112,
    prepTimeMinutes: 18,
    deliveryTimeMinutes: 32,
    category: 'Pasta',
    isFeatured: false,
    isPopular: true,
  ),
  const FoodItem(
    id: 'food5',
    name: 'Chocolate Lava Cake',
    description: 'Warm chocolate cake with a molten chocolate center, served with vanilla ice cream.',
    price: 8.99,
    imageUrl: 'https://picsum.photos/seed/dessert1/500/300',
    rating: 4.9,
    reviewCount: 145,
    prepTimeMinutes: 15,
    deliveryTimeMinutes: 30,
    category: 'Desserts',
    isFeatured: true,
    isBestseller: true,
  ),
  const FoodItem(
    id: 'food6',
    name: 'Mango Smoothie',
    description: 'Refreshing smoothie made with fresh mangoes, yogurt, and a hint of honey.',
    price: 6.99,
    imageUrl: 'https://picsum.photos/seed/drink1/500/300',
    rating: 4.4,
    reviewCount: 89,
    prepTimeMinutes: 10,
    deliveryTimeMinutes: 25,
    category: 'Drinks',
    isFeatured: false,
    isPopular: true,
  ),
];

/// Mock data for restaurants
final List<Restaurant> restaurants = [
  const Restaurant(
    id: 'rest1',
    name: 'Burger Palace',
    logoUrl: 'https://picsum.photos/seed/burgerlogo/100/100',
    coverImageUrl: 'https://picsum.photos/seed/burgercover/500/300',
    rating: 4.7,
    reviewCount: 328,
    address: '123 Main St, Anytown',
    deliveryTimeMinutes: 30,
    deliveryFee: 2.99,
    minimumOrderAmount: 15.00,
    categories: ['Burgers', 'Fries', 'Drinks'],
    isFeatured: true,
  ),
  const Restaurant(
    id: 'rest2',
    name: 'Pizza Heaven',
    logoUrl: 'https://picsum.photos/seed/pizzalogo/100/100',
    coverImageUrl: 'https://picsum.photos/seed/pizzacover/500/300',
    rating: 4.5,
    reviewCount: 245,
    address: '456 Oak St, Somewhere',
    deliveryTimeMinutes: 35,
    deliveryFee: 3.99,
    minimumOrderAmount: 20.00,
    categories: ['Pizza', 'Pasta', 'Salads'],
    isFeatured: true,
  ),
  const Restaurant(
    id: 'rest3',
    name: 'Sushi World',
    logoUrl: 'https://picsum.photos/seed/sushilogo/100/100',
    coverImageUrl: 'https://picsum.photos/seed/sushicover/500/300',
    rating: 4.8,
    reviewCount: 176,
    address: '789 Pine St, Nowhere',
    deliveryTimeMinutes: 40,
    deliveryFee: 4.99,
    minimumOrderAmount: 25.00,
    categories: ['Sushi', 'Japanese', 'Asian'],
    isFeatured: false,
  ),
];
