import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:location/location.dart' as loc;
import '../models/lat_lng.dart' as custom;
import '../providers/location_provider.dart' as legacy_providers;
import '../providers/enhanced_location_provider.dart';
import '../services/enhanced_location_service.dart';
import '../services/logging_service.dart';

/// Integration adapter to bridge enhanced location service with existing BodaBoda system
/// PRP-LOCATION-ENH-001 Phase 3 Implementation
class LocationIntegrationAdapter {
  static final LocationIntegrationAdapter _instance =
      LocationIntegrationAdapter._internal();
  factory LocationIntegrationAdapter() => _instance;
  LocationIntegrationAdapter._internal();

  /// Initialize the integration adapter
  static Future<void> initialize(WidgetRef ref) async {
    try {
      // Initialize enhanced location service
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      await enhancedLocationService.initialize();

      // Start location tracking
      await enhancedLocationService.startLocationTracking(
        accuracyMode: LocationAccuracyMode.high,
        onLocationUpdate: (locationData) {
          _syncLocationToLegacyProviders(ref, locationData);
        },
      );

      // Load location history
      final locationHistoryNotifier =
          ref.read(locationHistoryStateProvider.notifier);
      await locationHistoryNotifier.refresh();

      // Set up bidirectional sync between old and new providers
      _setupProviderSync(ref);
    } catch (e) {
      LoggingService.e('Error initializing LocationIntegrationAdapter',
          error: e);
    }
  }

  /// Sync location data from enhanced service to legacy providers
  static void _syncLocationToLegacyProviders(
      WidgetRef ref, loc.LocationData locationData) {
    if (locationData.latitude != null && locationData.longitude != null) {
      // Update legacy user location provider by triggering a refresh
      // Note: We can't directly set the state, so we'll trigger a refresh
      final userLocationNotifier =
          ref.read(legacy_providers.userLocationProvider.notifier);
      userLocationNotifier.refreshLocation();
    }
  }

  /// Set up bidirectional sync between old and new location providers
  static void _setupProviderSync(WidgetRef ref) {
    // Sync pickup location from enhanced to legacy
    ref.listen(pickupLocationProvider, (previous, next) {
      if (next != null) {
        final customLatLng = custom.LatLng(next.latitude, next.longitude);
        final pickupNotifier =
            ref.read(legacy_providers.pickupLocationProvider.notifier);
        pickupNotifier.setLocation(customLatLng);
      }
    });

    // Sync dropoff location from enhanced to legacy
    ref.listen(dropoffLocationProvider, (previous, next) {
      if (next != null) {
        final customLatLng = custom.LatLng(next.latitude, next.longitude);
        final dropoffNotifier =
            ref.read(legacy_providers.dropoffLocationProvider.notifier);
        dropoffNotifier.setLocation(customLatLng);
      }
    });
  }

  /// Convert SavedLocation to custom.LatLng for legacy compatibility
  static custom.LatLng savedLocationToLatLng(SavedLocation location) {
    return custom.LatLng(location.latitude, location.longitude);
  }

  /// Convert custom.LatLng to SavedLocation for enhanced service
  static SavedLocation latLngToSavedLocation(
    custom.LatLng latLng,
    String address,
    LocationType type,
  ) {
    return SavedLocation(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      latitude: latLng.latitude,
      longitude: latLng.longitude,
      address: address,
      locationType: type.value,
      usageCount: 1,
      lastUsedAt: DateTime.now(),
      createdAt: DateTime.now(),
    );
  }

  /// Get current location using enhanced service but return legacy format
  static Future<custom.LatLng?> getCurrentLocationLegacy(WidgetRef ref) async {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      final locationData = await enhancedLocationService.getCurrentLocation();

      if (locationData.latitude != null && locationData.longitude != null) {
        return custom.LatLng(locationData.latitude!, locationData.longitude!);
      }
      return null;
    } catch (e) {
      LoggingService.e('Error getting current location', error: e);
      return null;
    }
  }

  /// Check if enhanced location service is available and initialized
  static bool isEnhancedLocationAvailable(WidgetRef ref) {
    try {
      ref.read(enhancedLocationServiceProvider);
      return true; // Service is available if we can read it
    } catch (e) {
      return false;
    }
  }

  /// Get location suggestions using enhanced service
  static Future<List<LocationSuggestion>> getLocationSuggestions(
    WidgetRef ref, {
    required String query,
    LocationType? type,
    int limit = 10,
  }) async {
    try {
      final locationHistory = ref.read(locationHistoryStateProvider);

      // Filter location history based on query
      final filteredHistory = locationHistory.when(
        data: (locations) => locations
            .where((location) =>
                location.address.toLowerCase().contains(query.toLowerCase()) &&
                (type == null || location.locationType == type.value))
            .take(limit)
            .map((location) => LocationSuggestion(
                  id: location.id,
                  address: location.address,
                  latitude: location.latitude,
                  longitude: location.longitude,
                  type: _parseLocationType(location.locationType),
                ))
            .toList(),
        loading: () => <LocationSuggestion>[],
        error: (_, __) => <LocationSuggestion>[],
      );

      return filteredHistory;
    } catch (e) {
      LoggingService.e('Error getting location suggestions', error: e);
      return [];
    }
  }

  /// Save location to enhanced service
  static Future<void> saveLocationToHistory(
    WidgetRef ref,
    custom.LatLng location,
    String address,
    LocationType type,
  ) async {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);

      await enhancedLocationService.saveLocationToHistory(
        latitude: location.latitude,
        longitude: location.longitude,
        address: address,
        locationType: type,
      );

      // Refresh location history
      final locationHistoryNotifier =
          ref.read(locationHistoryStateProvider.notifier);
      await locationHistoryNotifier.refresh();
    } catch (e) {
      LoggingService.e('Error saving location to history', error: e);
    }
  }

  /// Get recent locations from enhanced service
  static List<SavedLocation> getRecentLocations(WidgetRef ref,
      {int limit = 5}) {
    final locationHistory = ref.read(locationHistoryStateProvider);

    return locationHistory.when(
      data: (locations) => locations.take(limit).toList(),
      loading: () => [],
      error: (_, __) => [],
    );
  }

  /// Clear location cache
  static void clearLocationCache(WidgetRef ref) {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      enhancedLocationService.clearLocationCache();

      // Refresh location history
      final locationHistoryNotifier =
          ref.read(locationHistoryStateProvider.notifier);
      locationHistoryNotifier.refresh();
    } catch (e) {
      LoggingService.e('Error clearing location cache', error: e);
    }
  }
}

/// Location suggestion model for integration
class LocationSuggestion {
  final String id;
  final String address;
  final double latitude;
  final double longitude;
  final LocationType type;

  LocationSuggestion({
    required this.id,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.type,
  });
}

/// Location suggestion parameters for provider
class LocationSuggestionParams {
  final String query;
  final LocationType? type;
  final int limit;

  LocationSuggestionParams({
    required this.query,
    this.type,
    this.limit = 10,
  });
}

/// Provider for location suggestions
final locationSuggestionsProvider =
    FutureProvider.family<List<LocationSuggestion>, LocationSuggestionParams>(
        (ref, params) async {
  return await _getLocationSuggestionsForProvider(
    ref,
    query: params.query,
    type: params.type,
    limit: params.limit,
  );
});

/// Provider for recent locations
final recentLocationsProvider = Provider<List<SavedLocation>>((ref) {
  return _getRecentLocationsForProvider(ref);
});

/// Helper method for location suggestions provider
Future<List<LocationSuggestion>> _getLocationSuggestionsForProvider(
  Ref ref, {
  required String query,
  LocationType? type,
  int limit = 10,
}) async {
  try {
    final locationHistory = ref.read(locationHistoryStateProvider);

    // Filter location history based on query
    final filteredHistory = locationHistory.when(
      data: (locations) => locations
          .where((location) =>
              location.address.toLowerCase().contains(query.toLowerCase()) &&
              (type == null || location.locationType == type.value))
          .take(limit)
          .map((location) => LocationSuggestion(
                id: location.id,
                address: location.address,
                latitude: location.latitude,
                longitude: location.longitude,
                type: _parseLocationType(location.locationType),
              ))
          .toList(),
      loading: () => <LocationSuggestion>[],
      error: (_, __) => <LocationSuggestion>[],
    );

    return filteredHistory;
  } catch (e) {
    LoggingService.e('Error getting location suggestions', error: e);
    return [];
  }
}

/// Helper method for recent locations provider
List<SavedLocation> _getRecentLocationsForProvider(Ref ref, {int limit = 5}) {
  final locationHistory = ref.read(locationHistoryStateProvider);

  return locationHistory.when(
    data: (locations) => locations.take(limit).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
}

/// Parse location type from string
LocationType _parseLocationType(String typeString) {
  switch (typeString.toLowerCase()) {
    case 'home':
      return LocationType.home;
    case 'work':
      return LocationType.work;
    case 'pickup':
      return LocationType.pickup;
    case 'dropoff':
    case 'destination':
      return LocationType.destination;
    case 'favorite':
      return LocationType.favorite;
    default:
      return LocationType.pickup; // Default fallback
  }
}
