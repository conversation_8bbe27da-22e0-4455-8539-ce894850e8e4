import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:location/location.dart' as loc;
import '../models/lat_lng.dart' as custom;
import '../providers/location_provider.dart';
import '../providers/enhanced_location_provider.dart';
import '../services/enhanced_location_service.dart';

/// Integration adapter to bridge enhanced location service with existing BodaBoda system
/// PRP-LOCATION-ENH-001 Phase 3 Implementation
class LocationIntegrationAdapter {
  static final LocationIntegrationAdapter _instance = LocationIntegrationAdapter._internal();
  factory LocationIntegrationAdapter() => _instance;
  LocationIntegrationAdapter._internal();

  /// Initialize the integration adapter
  static Future<void> initialize(WidgetRef ref) async {
    try {
      // Initialize enhanced location service
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      await enhancedLocationService.initialize();
      
      // Start location tracking
      await enhancedLocationService.startLocationTracking(
        accuracyMode: LocationAccuracyMode.high,
        onLocationUpdate: (locationData) {
          _syncLocationToLegacyProviders(ref, locationData);
        },
      );
      
      // Load location history
      final locationHistoryNotifier = ref.read(locationHistoryStateProvider.notifier);
      await locationHistoryNotifier.refresh();
      
      // Set up bidirectional sync between old and new providers
      _setupProviderSync(ref);
      
    } catch (e) {
      throw LocationIntegrationException('Failed to initialize location integration: $e');
    }
  }

  /// Sync enhanced location data to legacy providers
  static void _syncLocationToLegacyProviders(WidgetRef ref, loc.LocationData locationData) {
    if (locationData.latitude != null && locationData.longitude != null) {
      final customLatLng = custom.LatLng(locationData.latitude!, locationData.longitude!);
      
      // Update legacy current location provider
      ref.read(currentLocationProvider.notifier).setLocation(customLatLng);
    }
  }

  /// Set up bidirectional sync between old and new location providers
  static void _setupProviderSync(WidgetRef ref) {
    // Sync pickup location from enhanced to legacy
    ref.listen(pickupLocationProvider, (previous, next) {
      if (next != null) {
        final customLatLng = custom.LatLng(next.latitude, next.longitude);
        ref.read(pickupLocationProvider.notifier).setLocation(customLatLng);
      }
    });
    
    // Sync dropoff location from enhanced to legacy
    ref.listen(dropoffLocationProvider, (previous, next) {
      if (next != null) {
        final customLatLng = custom.LatLng(next.latitude, next.longitude);
        ref.read(dropoffLocationProvider.notifier).setLocation(customLatLng);
      }
    });
  }

  /// Convert SavedLocation to custom.LatLng for legacy compatibility
  static custom.LatLng savedLocationToLatLng(SavedLocation location) {
    return custom.LatLng(location.latitude, location.longitude);
  }

  /// Convert custom.LatLng to SavedLocation for enhanced service
  static SavedLocation latLngToSavedLocation(
    custom.LatLng latLng, 
    String address, 
    LocationType type,
  ) {
    return SavedLocation(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      latitude: latLng.latitude,
      longitude: latLng.longitude,
      address: address,
      locationType: type.value,
      usageCount: 1,
      lastUsedAt: DateTime.now(),
      createdAt: DateTime.now(),
    );
  }

  /// Get current location using enhanced service but return legacy format
  static Future<custom.LatLng?> getCurrentLocationLegacy(WidgetRef ref) async {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      final locationData = await enhancedLocationService.getCurrentLocation();
      
      if (locationData.latitude != null && locationData.longitude != null) {
        return custom.LatLng(locationData.latitude!, locationData.longitude!);
      }
      return null;
    } catch (e) {
      throw LocationIntegrationException('Failed to get current location: $e');
    }
  }

  /// Save location to history when user selects it in legacy screens
  static Future<void> saveLocationFromLegacySelection(
    WidgetRef ref,
    custom.LatLng location,
    String address,
    bool isPickup,
  ) async {
    try {
      final locationHistoryNotifier = ref.read(locationHistoryStateProvider.notifier);
      
      await locationHistoryNotifier.saveLocation(
        latitude: location.latitude,
        longitude: location.longitude,
        address: address,
        locationType: isPickup ? LocationType.pickup : LocationType.destination,
      );
    } catch (e) {
      // Don't throw error for history saving - it's not critical
      print('Warning: Failed to save location to history: $e');
    }
  }

  /// Get location suggestions for legacy screens
  static Future<List<LocationSuggestion>> getLocationSuggestions(
    WidgetRef ref, {
    String? query,
    LocationType? type,
    int limit = 5,
  }) async {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      List<SavedLocation> locations;
      
      if (type != null) {
        locations = await enhancedLocationService.getLocationHistoryByType(type);
      } else {
        locations = await enhancedLocationService.getLocationHistory();
      }
      
      // Filter by query if provided
      if (query != null && query.isNotEmpty) {
        locations = enhancedLocationService.searchLocationHistory(query);
      }
      
      // Convert to legacy format and limit results
      return locations
          .take(limit)
          .map((location) => LocationSuggestion(
                address: location.address,
                latLng: custom.LatLng(location.latitude, location.longitude),
                usageCount: location.usageCount,
                type: location.locationType,
              ))
          .toList();
    } catch (e) {
      // Return empty list on error - don't break legacy functionality
      print('Warning: Failed to get location suggestions: $e');
      return [];
    }
  }

  /// Check if enhanced location service is available
  static bool isEnhancedLocationAvailable(WidgetRef ref) {
    try {
      final locationInitialization = ref.read(locationInitializationProvider);
      return locationInitialization.when(
        data: (initialized) => initialized,
        loading: () => false,
        error: (_, __) => false,
      );
    } catch (e) {
      return false;
    }
  }

  /// Graceful fallback to legacy location service
  static Future<void> fallbackToLegacyLocation(WidgetRef ref) async {
    try {
      // This would initialize the legacy location service
      // Implementation depends on existing LocationService
      print('Falling back to legacy location service');
    } catch (e) {
      throw LocationIntegrationException('Failed to fallback to legacy location: $e');
    }
  }

  /// Dispose resources
  static void dispose(WidgetRef ref) {
    try {
      final enhancedLocationService = ref.read(enhancedLocationServiceProvider);
      enhancedLocationService.dispose();
    } catch (e) {
      print('Warning: Error disposing location integration: $e');
    }
  }
}

/// Location suggestion model for legacy compatibility
class LocationSuggestion {
  final String address;
  final custom.LatLng latLng;
  final int usageCount;
  final String type;

  LocationSuggestion({
    required this.address,
    required this.latLng,
    required this.usageCount,
    required this.type,
  });
}

/// Custom exception for location integration errors
class LocationIntegrationException implements Exception {
  final String message;
  LocationIntegrationException(this.message);
  
  @override
  String toString() => 'LocationIntegrationException: $message';
}

/// Provider for location integration adapter
final locationIntegrationProvider = Provider<LocationIntegrationAdapter>((ref) {
  return LocationIntegrationAdapter();
});

/// Provider for location suggestions
final locationSuggestionsProvider = FutureProvider.family<List<LocationSuggestion>, LocationSuggestionParams>((ref, params) async {
  return await LocationIntegrationAdapter.getLocationSuggestions(
    ref,
    query: params.query,
    type: params.type,
    limit: params.limit,
  );
});

/// Parameters for location suggestions
class LocationSuggestionParams {
  final String? query;
  final LocationType? type;
  final int limit;

  LocationSuggestionParams({
    this.query,
    this.type,
    this.limit = 5,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationSuggestionParams &&
          runtimeType == other.runtimeType &&
          query == other.query &&
          type == other.type &&
          limit == other.limit;

  @override
  int get hashCode => query.hashCode ^ type.hashCode ^ limit.hashCode;
}
