import 'dart:async';
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:location/location.dart' as loc;
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

import '../models/websocket_message.dart';
import '../services/api_config.dart';
import '../services/enhanced_location_service.dart';
import '../services/logging_service.dart';
import '../services/websocket_service.dart';

/// Emergency service for handling emergency alerts and location sharing
class EmergencyService {
  static final EmergencyService _instance = EmergencyService._internal();
  factory EmergencyService() => _instance;
  EmergencyService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  final WebSocketService _webSocketService = WebSocketService();
  final EnhancedLocationService _locationService = EnhancedLocationService();

  // Emergency contacts
  static const String _emergencyNumber = '999'; // Kenya emergency number
  static const String _policeNumber = '999';
  static const String _ambulanceNumber = '999';

  // Emergency alert tracking
  bool _isEmergencyActive = false;
  Timer? _locationSharingTimer;
  StreamSubscription<loc.LocationData>? _locationSubscription;

  /// Check if emergency mode is active
  bool get isEmergencyActive => _isEmergencyActive;

  /// Trigger emergency alert
  Future<void> triggerEmergencyAlert({
    required String rideId,
    String? customMessage,
    EmergencyType type = EmergencyType.general,
  }) async {
    try {
      LoggingService.i('Emergency alert triggered: $type');

      // Get current location
      final position = await _getCurrentLocation();
      if (position == null) {
        throw Exception('Unable to get current location');
      }

      // Get authentication token
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Send emergency alert to backend
      await _sendEmergencyAlert(
        rideId: rideId,
        position: position,
        type: type,
        message: customMessage,
        token: token,
      );

      // Send real-time WebSocket alert
      await _sendWebSocketEmergencyAlert(
        rideId: rideId,
        position: position,
        type: type,
        message: customMessage,
      );

      // Start continuous location sharing
      await _startEmergencyLocationSharing(rideId);

      // Call emergency services if critical
      if (type == EmergencyType.medical || type == EmergencyType.accident) {
        await _callEmergencyServices(type);
      }

      _isEmergencyActive = true;
      LoggingService.i('Emergency alert sent successfully');
    } catch (e) {
      LoggingService.e('Error triggering emergency alert', error: e);
      rethrow;
    }
  }

  /// Send emergency alert to backend
  Future<void> _sendEmergencyAlert({
    required String rideId,
    required loc.LocationData position,
    required EmergencyType type,
    String? message,
    required String token,
  }) async {
    final baseUrl = ApiConfig.getBaseUrl();
    final response = await http.post(
      Uri.parse('$baseUrl/emergency/alert'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'ride_id': rideId,
        'emergency_type': type.value,
        'latitude': position.latitude!,
        'longitude': position.longitude!,
        'accuracy': position.accuracy!,
        'message': message ?? 'Emergency alert triggered',
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );

    if (response.statusCode != 201) {
      final errorData = jsonDecode(response.body);
      throw Exception('Failed to send emergency alert: ${errorData['error']}');
    }
  }

  /// Send real-time WebSocket emergency alert
  Future<void> _sendWebSocketEmergencyAlert({
    required String rideId,
    required loc.LocationData position,
    required EmergencyType type,
    String? message,
  }) async {
    final emergencyMessage = WebSocketMessage(
      type: WebSocketMessageType.emergency,
      rideId: rideId,
      data: {
        'emergency_type': type.value,
        'latitude': position.latitude!,
        'longitude': position.longitude!,
        'accuracy': position.accuracy!,
        'message': message ?? 'Emergency alert triggered',
        'is_active': true,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );

    _webSocketService.sendHighPriorityMessage(emergencyMessage);
  }

  /// Start continuous location sharing during emergency
  Future<void> _startEmergencyLocationSharing(String rideId) async {
    // Cancel any existing location sharing
    await _stopEmergencyLocationSharing();

    // Start high-frequency location updates (every 10 seconds)
    _locationSharingTimer = Timer.periodic(
      const Duration(seconds: 10),
      (timer) async {
        try {
          final position = await _getCurrentLocation();
          if (position != null) {
            await _shareEmergencyLocation(rideId, position);
          }
        } catch (e) {
          LoggingService.e('Error sharing emergency location', error: e);
        }
      },
    );

    LoggingService.i('Emergency location sharing started');
  }

  /// Share current location during emergency
  Future<void> _shareEmergencyLocation(
      String rideId, loc.LocationData position) async {
    final locationMessage = WebSocketMessage(
      type: WebSocketMessageType.locationUpdate,
      rideId: rideId,
      data: {
        'latitude': position.latitude!,
        'longitude': position.longitude!,
        'accuracy': position.accuracy!,
        'is_emergency': true,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );

    _webSocketService.sendHighPriorityMessage(locationMessage);
  }

  /// Get current location
  Future<loc.LocationData?> _getCurrentLocation() async {
    try {
      // Initialize location service if needed
      await _locationService.initialize();

      // Get current position with high accuracy for emergency
      return await _locationService.getCurrentLocation(
        accuracyMode: LocationAccuracyMode.high,
        timeout: const Duration(seconds: 10),
      );
    } catch (e) {
      LoggingService.e('Error getting current location', error: e);
      return null;
    }
  }

  /// Call emergency services
  Future<void> _callEmergencyServices(EmergencyType type) async {
    try {
      String phoneNumber;
      switch (type) {
        case EmergencyType.medical:
          phoneNumber = _ambulanceNumber;
          break;
        case EmergencyType.accident:
          phoneNumber = _emergencyNumber;
          break;
        default:
          phoneNumber = _policeNumber;
      }

      final uri = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        LoggingService.i('Emergency call initiated: $phoneNumber');
      } else {
        throw Exception('Unable to make emergency call');
      }
    } catch (e) {
      LoggingService.e('Error calling emergency services', error: e);
    }
  }

  /// Stop emergency mode
  Future<void> stopEmergencyMode(String rideId) async {
    try {
      await _stopEmergencyLocationSharing();

      // Send WebSocket message to stop emergency
      final stopMessage = WebSocketMessage(
        type: WebSocketMessageType.emergency,
        rideId: rideId,
        data: {
          'is_active': false,
          'stopped_at': DateTime.now().millisecondsSinceEpoch,
        },
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );

      _webSocketService.sendMessage(stopMessage);

      _isEmergencyActive = false;
      LoggingService.i('Emergency mode stopped');
    } catch (e) {
      LoggingService.e('Error stopping emergency mode', error: e);
    }
  }

  /// Stop emergency location sharing
  Future<void> _stopEmergencyLocationSharing() async {
    _locationSharingTimer?.cancel();
    _locationSharingTimer = null;

    await _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  /// Dispose resources
  void dispose() {
    _stopEmergencyLocationSharing();
  }
}

/// Emergency types
enum EmergencyType {
  general('general'),
  medical('medical'),
  accident('accident'),
  security('security'),
  breakdown('breakdown');

  const EmergencyType(this.value);
  final String value;
}
