import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jirani_app/providers/message_provider.dart';
import 'package:logger/logger.dart';
import 'base_api_service.dart';

final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(ref);
});

/// Authentication service for handling user authentication
class AuthService extends BaseApiService {
  final Ref _ref;
  /// Secure storage for storing tokens
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  AuthService(this._ref) : super(_ref);

  /// Logger for debugging
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 80,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  /// Token key in secure storage
  static const String _tokenKey = 'auth_token';

  /// User ID key in secure storage
  static const String _userIdKey = 'user_id';

  /// Register a new user
  Future<Map<String, dynamic>> register({
    required String fullName,
    required String email,
    required String password,
    required String phoneNumber,
  }) async {
    try {
      final data = await post(
        'auth/register',
        body: {
          'full_name': fullName,
          'email': email,
          'password': password,
          'phone_number': phoneNumber,
        },
      );

      // Save token and user ID
      await _storage.write(key: _tokenKey, value: data['token']);
      await _storage.write(key: _userIdKey, value: data['user']['id']);
      return data;
    } catch (e) {
      rethrow;
    }
  }

  /// Login a user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final data = await post(
        'auth/login',
        body: {
          'email': email,
          'password': password,
        },
      );

      // Save token and user ID
      await _storage.write(key: _tokenKey, value: data['token']);
      await _storage.write(key: _userIdKey, value: data['user']['id']);
      return data;
    } catch (e) {
      rethrow;
    }
  }

  /// Forgot password
  Future<void> forgotPassword({required String email}) async {
    try {
      await post(
        'auth/forgot-password',
        body: {
          'email': email,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      await post(
        'auth/reset-password',
        body: {
          'token': token,
          'new_password': newPassword,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Logout the current user
  Future<void> logout() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _userIdKey);
  }

  /// Check if a user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _storage.read(key: _tokenKey);
    return token != null;
  }

  /// Get the current user's token
  Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  /// Get the current user's ID
  Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }

  /// Get the current user's profile
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final token = await getToken();

      if (token == null) {
        throw Exception('Not authenticated');
      }

      return await get('users/me', token: token);
    } catch (e) {
      rethrow;
    }
  }

  /// Verify email with token
  Future<void> verifyEmail({required String token}) async {
    try {
      await get('auth/verify-email?token=$token');
    } catch (e) {
      rethrow;
    }
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateProfile({
    required String fullName,
    required String phoneNumber,
  }) async {
    try {
      final token = await getToken();

      if (token == null) {
        throw Exception('Not authenticated');
      }

      return await put(
        'users/me',
        token: token,
        body: {
          'full_name': fullName,
          'phone_number': phoneNumber,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Register the current user as a driver
  Future<Map<String, dynamic>> registerAsDriver({
    required String licenseNumber,
    required String idNumber,
  }) async {
    try {
      final token = await getToken();
      if (token == null) {
        throw Exception('Not authenticated');
      }

      _logger.i('Registering user as driver. License: $licenseNumber, ID: $idNumber');
      final data = await post(
        'driver/register', // Endpoint for driver registration
        token: token,
        body: {
          'license_number': licenseNumber,
          'id_number': idNumber,
        },
      );
      _logger.i('Driver registration successful: $data');
      return data;
    } catch (e) {
      _logger.e('Error registering as driver: $e');
      rethrow;
    }
  }

  /// Upload profile image
  Future<String> uploadProfileImage({required List<int> imageBytes}) async {
    try {
      final token = await getToken();

      if (token == null) {
        throw Exception('Not authenticated');
      }

      // Log the API endpoint for debugging
      final apiEndpoint = '$baseUrl/users/me/profileimage';
      _logger.i('Uploading profile image to: $apiEndpoint');

      // Create multipart request
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(apiEndpoint),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Bearer $token';

      // Add file
      request.files.add(
        http.MultipartFile.fromBytes(
          'image',
          imageBytes,
          filename: 'profile_image.jpg',
        ),
      );

      // Log request details
      _logger.d('Request headers: ${request.headers}');
      _logger.d('File size: ${imageBytes.length} bytes');

      // Send request
      try {
        final streamedResponse = await request.send();
        final response = await http.Response.fromStream(streamedResponse);

        // Log response details
        _logger.d('Response status code: ${response.statusCode}');
        _logger.d('Response body: ${response.body}');
        
        final messageNotifier = _ref.read(messageProvider.notifier);
        Map<String, dynamic> data;

        try {
            data = jsonDecode(response.body);
        } catch (e) {
            messageNotifier.showMessage(
                text: 'Server returned non-JSON response. Status: ${response.statusCode}',
                type: MessageType.error);
            throw Exception('Failed to upload profile image: Non-JSON response');
        }

        if (response.statusCode >= 200 && response.statusCode < 300) {
          _logger.i('Profile image uploaded successfully');
          final successMessage = data['message'] as String? ?? 'Profile image uploaded successfully.';
          messageNotifier.showMessage(text: successMessage, type: MessageType.success);
          return data['image_url'];
        } else {
          final errorMessage = data['error'] as String? ?? 
                               data['message'] as String? ?? 
                               'Failed to upload profile image. Status: ${response.statusCode}';
          _logger.e('Failed to upload profile image: $errorMessage');
          messageNotifier.showMessage(text: errorMessage, type: MessageType.error);
          throw Exception(errorMessage);
        }
      } catch (e) {
        // This catch block was part of the original diff, ensure it's correctly placed
        _logger.e('Error during HTTP request: $e');
        throw Exception('Network error during profile image upload: $e');
      }
    } catch (e) {
      _logger.e('Error in uploadProfileImage: $e');
      rethrow;
    }
  }
}
