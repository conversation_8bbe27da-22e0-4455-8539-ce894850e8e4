import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

/// Log level
enum LogLevel {
  /// Verbose log level
  verbose,

  /// Debug log level
  debug,

  /// Info log level
  info,

  /// Warning log level
  warning,

  /// Error log level
  error,

  /// WTF log level
  wtf,

  /// Nothing log level
  nothing,
}

/// Logging service for handling logs
class LoggingService {
  /// Logger instance
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  /// File logger instance
  static Logger? _fileLogger;

  /// Log file
  static File? _logFile;

  /// Log buffer
  static final List<String> _logBuffer = [];

  /// Maximum log buffer size
  static const int _maxLogBufferSize = 100;

  /// Whether to use file logging
  static bool _useFileLogging = false;

  /// Whether the service is initialized
  static bool _isInitialized = false;

  /// Initialize the logging service
  static Future<void> initialize({bool useFileLogging = false}) async {
    if (_isInitialized) return;

    _useFileLogging = useFileLogging;

    if (_useFileLogging) {
      await _initializeFileLogging();
    }

    _isInitialized = true;
  }

  /// Initialize file logging
  static Future<void> _initializeFileLogging() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDirectory = Directory('${directory.path}/logs');

      if (!await logDirectory.exists()) {
        await logDirectory.create(recursive: true);
      }

      final now = DateTime.now();
      final fileName = 'jirani_${now.year}-${now.month}-${now.day}.log';
      _logFile = File('${logDirectory.path}/$fileName');

      if (!await _logFile!.exists()) {
        await _logFile!.create();
      }

      _fileLogger = Logger(
        printer: SimplePrinter(printTime: true),
        output: FileOutput(_logFile!),
      );

      if (kDebugMode) {
        print('File logging initialized: ${_logFile!.path}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing file logging: $e');
      }
      _useFileLogging = false;
    }
  }

  /// Log a message
  static void log(
    LogLevel level,
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    if (!_isInitialized) {
      initialize();
    }

    final logMessage = tag != null ? '[$tag] $message' : message;

    switch (level) {
      case LogLevel.verbose:
        _logger.t(logMessage);
        if (_useFileLogging) _fileLogger?.t(logMessage);
        break;
      case LogLevel.debug:
        _logger.d(logMessage);
        if (_useFileLogging) _fileLogger?.d(logMessage);
        break;
      case LogLevel.info:
        _logger.i(logMessage);
        if (_useFileLogging) _fileLogger?.i(logMessage);
        break;
      case LogLevel.warning:
        _logger.w(logMessage);
        if (_useFileLogging) _fileLogger?.w(logMessage);
        break;
      case LogLevel.error:
        _logger.e(logMessage, error: error, stackTrace: stackTrace);
        if (_useFileLogging) {
          _fileLogger?.e(logMessage, error: error, stackTrace: stackTrace);
        }
        break;
      case LogLevel.wtf:
        _logger.f(logMessage, error: error, stackTrace: stackTrace);
        if (_useFileLogging) {
          _fileLogger?.f(logMessage, error: error, stackTrace: stackTrace);
        }
        break;
      case LogLevel.nothing:
        // Do nothing
        break;
    }

    // Add to buffer
    _addToBuffer(level, logMessage, error, stackTrace);
  }

  /// Add a log to the buffer
  static void _addToBuffer(
    LogLevel level,
    String message,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    final now = DateTime.now();
    final logEntry = {
      'timestamp': now.toIso8601String(),
      'level': level.toString(),
      'message': message,
      'error': error?.toString(),
      'stackTrace': stackTrace?.toString(),
    };

    _logBuffer.add(jsonEncode(logEntry));

    if (_logBuffer.length > _maxLogBufferSize) {
      _logBuffer.removeAt(0);
    }
  }

  /// Get the log buffer
  static List<String> getLogBuffer() {
    return List.unmodifiable(_logBuffer);
  }

  /// Clear the log buffer
  static void clearLogBuffer() {
    _logBuffer.clear();
  }

  /// Log a verbose message
  static void v(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.verbose, message,
        error: error, stackTrace: stackTrace, tag: tag);
  }

  /// Log a debug message
  static void d(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.debug, message,
        error: error, stackTrace: stackTrace, tag: tag);
  }

  /// Log an info message
  static void i(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.info, message, error: error, stackTrace: stackTrace, tag: tag);
  }

  /// Log a warning message
  static void w(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.warning, message,
        error: error, stackTrace: stackTrace, tag: tag);
  }

  /// Log an error message
  static void e(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.error, message,
        error: error, stackTrace: stackTrace, tag: tag);
  }

  /// Log a WTF message
  static void wtf(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    log(LogLevel.wtf, message, error: error, stackTrace: stackTrace, tag: tag);
  }
}

/// File output for logger
class FileOutput extends LogOutput {
  /// Log file
  final File file;

  /// Creates a new instance of [FileOutput]
  FileOutput(this.file);

  @override
  void output(OutputEvent event) {
    for (final line in event.lines) {
      file.writeAsStringSync('${line.trim()}\n', mode: FileMode.append);
    }
  }
}
