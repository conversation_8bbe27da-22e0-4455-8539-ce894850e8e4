import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/service.dart';

/// A service that provides mock data for the app
class MockDataService {
  // Private constructor to prevent instantiation
  MockDataService._();

  /// Gets a list of categories
  static List<Category> getCategories() {
    return [
      const Category(
        id: '1',
        name: 'Bo<PERSON> Boda',
        icon: Icons.motorcycle,
        color: Color(0xFFFF6B00),
        imageUrl: 'https://images.unsplash.com/photo-1625378667653-9be5d0bb6188',
      ),
      const Category(
        id: '2',
        name: 'Salon',
        icon: Icons.content_cut,
        color: Color(0xFFFF4785),
        imageUrl: 'https://images.unsplash.com/photo-**********-138dadb4c035',
      ),
      const Category(
        id: '3',
        name: 'Grocery',
        icon: Icons.shopping_basket,
        color: Color(0xFF4CAF50),
        imageUrl: 'https://images.unsplash.com/photo-**********-92c53300491e',
      ),
      const Category(
        id: '4',
        name: 'Mechanic',
        icon: Icons.build,
        color: Color(0xFF2196F3),
        imageUrl: 'https://images.unsplash.com/photo-1530046339915-99de2d4736cd',
      ),
      const Category(
        id: '5',
        name: 'Barber',
        icon: Icons.person,
        color: Color(0xFF9C27B0),
        imageUrl: 'https://images.unsplash.com/photo-1585747860715-2ba37e788b70',
      ),
      const Category(
        id: '6',
        name: 'Tailor',
        icon: Icons.checkroom,
        color: Color(0xFFFF9800),
        imageUrl: 'https://images.unsplash.com/photo-**********-8f358a7a47b2',
      ),
      const Category(
        id: '7',
        name: 'Food',
        icon: Icons.restaurant,
        color: Color(0xFFE91E63),
        imageUrl: 'https://images.unsplash.com/photo-1517245386807-bb43f0f5c827',
      ),
    ];
  }

  /// Gets a list of services
  static List<Service> getServices() {
    return [
      const Service(
        id: '1',
        name: 'Express Boda Ride',
        description: 'Fast and reliable boda boda service for all your transportation needs.',
        category: 'Boda Boda',
        price: 100.0,
        rating: 4.8,
        reviewCount: 120,
        imageUrl: 'https://images.unsplash.com/photo-**********-138dadb4c035',
        provider: ServiceProvider(
          id: '1',
          name: 'John\'s Boda Service',
          description: 'Providing reliable transportation services since 2015.',
          address: '123 Moi Avenue, Nairobi',
          phone: '+254712345678',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-**********-138dadb4c035',
          location: Location(latitude: -1.2921, longitude: 36.8219),
        ),
        isFeatured: true,
        isPopular: true,
      ),
      const Service(
        id: '2',
        name: 'Women\'s Haircut & Styling',
        description: 'Professional haircut and styling services for women.',
        category: 'Salon',
        price: 500.0,
        rating: 4.5,
        reviewCount: 85,
        imageUrl: 'https://images.unsplash.com/photo-**********-138dadb4c035',
        provider: ServiceProvider(
          id: '2',
          name: 'Mary\'s Beauty Salon',
          description: 'Professional beauty salon offering a wide range of services.',
          address: '456 Kenyatta Avenue, Nairobi',
          phone: '+254723456789',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f',
          location: Location(latitude: -1.2950, longitude: 36.8260),
        ),
        isFeatured: true,
      ),
      const Service(
        id: '3',
        name: 'Fresh Fruit Basket',
        description: 'Assorted fresh fruits delivered to your doorstep.',
        category: 'Grocery',
        price: 600.0,
        rating: 4.2,
        reviewCount: 65,
        imageUrl: 'https://images.unsplash.com/photo-**********-92c53300491e',
        provider: ServiceProvider(
          id: '3',
          name: 'Fresh Groceries',
          description: 'Fresh fruits, vegetables, and groceries delivered to your doorstep.',
          address: '789 Kimathi Street, Nairobi',
          phone: '+254734567890',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-**********-92c53300491e',
          location: Location(latitude: -1.2930, longitude: 36.8240),
        ),
        isPopular: true,
      ),
      const Service(
        id: '4',
        name: 'Car Engine Repair',
        description: 'Professional car engine repair and maintenance services.',
        category: 'Mechanic',
        price: 2000.0,
        rating: 4.7,
        reviewCount: 42,
        imageUrl: 'https://images.unsplash.com/photo-1530046339915-99de2d4736cd',
        provider: ServiceProvider(
          id: '4',
          name: 'Auto Repair Shop',
          description: 'Professional auto repair services for all makes and models.',
          address: '101 Ngong Road, Nairobi',
          phone: '+254745678901',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-1530046339915-99de2d4736cd',
          location: Location(latitude: -1.2980, longitude: 36.8290),
        ),
      ),
      const Service(
        id: '5',
        name: 'Men\'s Haircut & Beard Trim',
        description: 'Professional haircut and beard trimming services for men.',
        category: 'Barber',
        price: 300.0,
        rating: 4.9,
        reviewCount: 110,
        imageUrl: 'https://images.unsplash.com/photo-1585747860715-2ba37e788b70',
        provider: ServiceProvider(
          id: '5',
          name: 'Modern Barber Shop',
          description: 'Modern barber shop offering haircuts, beard trims, and more.',
          address: '202 Uhuru Highway, Nairobi',
          phone: '+254756789012',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-1585747860715-2ba37e788b70',
          location: Location(latitude: -1.2910, longitude: 36.8230),
        ),
        isFeatured: true,
        isPopular: true,
      ),
      const Service(
        id: '6',
        name: 'Custom Suit Tailoring',
        description: 'Custom suit tailoring services for all occasions.',
        category: 'Tailor',
        price: 5000.0,
        rating: 4.6,
        reviewCount: 38,
        imageUrl: 'https://images.unsplash.com/photo-**********-8f358a7a47b2',
        provider: ServiceProvider(
          id: '6',
          name: 'Elite Tailors',
          description: 'Custom tailoring services for men and women.',
          address: '303 Mombasa Road, Nairobi',
          phone: '+254767890123',
          email: '<EMAIL>',
          imageUrl: 'https://images.unsplash.com/photo-**********-8f358a7a47b2',
          location: Location(latitude: -1.3010, longitude: 36.8320),
        ),
      ),
    ];
  }

  /// Gets a list of featured services
  static List<Service> getFeaturedServices() {
    return getServices().where((service) => service.isFeatured).toList();
  }

  /// Gets a list of popular services
  static List<Service> getPopularServices() {
    return getServices().where((service) => service.isPopular).toList();
  }

  /// Gets a service by ID
  static Service? getServiceById(String id) {
    try {
      return getServices().firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Gets a list of services by category
  static List<Service> getServicesByCategory(String category) {
    return getServices().where((service) => service.category == category).toList();
  }

  /// Gets a list of service providers
  static List<ServiceProvider> getProviders() {
    // Extract unique providers from services
    return getServices().map((service) => service.provider).toList();
  }
}
