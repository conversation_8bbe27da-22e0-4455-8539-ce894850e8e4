import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:location/location.dart' as loc;
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/lat_lng.dart' as custom;
import '../services/api_config.dart';

/// Enhanced location service with improved GPS accuracy and location history
/// PRP-LOCATION-ENH-001 Phase 2 Implementation
class EnhancedLocationService {
  // Singleton instance
  static final EnhancedLocationService _instance =
      EnhancedLocationService._internal();
  factory EnhancedLocationService() => _instance;
  EnhancedLocationService._internal();

  // Location instance with enhanced settings
  final loc.Location _location = loc.Location();

  // Stream controllers
  final _locationController = StreamController<loc.LocationData>.broadcast();
  final _locationHistoryController =
      StreamController<List<SavedLocation>>.broadcast();

  // Streams
  Stream<loc.LocationData> get locationStream => _locationController.stream;
  Stream<List<SavedLocation>> get locationHistoryStream =>
      _locationHistoryController.stream;

  // Subscriptions
  StreamSubscription<loc.LocationData>? _locationSubscription;

  // Cache for location history
  List<SavedLocation> _cachedLocationHistory = [];
  Timer? _cacheRefreshTimer;

  // Enhanced GPS accuracy settings
  static const LocationSettings _highAccuracySettings = LocationSettings(
    accuracy: loc.LocationAccuracy.high,
    interval: 3000, // 3 seconds for high accuracy
    distanceFilter: 5, // 5 meters minimum distance
  );

  static const LocationSettings _balancedSettings = LocationSettings(
    accuracy: loc.LocationAccuracy.balanced,
    interval: 5000, // 5 seconds for balanced mode
    distanceFilter: 10, // 10 meters minimum distance
  );

  static const LocationSettings _powerSaveSettings = LocationSettings(
    accuracy: loc.LocationAccuracy.low,
    interval: 10000, // 10 seconds for power save
    distanceFilter: 20, // 20 meters minimum distance
  );

  /// Initialize the enhanced location service
  Future<void> initialize() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          throw LocationException('Location services are disabled.');
        }
      }

      // Check for location permissions
      loc.PermissionStatus permissionStatus = await _location.hasPermission();
      if (permissionStatus == loc.PermissionStatus.denied) {
        permissionStatus = await _location.requestPermission();
        if (permissionStatus == loc.PermissionStatus.denied) {
          throw LocationException('Location permissions are denied');
        }
      }

      if (permissionStatus == loc.PermissionStatus.deniedForever) {
        throw LocationException(
            'Location permissions are permanently denied, we cannot request permissions.');
      }

      // Configure enhanced location settings
      await _configureLocationSettings(LocationAccuracyMode.high);

      // Load cached location history
      await _loadCachedLocationHistory();

      // Start periodic cache refresh
      _startCacheRefreshTimer();

      if (kDebugMode) {
        print('Enhanced Location Service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Enhanced Location Service initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Configure location settings based on accuracy mode
  Future<void> _configureLocationSettings(LocationAccuracyMode mode) async {
    LocationSettings settings;

    switch (mode) {
      case LocationAccuracyMode.high:
        settings = _highAccuracySettings;
        break;
      case LocationAccuracyMode.balanced:
        settings = _balancedSettings;
        break;
      case LocationAccuracyMode.powerSave:
        settings = _powerSaveSettings;
        break;
    }

    await _location.changeSettings(
      accuracy: settings.accuracy,
      interval: settings.interval,
      distanceFilter: settings.distanceFilter,
    );
  }

  /// Start enhanced location tracking
  Future<void> startLocationTracking({
    LocationAccuracyMode accuracyMode = LocationAccuracyMode.high,
    Function(loc.LocationData)? onLocationUpdate,
  }) async {
    try {
      // Configure settings for the requested accuracy mode
      await _configureLocationSettings(accuracyMode);

      _locationSubscription = _location.onLocationChanged.listen(
        (loc.LocationData locationData) {
          // Validate location data
          if (_isValidLocationData(locationData)) {
            // Add to stream
            _locationController.add(locationData);

            // Call callback if set
            onLocationUpdate?.call(locationData);

            if (kDebugMode) {
              print(
                  'Enhanced location update: ${locationData.latitude}, ${locationData.longitude} (accuracy: ${locationData.accuracy}m)');
            }
          }
        },
        onError: (error) {
          if (kDebugMode) {
            print('Location tracking error: $error');
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to start location tracking: $e');
      }
      rethrow;
    }
  }

  /// Stop location tracking
  Future<void> stopLocationTracking() async {
    await _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  /// Get current location with enhanced accuracy
  Future<loc.LocationData> getCurrentLocation({
    LocationAccuracyMode accuracyMode = LocationAccuracyMode.high,
    Duration timeout = const Duration(seconds: 10),
  }) async {
    try {
      await _configureLocationSettings(accuracyMode);

      final locationData = await _location.getLocation().timeout(timeout);

      if (!_isValidLocationData(locationData)) {
        throw LocationException('Invalid location data received');
      }

      return locationData;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get current location: $e');
      }
      rethrow;
    }
  }

  /// Validate location data quality
  bool _isValidLocationData(loc.LocationData locationData) {
    if (locationData.latitude == null || locationData.longitude == null) {
      return false;
    }

    // Check if coordinates are within reasonable bounds
    final lat = locationData.latitude!;
    final lng = locationData.longitude!;

    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return false;
    }

    // Check accuracy if available (reject if accuracy is too poor)
    if (locationData.accuracy != null && locationData.accuracy! > 100) {
      if (kDebugMode) {
        print('Location accuracy too poor: ${locationData.accuracy}m');
      }
      return false;
    }

    return true;
  }

  /// Get address from coordinates using geocoding
  Future<String> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _formatAddress(placemark);
      }

      return 'Unknown location';
    } catch (e) {
      if (kDebugMode) {
        print('Geocoding failed: $e');
      }
      return 'Address unavailable';
    }
  }

  /// Format address from placemark
  String _formatAddress(Placemark placemark) {
    List<String> addressParts = [];

    if (placemark.name != null && placemark.name!.isNotEmpty) {
      addressParts.add(placemark.name!);
    }

    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }

    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }

    if (placemark.country != null && placemark.country!.isNotEmpty) {
      addressParts.add(placemark.country!);
    }

    return addressParts.join(', ');
  }

  /// Load cached location history from local storage
  Future<void> _loadCachedLocationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('location_history_cache');

      if (cachedData != null) {
        final List<dynamic> jsonList = json.decode(cachedData);
        _cachedLocationHistory =
            jsonList.map((json) => SavedLocation.fromJson(json)).toList();

        // Emit cached data
        _locationHistoryController.add(_cachedLocationHistory);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load cached location history: $e');
      }
    }
  }

  /// Start periodic cache refresh timer
  void _startCacheRefreshTimer() {
    _cacheRefreshTimer?.cancel();
    _cacheRefreshTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => refreshLocationHistory(),
    );
  }

  /// Refresh location history from server
  Future<void> refreshLocationHistory() async {
    try {
      final history = await getLocationHistory();
      _cachedLocationHistory = history;

      // Cache to local storage
      final prefs = await SharedPreferences.getInstance();
      final jsonList = history.map((location) => location.toJson()).toList();
      await prefs.setString('location_history_cache', json.encode(jsonList));

      // Emit updated data
      _locationHistoryController.add(_cachedLocationHistory);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to refresh location history: $e');
      }
    }
  }

  /// Get location history from cache (fast) or server (if cache is empty)
  Future<List<SavedLocation>> getLocationHistory(
      {bool forceRefresh = false}) async {
    if (forceRefresh || _cachedLocationHistory.isEmpty) {
      await _fetchLocationHistoryFromServer();
    }
    return _cachedLocationHistory;
  }

  /// Fetch location history from server
  Future<void> _fetchLocationHistoryFromServer() async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw LocationException('Authentication required');
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.getBaseUrl()}/users/me/location-history'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['locations'] != null) {
          final List<dynamic> locationsJson = data['locations'];
          _cachedLocationHistory = locationsJson
              .map((json) => SavedLocation.fromJson(json))
              .toList();

          // Cache to local storage
          final prefs = await SharedPreferences.getInstance();
          final jsonList = _cachedLocationHistory
              .map((location) => location.toJson())
              .toList();
          await prefs.setString(
              'location_history_cache', json.encode(jsonList));

          // Emit updated data
          _locationHistoryController.add(_cachedLocationHistory);
        }
      } else {
        throw LocationException(
            'Failed to fetch location history: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to fetch location history from server: $e');
      }
      rethrow;
    }
  }

  /// Get authentication token from secure storage
  Future<String?> _getAuthToken() async {
    try {
      const storage = FlutterSecureStorage();
      return await storage.read(key: 'auth_token');
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get auth token: $e');
      }
      return null;
    }
  }

  /// Save location to history
  Future<SavedLocation> saveLocationToHistory({
    required double latitude,
    required double longitude,
    required String address,
    required LocationType locationType,
  }) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw LocationException('Authentication required');
      }

      final requestData = {
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
        'location_type': locationType.value,
      };

      final response = await http.post(
        Uri.parse('${ApiConfig.getBaseUrl()}/users/me/location-history'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestData),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['location'] != null) {
          final savedLocation = SavedLocation.fromJson(data['location']);

          // Update cache
          final existingIndex = _cachedLocationHistory.indexWhere(
            (loc) => loc.id == savedLocation.id,
          );

          if (existingIndex >= 0) {
            _cachedLocationHistory[existingIndex] = savedLocation;
          } else {
            _cachedLocationHistory.insert(0, savedLocation);
          }

          // Update local cache
          final prefs = await SharedPreferences.getInstance();
          final jsonList = _cachedLocationHistory
              .map((location) => location.toJson())
              .toList();
          await prefs.setString(
              'location_history_cache', json.encode(jsonList));

          // Emit updated data
          _locationHistoryController.add(_cachedLocationHistory);

          return savedLocation;
        }
      }

      throw LocationException(
          'Failed to save location to history: ${response.statusCode}');
    } catch (e) {
      if (kDebugMode) {
        print('Failed to save location to history: $e');
      }
      rethrow;
    }
  }

  /// Delete location from history
  Future<void> deleteLocationFromHistory(String locationId) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw LocationException('Authentication required');
      }

      final response = await http.delete(
        Uri.parse(
            '${ApiConfig.getBaseUrl()}/users/me/location-history/$locationId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode != 200) {
        throw LocationException(
            'Failed to delete location: ${response.statusCode}');
      }

      // Update cache
      _cachedLocationHistory.removeWhere((loc) => loc.id == locationId);

      // Update local cache
      final prefs = await SharedPreferences.getInstance();
      final jsonList =
          _cachedLocationHistory.map((location) => location.toJson()).toList();
      await prefs.setString('location_history_cache', json.encode(jsonList));

      // Emit updated data
      _locationHistoryController.add(_cachedLocationHistory);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to delete location from history: $e');
      }
      rethrow;
    }
  }

  /// Get location history by type
  Future<List<SavedLocation>> getLocationHistoryByType(
      LocationType type) async {
    final allHistory = await getLocationHistory();
    return allHistory.where((loc) => loc.locationType == type.value).toList();
  }

  /// Search location history
  List<SavedLocation> searchLocationHistory(String query) {
    if (query.isEmpty) return _cachedLocationHistory;

    final lowerQuery = query.toLowerCase();
    return _cachedLocationHistory.where((location) {
      return location.address.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Optimize location tracking for battery efficiency
  Future<void> optimizeForBattery() async {
    try {
      await _configureLocationSettings(LocationAccuracyMode.powerSave);

      // Reduce update frequency for battery optimization
      await _location.changeSettings(
        interval: 30000, // 30 seconds
        distanceFilter: 50, // 50 meters
      );

      if (kDebugMode) {
        print('Enhanced Location Service: Optimized for battery efficiency');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Enhanced Location Service: Battery optimization failed: $e');
      }
    }
  }

  /// Optimize location tracking for high accuracy
  Future<void> optimizeForAccuracy() async {
    try {
      await _configureLocationSettings(LocationAccuracyMode.high);

      // Increase update frequency for high accuracy
      await _location.changeSettings(
        interval: 3000, // 3 seconds
        distanceFilter: 5, // 5 meters
      );

      if (kDebugMode) {
        print('Enhanced Location Service: Optimized for high accuracy');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Enhanced Location Service: Accuracy optimization failed: $e');
      }
    }
  }

  /// Adaptive location tracking based on ride status
  Future<void> adaptiveLocationTracking({
    required bool isRideActive,
    required bool isEmergency,
  }) async {
    try {
      if (isEmergency) {
        // Emergency mode: highest accuracy and frequency
        await _configureLocationSettings(LocationAccuracyMode.high);
        await _location.changeSettings(
          interval: 1000, // 1 second
          distanceFilter: 1, // 1 meter
        );
      } else if (isRideActive) {
        // Active ride: balanced accuracy and battery
        await _configureLocationSettings(LocationAccuracyMode.balanced);
        await _location.changeSettings(
          interval: 5000, // 5 seconds
          distanceFilter: 10, // 10 meters
        );
      } else {
        // Idle mode: battery optimization
        await optimizeForBattery();
      }

      if (kDebugMode) {
        print('Enhanced Location Service: Adaptive tracking configured - '
            'Ride: $isRideActive, Emergency: $isEmergency');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Enhanced Location Service: Adaptive tracking failed: $e');
      }
    }
  }

  /// Get location performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'cached_locations': _cachedLocationHistory.length,
      'location_stream_active': _locationSubscription != null,
      'cache_refresh_active': _cacheRefreshTimer != null,
      'last_location_update': _cachedLocationHistory.isNotEmpty
          ? _cachedLocationHistory.first.lastUsedAt.toIso8601String()
          : null,
      'total_saved_locations': _cachedLocationHistory.length,
    };
  }

  /// Clear location cache to free memory
  void clearLocationCache() {
    _cachedLocationHistory.clear();
    if (kDebugMode) {
      print('Enhanced Location Service: Location cache cleared');
    }
  }

  /// Dispose resources
  void dispose() {
    _locationSubscription?.cancel();
    _cacheRefreshTimer?.cancel();
    _locationController.close();
    _locationHistoryController.close();
  }
}

/// Location accuracy modes
enum LocationAccuracyMode {
  high, // Best accuracy, more battery usage
  balanced, // Good accuracy, moderate battery usage
  powerSave // Lower accuracy, minimal battery usage
}

/// Location types for history categorization
enum LocationType {
  pickup('pickup'),
  destination('destination'),
  favorite('favorite'),
  home('home'),
  work('work');

  const LocationType(this.value);
  final String value;
}

/// Location settings configuration
class LocationSettings {
  final loc.LocationAccuracy accuracy;
  final int interval;
  final double distanceFilter;

  const LocationSettings({
    required this.accuracy,
    required this.interval,
    required this.distanceFilter,
  });
}

/// Custom location exception
class LocationException implements Exception {
  final String message;
  LocationException(this.message);

  @override
  String toString() => 'LocationException: $message';
}

/// Saved location model
class SavedLocation {
  final String id;
  final double latitude;
  final double longitude;
  final String address;
  final String locationType;
  final int usageCount;
  final DateTime lastUsedAt;
  final DateTime createdAt;

  SavedLocation({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.locationType,
    required this.usageCount,
    required this.lastUsedAt,
    required this.createdAt,
  });

  factory SavedLocation.fromJson(Map<String, dynamic> json) {
    return SavedLocation(
      id: json['id'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      address: json['address'],
      locationType: json['location_type'],
      usageCount: json['usage_count'],
      lastUsedAt: DateTime.parse(json['last_used_at']),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'location_type': locationType,
      'usage_count': usageCount,
      'last_used_at': lastUsedAt.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  custom.LatLng get coordinates => custom.LatLng(latitude, longitude);
}
