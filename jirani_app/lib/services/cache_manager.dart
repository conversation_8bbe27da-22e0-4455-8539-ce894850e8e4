import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Cache manager for handling data caching
class AppCacheManager {
  /// Default cache manager instance
  static final DefaultCacheManager _cacheManager = DefaultCacheManager();

  /// Cache duration for API responses
  static const Duration _defaultCacheDuration = Duration(hours: 1);

  /// Cache key prefix for API responses
  static const String _apiCacheKeyPrefix = 'api_cache_';

  /// Cache key prefix for user data
  static const String _userDataKeyPrefix = 'user_data_';

  /// Cache API response
  static Future<void> cacheApiResponse(String endpoint, dynamic data,
      {Duration? duration}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _apiCacheKeyPrefix + endpoint;
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': DateTime.now()
            .add(duration ?? _defaultCacheDuration)
            .millisecondsSinceEpoch,
      };

      await prefs.setString(cacheKey, jsonEncode(cacheData));

      if (kDebugMode) {
        print('Cached API response for $endpoint');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching API response: $e');
      }
    }
  }

  /// Get cached API response
  static Future<dynamic> getCachedApiResponse(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _apiCacheKeyPrefix + endpoint;

      if (!prefs.containsKey(cacheKey)) {
        return null;
      }

      final cacheDataString = prefs.getString(cacheKey);
      if (cacheDataString == null) {
        return null;
      }

      final cacheData = jsonDecode(cacheDataString);
      final expiry = cacheData['expiry'] as int;

      // Check if cache is expired
      if (DateTime.now().millisecondsSinceEpoch > expiry) {
        await prefs.remove(cacheKey);
        return null;
      }

      return cacheData['data'];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached API response: $e');
      }
      return null;
    }
  }

  /// Clear API response cache
  static Future<void> clearApiCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_apiCacheKeyPrefix)) {
          await prefs.remove(key);
        }
      }

      if (kDebugMode) {
        print('Cleared API cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing API cache: $e');
      }
    }
  }

  /// Clear specific API response cache
  static Future<void> clearApiCacheForEndpoint(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _apiCacheKeyPrefix + endpoint;

      if (prefs.containsKey(cacheKey)) {
        await prefs.remove(cacheKey);

        if (kDebugMode) {
          print('Cleared API cache for $endpoint');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing API cache for endpoint: $e');
      }
    }
  }

  /// Cache file
  static Future<void> cacheFile(String url, {String? key}) async {
    try {
      await _cacheManager.downloadFile(url, key: key);

      if (kDebugMode) {
        print('Cached file: $url');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching file: $e');
      }
    }
  }

  /// Get cached file
  static Future<FileInfo?> getCachedFile(String url, {String? key}) async {
    try {
      return await _cacheManager.getFileFromCache(key ?? url);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached file: $e');
      }
      return null;
    }
  }

  /// Clear file cache
  static Future<void> clearFileCache() async {
    try {
      await _cacheManager.emptyCache();

      if (kDebugMode) {
        print('Cleared file cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing file cache: $e');
      }
    }
  }

  /// Cache user data
  static Future<void> cacheUserData(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _userDataKeyPrefix + key;

      await prefs.setString(cacheKey, jsonEncode(data));

      if (kDebugMode) {
        print('Cached user data for $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching user data: $e');
      }
    }
  }

  /// Get cached user data
  static Future<dynamic> getCachedUserData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _userDataKeyPrefix + key;

      if (!prefs.containsKey(cacheKey)) {
        return null;
      }

      final cacheDataString = prefs.getString(cacheKey);
      if (cacheDataString == null) {
        return null;
      }

      return jsonDecode(cacheDataString);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached user data: $e');
      }
      return null;
    }
  }

  /// Clear user data cache
  static Future<void> clearUserDataCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_userDataKeyPrefix)) {
          await prefs.remove(key);
        }
      }

      if (kDebugMode) {
        print('Cleared user data cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing user data cache: $e');
      }
    }
  }
}
