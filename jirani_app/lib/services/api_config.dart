import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

/// API configuration service for handling different platforms
class ApiConfig {
  /// Production API URL
  static const String _productionApiUrl =
      'https://api.jirani.tufiked.live/api/v1';

  /// Development API URL (for local testing)
  static const String _developmentApiUrl = 'http://localhost:8080/api/v1';

  /// Android emulator API URL
  static const String _androidEmulatorApiUrl = 'http://********:8080/api/v1';

  /// Flag to force using production API
  static const bool _forceProductionApi = true;

  /// Get the base URL for the API based on the platform
  static String getBaseUrl() {
    // Always use production API if forced
    if (_forceProductionApi) {
      return _productionApiUrl;
    }

    // For web platform
    if (kIsWeb) {
      // When running on web, use the local backend
      return _developmentApiUrl;
    }

    // For mobile platforms
    if (Platform.isAndroid) {
      // Android emulator needs special IP to access host machine
      return _androidEmulatorApiUrl;
    } else if (Platform.isIOS) {
      // iOS simulator can use localhost
      return _developmentApiUrl;
    } else if (Platform.isLinux || Platform.isMacOS || Platform.isWindows) {
      // Desktop platforms can use localhost
      return _developmentApiUrl;
    }

    // Default to production URL for other platforms or release builds
    return _productionApiUrl;
  }

  /// Get the base URL for the API with environment consideration
  static String getEnvironmentBaseUrl({bool isProduction = false}) {
    if (isProduction || _forceProductionApi) {
      return _productionApiUrl;
    }

    return getBaseUrl();
  }
}
