import 'dart:async';
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../services/logging_service.dart';
import '../services/websocket_service.dart';
import '../services/enhanced_location_service.dart';

/// Comprehensive error handling and recovery service for production
class ErrorRecoveryService {
  static final ErrorRecoveryService _instance =
      ErrorRecoveryService._internal();
  factory ErrorRecoveryService() => _instance;
  ErrorRecoveryService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  final WebSocketService _webSocketService = WebSocketService();
  final EnhancedLocationService _locationService = EnhancedLocationService();

  // Error tracking
  final Map<String, int> _errorCounts = {};
  final Map<String, DateTime> _lastErrorTimes = {};
  final List<ErrorReport> _errorHistory = [];

  // Recovery state
  bool _isRecovering = false;
  Timer? _healthCheckTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  static const int _maxErrorsPerMinute = 10;
  static const Duration _errorResetDuration = Duration(minutes: 1);
  static const Duration _healthCheckInterval = Duration(seconds: 30);

  /// Initialize error recovery service
  Future<void> initialize() async {
    try {
      // Start connectivity monitoring
      _startConnectivityMonitoring();

      // Start periodic health checks
      _startHealthChecks();

      LoggingService.i('Error Recovery Service initialized');
    } catch (e) {
      LoggingService.e('Error Recovery Service initialization failed',
          error: e);
    }
  }

  /// Handle and recover from errors
  Future<void> handleError(
    String errorType,
    dynamic error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool shouldRecover = true,
  }) async {
    try {
      // Track error frequency
      final errorKey = errorType;
      final now = DateTime.now();

      _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;
      _lastErrorTimes[errorKey] = now;

      // Create error report
      final errorReport = ErrorReport(
        type: errorType,
        message: error.toString(),
        stackTrace: stackTrace?.toString(),
        context: context,
        timestamp: now,
      );

      _errorHistory.add(errorReport);

      // Log error
      LoggingService.e(
        'Error handled: $errorType',
        error: error,
        stackTrace: stackTrace,
      );

      // Check if error rate is too high
      if (_isErrorRateTooHigh(errorKey)) {
        LoggingService.w(
            'Error rate too high for $errorType, throttling recovery');
        return;
      }

      // Attempt recovery if enabled
      if (shouldRecover && !_isRecovering) {
        await _attemptRecovery(errorType, error, context);
      }

      // Store error report for analysis
      await _storeErrorReport(errorReport);
    } catch (e) {
      LoggingService.e('Error in error handler', error: e);
    }
  }

  /// Check if error rate is too high
  bool _isErrorRateTooHigh(String errorKey) {
    final lastErrorTime = _lastErrorTimes[errorKey];
    if (lastErrorTime == null) return false;

    final timeSinceLastError = DateTime.now().difference(lastErrorTime);
    if (timeSinceLastError > _errorResetDuration) {
      _errorCounts[errorKey] = 0;
      return false;
    }

    return (_errorCounts[errorKey] ?? 0) > _maxErrorsPerMinute;
  }

  /// Attempt to recover from specific error types
  Future<void> _attemptRecovery(
    String errorType,
    dynamic error,
    Map<String, dynamic>? context,
  ) async {
    if (_isRecovering) return;

    _isRecovering = true;
    LoggingService.i('Attempting recovery for error: $errorType');

    try {
      switch (errorType) {
        case 'websocket_connection':
          await _recoverWebSocketConnection();
          break;
        case 'location_service':
          await _recoverLocationService();
          break;
        case 'authentication':
          await _recoverAuthentication();
          break;
        case 'network_connectivity':
          await _recoverNetworkConnectivity();
          break;
        default:
          LoggingService.w('No recovery strategy for error type: $errorType');
      }

      LoggingService.i('Recovery attempt completed for: $errorType');
    } catch (e) {
      LoggingService.e('Recovery failed for $errorType', error: e);
    } finally {
      _isRecovering = false;
    }
  }

  /// Recover WebSocket connection
  Future<void> _recoverWebSocketConnection() async {
    try {
      LoggingService.i('Recovering WebSocket connection...');

      // Disconnect and reconnect with retry
      _webSocketService.disconnect();
      await Future.delayed(const Duration(seconds: 2));
      await _webSocketService.connectWithRetry(maxRetries: 3);

      LoggingService.i('WebSocket connection recovered');
    } catch (e) {
      LoggingService.e('WebSocket recovery failed', error: e);
      rethrow;
    }
  }

  /// Recover location service
  Future<void> _recoverLocationService() async {
    try {
      LoggingService.i('Recovering location service...');

      // Reinitialize location service
      await _locationService.initialize();

      LoggingService.i('Location service recovered');
    } catch (e) {
      LoggingService.e('Location service recovery failed', error: e);
      rethrow;
    }
  }

  /// Recover authentication
  Future<void> _recoverAuthentication() async {
    try {
      LoggingService.i('Recovering authentication...');

      // Check if token exists and is valid
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        LoggingService.w('No auth token found, user needs to re-login');
        return;
      }

      // Validate token with backend
      // This would typically involve making an API call to validate the token
      LoggingService.i('Authentication recovery completed');
    } catch (e) {
      LoggingService.e('Authentication recovery failed', error: e);
      rethrow;
    }
  }

  /// Recover network connectivity
  Future<void> _recoverNetworkConnectivity() async {
    try {
      LoggingService.i('Recovering network connectivity...');

      // Wait for network to become available
      final connectivity = Connectivity();
      final results = await connectivity.checkConnectivity();

      if (results.contains(ConnectivityResult.none) || results.isEmpty) {
        LoggingService.w('No network connectivity available');
        return;
      }

      // Reconnect services that depend on network
      await _recoverWebSocketConnection();

      LoggingService.i('Network connectivity recovered');
    } catch (e) {
      LoggingService.e('Network connectivity recovery failed', error: e);
      rethrow;
    }
  }

  /// Start connectivity monitoring
  void _startConnectivityMonitoring() {
    final connectivity = Connectivity();
    _connectivitySubscription = connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        if (results.contains(ConnectivityResult.none) || results.isEmpty) {
          handleError('network_connectivity', 'Network connection lost');
        } else {
          LoggingService.i('Network connectivity restored: $results');
          // Attempt to recover services
          _attemptRecovery('network_connectivity', null, null);
        }
      },
    );
  }

  /// Start periodic health checks
  void _startHealthChecks() {
    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (timer) async {
      await _performHealthCheck();
    });
  }

  /// Perform system health check
  Future<void> _performHealthCheck() async {
    try {
      // Check WebSocket connection
      if (!_webSocketService.isConnected) {
        handleError('websocket_connection',
            'WebSocket disconnected during health check');
      }

      // Check location service
      try {
        await _locationService.getCurrentLocation(
            timeout: const Duration(seconds: 5));
      } catch (e) {
        handleError(
            'location_service', 'Location service failed during health check',
            shouldRecover: false);
      }

      LoggingService.d('Health check completed successfully');
    } catch (e) {
      LoggingService.e('Health check failed', error: e);
    }
  }

  /// Store error report for analysis
  Future<void> _storeErrorReport(ErrorReport report) async {
    try {
      final reportJson = jsonEncode(report.toJson());
      await _storage.write(
        key: 'error_report_${report.timestamp.millisecondsSinceEpoch}',
        value: reportJson,
      );

      // Keep only last 100 error reports
      if (_errorHistory.length > 100) {
        _errorHistory.removeAt(0);
      }
    } catch (e) {
      LoggingService.e('Failed to store error report', error: e);
    }
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStatistics() {
    final now = DateTime.now();
    final recentErrors = _errorHistory
        .where(
          (error) => now.difference(error.timestamp) < const Duration(hours: 1),
        )
        .toList();

    return {
      'total_errors': _errorHistory.length,
      'recent_errors_1h': recentErrors.length,
      'error_types': _errorCounts,
      'is_recovering': _isRecovering,
      'last_health_check': now.toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    _healthCheckTimer?.cancel();
    _connectivitySubscription?.cancel();
  }
}

/// Error report model
class ErrorReport {
  final String type;
  final String message;
  final String? stackTrace;
  final Map<String, dynamic>? context;
  final DateTime timestamp;

  ErrorReport({
    required this.type,
    required this.message,
    this.stackTrace,
    this.context,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'message': message,
      'stack_trace': stackTrace,
      'context': context,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
