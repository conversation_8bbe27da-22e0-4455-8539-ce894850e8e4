import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:location/location.dart' as loc;
import 'package:geocoding/geocoding.dart';
import '../models/lat_lng.dart' as custom;

/// Service for handling location-related functionality
class LocationService {
  // Singleton instance
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // Location instance
  final loc.Location _location = loc.Location();

  // Stream controller for location updates
  final _locationController = StreamController<loc.LocationData>.broadcast();
  Stream<loc.LocationData> get locationStream => _locationController.stream;

  // Subscription for location updates
  StreamSubscription<loc.LocationData>? _locationSubscription;

  // Callback function for location updates
  Function(loc.LocationData)? _onLocationUpdate;

  /// Initialize the location service
  Future<void> initialize() async {
    // Check if location services are enabled
    bool serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled.');
      }
    }

    // Check for location permissions
    loc.PermissionStatus permissionStatus = await _location.hasPermission();
    if (permissionStatus == loc.PermissionStatus.denied) {
      permissionStatus = await _location.requestPermission();
      if (permissionStatus == loc.PermissionStatus.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permissionStatus == loc.PermissionStatus.deniedForever) {
      throw Exception(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // Configure location settings
    await _location.changeSettings(
      accuracy: loc.LocationAccuracy.high,
      interval: 5000, // Update interval in milliseconds
      distanceFilter: 10, // Minimum distance (in meters) to trigger updates
    );
  }

  /// Start tracking location
  Future<void> startLocationTracking({Function(loc.LocationData)? onLocationUpdate}) async {
    _onLocationUpdate = onLocationUpdate;

    _locationSubscription = _location.onLocationChanged.listen((loc.LocationData locationData) {
      // Add to stream
      _locationController.add(locationData);

      // Call callback if set
      _onLocationUpdate?.call(locationData);

      if (kDebugMode) {
        print('Location update: ${locationData.latitude}, ${locationData.longitude}');
      }
    });
  }

  /// Stop tracking location
  Future<void> stopLocationTracking() async {
    await _locationSubscription?.cancel();
    _locationSubscription = null;
    _onLocationUpdate = null;
  }

  /// Get the current position
  Future<loc.LocationData> getCurrentPosition() async {
    return await _location.getLocation();
  }

  /// Get the last known position
  Future<loc.LocationData?> getLastKnownPosition() async {
    try {
      return await _location.getLocation();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting last known position: $e');
      }
      return null;
    }
  }

  /// Get address from coordinates
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        return '${place.street}, ${place.locality}, ${place.postalCode}, ${place.country}';
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting address: $e');
      }
      return null;
    }
  }

  /// Get coordinates from address
  Future<custom.LatLng?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        return custom.LatLng(
          locations.first.latitude,
          locations.first.longitude,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting coordinates: $e');
      }
      return null;
    }
  }

  /// Calculate distance between two points
  double calculateDistance(custom.LatLng start, custom.LatLng end) {
    // Haversine formula to calculate distance between two points
    const double earthRadius = 6371000; // in meters
    double lat1Rad = _degreesToRadians(start.latitude);
    double lat2Rad = _degreesToRadians(end.latitude);
    double deltaLatRad = _degreesToRadians(end.latitude - start.latitude);
    double deltaLonRad = _degreesToRadians(end.longitude - start.longitude);

    double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLonRad / 2) * sin(deltaLonRad / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Dispose the location service
  void dispose() {
    _locationSubscription?.cancel();
    _locationController.close();
  }
}
