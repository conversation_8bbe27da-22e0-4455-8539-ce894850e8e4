import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/lat_lng.dart' as custom;
import '../models/ride.dart';
import 'mapbox_service_new.dart';
import 'api_config.dart';

/// Service for managing Boda Boda rides
class RideService {
  /// Creates a new ride request
  static Future<Ride?> createRideRequest({
    required String userId,
    required RideLocation pickup,
    required RideLocation dropoff,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      // Get authentication token
      const storage = FlutterSecureStorage();
      final token = await storage.read(key: 'auth_token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Prepare request body for backend API
      final requestBody = {
        'pickup_address': pickup.address,
        'pickup_latitude': pickup.latitude,
        'pickup_longitude': pickup.longitude,
        'dropoff_address': dropoff.address,
        'dropoff_latitude': dropoff.latitude,
        'dropoff_longitude': dropoff.longitude,
        'notes': '', // Optional notes
      };

      // Make API call to backend
      final baseUrl = ApiConfig.getBaseUrl();
      final response = await http.post(
        Uri.parse('$baseUrl/boda/rides'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        final rideData = responseData['ride'];

        // Convert backend response to Ride model
        return Ride(
          id: rideData['id'].toString(),
          userId: rideData['user_id'],
          pickup: pickup,
          dropoff: dropoff,
          status: _mapBackendStatusToRideStatus(rideData['status']),
          estimatedFare: (rideData['estimated_fare'] as num).toDouble(),
          estimatedDistance: (rideData['distance'] as num).toDouble(),
          estimatedDuration: rideData['duration'],
          paymentMethod: paymentMethod,
          requestedAt: DateTime.parse(rideData['created_at']),
        );
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception('Failed to create ride: ${errorData['error']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating ride request: $e');
      }
      return null;
    }
  }

  /// Maps backend ride status to frontend RideStatus enum
  static RideStatus _mapBackendStatusToRideStatus(String backendStatus) {
    switch (backendStatus) {
      case 'requested':
        return RideStatus.requested;
      case 'accepted':
        return RideStatus.accepted;
      case 'en_route_to_pickup':
        return RideStatus.enRouteToPickup;
      case 'arrived_at_pickup':
        return RideStatus.arrivedAtPickup;
      case 'in_progress':
        return RideStatus.inProgress;
      case 'completed':
        return RideStatus.completed;
      case 'cancelled':
        return RideStatus.cancelled;
      default:
        return RideStatus.requested;
    }
  }

  /// Gets a ride by ID
  static Future<Ride?> getRideById(String id) async {
    try {
      // Get authentication token
      const storage = FlutterSecureStorage();
      final token = await storage.read(key: 'auth_token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Make API call to backend
      final baseUrl = ApiConfig.getBaseUrl();
      final response = await http.get(
        Uri.parse('$baseUrl/boda/rides/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final rideData = responseData['ride'];

        // Convert backend response to Ride model
        return Ride(
          id: rideData['id'].toString(),
          userId: rideData['user_id'],
          riderId: rideData['driver_id']?.toString(),
          pickup: RideLocation(
            latitude: (rideData['pickup_latitude'] as num).toDouble(),
            longitude: (rideData['pickup_longitude'] as num).toDouble(),
            address: rideData['pickup_address'],
            name: rideData['pickup_address'], // Use address as name for now
          ),
          dropoff: RideLocation(
            latitude: (rideData['dropoff_latitude'] as num).toDouble(),
            longitude: (rideData['dropoff_longitude'] as num).toDouble(),
            address: rideData['dropoff_address'],
            name: rideData['dropoff_address'], // Use address as name for now
          ),
          status: _mapBackendStatusToRideStatus(rideData['status']),
          estimatedFare: (rideData['estimated_fare'] as num).toDouble(),
          actualFare: rideData['actual_fare'] != null
              ? (rideData['actual_fare'] as num).toDouble()
              : null,
          estimatedDistance: (rideData['distance'] as num).toDouble(),
          estimatedDuration: rideData['duration'],
          paymentMethod: PaymentMethod.mobileMoney, // Default for now
          isPaid: rideData['payment_status'] == 'paid',
          rating: rideData['rating'],
          review: rideData['review'],
          requestedAt: DateTime.parse(rideData['created_at']),
          acceptedAt: rideData['accepted_at'] != null
              ? DateTime.parse(rideData['accepted_at'])
              : null,
          startedAt: rideData['started_at'] != null
              ? DateTime.parse(rideData['started_at'])
              : null,
          completedAt: rideData['completed_at'] != null
              ? DateTime.parse(rideData['completed_at'])
              : null,
        );
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception('Failed to get ride: ${errorData['error']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting ride by ID: $e');
      }
      return null;
    }
  }

  /// Gets the user's ride history
  static Future<List<Ride>> getUserRideHistory(String userId) async {
    try {
      // Get authentication token
      const storage = FlutterSecureStorage();
      final token = await storage.read(key: 'auth_token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Make API call to backend
      final baseUrl = ApiConfig.getBaseUrl();
      final response = await http.get(
        Uri.parse('$baseUrl/boda/rides'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final ridesData = responseData['rides'] as List;

        // Convert backend response to Ride models
        return ridesData.map((rideData) {
          return Ride(
            id: rideData['id'].toString(),
            userId: rideData['user_id'],
            riderId: rideData['driver_id']?.toString(),
            pickup: RideLocation(
              latitude: (rideData['pickup_latitude'] as num).toDouble(),
              longitude: (rideData['pickup_longitude'] as num).toDouble(),
              address: rideData['pickup_address'],
              name: rideData['pickup_address'], // Use address as name for now
            ),
            dropoff: RideLocation(
              latitude: (rideData['dropoff_latitude'] as num).toDouble(),
              longitude: (rideData['dropoff_longitude'] as num).toDouble(),
              address: rideData['dropoff_address'],
              name: rideData['dropoff_address'], // Use address as name for now
            ),
            status: _mapBackendStatusToRideStatus(rideData['status']),
            estimatedFare: (rideData['estimated_fare'] as num).toDouble(),
            actualFare: rideData['actual_fare'] != null
                ? (rideData['actual_fare'] as num).toDouble()
                : null,
            estimatedDistance: (rideData['distance'] as num).toDouble(),
            estimatedDuration: rideData['duration'],
            paymentMethod: PaymentMethod.mobileMoney, // Default for now
            isPaid: rideData['payment_status'] == 'paid',
            rating: rideData['rating'],
            review: rideData['review'],
            requestedAt: DateTime.parse(rideData['created_at']),
            acceptedAt: rideData['accepted_at'] != null
                ? DateTime.parse(rideData['accepted_at'])
                : null,
            startedAt: rideData['started_at'] != null
                ? DateTime.parse(rideData['started_at'])
                : null,
            completedAt: rideData['completed_at'] != null
                ? DateTime.parse(rideData['completed_at'])
                : null,
          );
        }).toList();
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception('Failed to get ride history: ${errorData['error']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user ride history: $e');
      }
      return [];
    }
  }

  /// Updates a ride's status
  static Future<Ride?> updateRideStatus({
    required String rideId,
    required RideStatus status,
  }) async {
    try {
      // Get the current ride
      final ride = await getRideById(rideId);

      if (ride == null) {
        throw Exception('Ride not found');
      }

      // Update the ride status
      final updatedRide = ride.copyWith(
        status: status,
        acceptedAt:
            status == RideStatus.accepted ? DateTime.now() : ride.acceptedAt,
        startedAt:
            status == RideStatus.inProgress ? DateTime.now() : ride.startedAt,
        completedAt:
            status == RideStatus.completed ? DateTime.now() : ride.completedAt,
      );

      // In a real app, this would save the updated ride to a database
      // For now, we'll just return the updated ride

      return updatedRide;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating ride status: $e');
      }
      return null;
    }
  }

  /// Rates a completed ride
  static Future<bool> rateRide({
    required String rideId,
    required int rating,
    String? review,
  }) async {
    try {
      // Get the current ride
      final ride = await getRideById(rideId);

      if (ride == null) {
        throw Exception('Ride not found');
      }

      if (ride.status != RideStatus.completed) {
        throw Exception('Cannot rate a ride that is not completed');
      }

      // Update the ride with the rating and review
      // In a real app, we would save the updated ride to a database
      // ride.copyWith(rating: rating, review: review);

      // In a real app, this would save the updated ride to a database
      // For now, we'll just return true to indicate success

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error rating ride: $e');
      }
      return false;
    }
  }

  /// Gets estimated fare for a ride
  static Future<double?> getEstimatedFare({
    required custom.LatLng pickup,
    required custom.LatLng dropoff,
  }) async {
    try {
      // Convert to Mapbox Points
      final startPoint =
          MapboxServiceNew.latLngToPoint(pickup.latitude, pickup.longitude);
      final endPoint =
          MapboxServiceNew.latLngToPoint(dropoff.latitude, dropoff.longitude);

      // Get directions
      final directions =
          await MapboxServiceNew.getDirections(startPoint, endPoint);

      if (directions['routes'] == null ||
          (directions['routes'] as List).isEmpty) {
        throw Exception('Failed to calculate distance and duration');
      }

      // Extract distance and duration from the first route
      final route = directions['routes'][0];
      final estimatedDistance = (route['distance'] as num).toDouble() /
          1000; // Convert meters to kilometers
      final estimatedDuration = ((route['duration'] as num).toDouble() / 60)
          .round(); // Convert seconds to minutes

      // Calculate fare
      final estimatedFare = MapboxServiceNew.calculateFare(
        estimatedDistance,
        estimatedDuration.toDouble(),
      );

      return estimatedFare;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting estimated fare: $e');
      }
      return null;
    }
  }
}
