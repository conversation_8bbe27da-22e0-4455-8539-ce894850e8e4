import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/lat_lng.dart' as custom;
import '../models/rider.dart';

/// Service for managing Boda Boda riders
class RiderService {
  /// Gets a list of nearby riders
  static Future<List<Rider>> getNearbyRiders({
    required custom.LatLng userLocation,
    double radiusInKm = 5.0,
  }) async {
    // In a real app, this would make an API call to get nearby riders
    // For now, we'll generate mock data

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Generate random riders around the user's location
      final riders = _generateMockRiders(
        centerLocation: userLocation,
        radiusInKm: radiusInKm,
        count: 10,
      );

      return riders;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting nearby riders: $e');
      }
      return [];
    }
  }

  /// Gets a rider by ID
  static Future<Rider?> getRiderById(String id) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, this would make an API call to get the rider
      // For now, we'll return a mock rider if the ID matches one of our mock riders
      final mockRiders = _generateMockRiders(
        centerLocation: const custom.LatLng(0, 0), // Doesn't matter for this method
        radiusInKm: 5.0,
        count: 10,
      );

      return mockRiders.firstWhere(
        (rider) => rider.id == id,
        orElse: () => throw Exception('Rider not found'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting rider by ID: $e');
      }
      return null;
    }
  }

  /// Calculates the estimated time of arrival for a rider to reach a location
  static Future<int> calculateEta({
    required RiderLocation riderLocation,
    required custom.LatLng destination,
    double averageSpeedKmh = 30.0, // Average speed in km/h
  }) async {
    try {
      // Calculate distance between rider and destination
      final distance = calculateDistance(
        lat1: riderLocation.latitude,
        lon1: riderLocation.longitude,
        lat2: destination.latitude,
        lon2: destination.longitude,
      );

      // Calculate ETA in minutes
      // distance (km) / speed (km/h) * 60 = time (minutes)
      final etaMinutes = (distance / averageSpeedKmh * 60).round();

      return etaMinutes;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating ETA: $e');
      }
      return 0;
    }
  }

  /// Generates mock riders around a center location
  static List<Rider> _generateMockRiders({
    required custom.LatLng centerLocation,
    required double radiusInKm,
    required int count,
  }) {
    final random = Random();
    final riders = <Rider>[];

    for (var i = 0; i < count; i++) {
      // Generate random coordinates within the radius
      final randomDistance = random.nextDouble() * radiusInKm;
      final randomAngle = random.nextDouble() * 2 * pi;

      // Convert polar coordinates to Cartesian
      // Earth's radius at equator is approximately 6378.1 km
      // 1 degree of latitude is approximately 111.32 km
      // 1 degree of longitude varies with latitude
      final latOffset = randomDistance / 111.32 * cos(randomAngle);
      final lonOffset = randomDistance / (111.32 * cos(centerLocation.latitude * pi / 180)) * sin(randomAngle);

      final latitude = centerLocation.latitude + latOffset;
      final longitude = centerLocation.longitude + lonOffset;

      // Generate random heading (0-360 degrees)
      final heading = random.nextDouble() * 360;

      // Generate random speed (0-40 km/h converted to m/s)
      final speed = random.nextDouble() * 40 / 3.6;

      // Create a rider with random attributes
      final rider = Rider(
        id: 'rider_${i + 1}',
        name: _getRandomName(),
        phoneNumber: '+254${700000000 + random.nextInt(99999999)}',
        email: 'rider${i + 1}@example.com',
        imageUrl: 'https://randomuser.me/api/portraits/men/${random.nextInt(100)}.jpg',
        location: RiderLocation(
          latitude: latitude,
          longitude: longitude,
          heading: heading,
          speed: speed,
          timestamp: DateTime.now(),
        ),
        status: random.nextDouble() < 0.7
            ? RiderStatus.available
            : (random.nextDouble() < 0.5 ? RiderStatus.busy : RiderStatus.offline),
        rating: 3.5 + random.nextDouble() * 1.5, // Random rating between 3.5 and 5.0
        ridesCompleted: random.nextInt(500) + 50, // Random rides between 50 and 549
        reviewCount: random.nextInt(100) + 10, // Random reviews between 10 and 109
        licensePlate: 'K${random.nextInt(999) + 100}${String.fromCharCode(65 + random.nextInt(26))}${String.fromCharCode(65 + random.nextInt(26))}',
        motorcycleModel: _getRandomMotorcycleModel(),
        motorcycleColor: _getRandomColor(),
        isVerified: random.nextBool(),
        joinedDate: DateTime.now().subtract(Duration(days: random.nextInt(365) + 30)),
      );

      riders.add(rider);
    }

    return riders;
  }

  /// Calculates the distance between two coordinates using the Haversine formula
  static double calculateDistance({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    const earthRadiusKm = 6371.0;

    final dLat = degreesToRadians(lat2 - lat1);
    final dLon = degreesToRadians(lon2 - lon1);

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(degreesToRadians(lat1)) * cos(degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);

    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadiusKm * c;
  }

  /// Converts degrees to radians
  static double degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  /// Gets a random name for a mock rider
  static String _getRandomName() {
    final firstNames = [
      'John', 'James', 'David', 'Michael', 'Robert', 'William', 'Joseph',
      'Daniel', 'Matthew', 'Anthony', 'Mark', 'Paul', 'Steven', 'George',
      'Kenneth', 'Andrew', 'Edward', 'Brian', 'Joshua', 'Kevin',
    ];

    final lastNames = [
      'Smith', 'Johnson', 'Williams', 'Jones', 'Brown', 'Davis', 'Miller',
      'Wilson', 'Moore', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White',
      'Harris', 'Martin', 'Thompson', 'Garcia', 'Martinez', 'Robinson',
    ];

    final random = Random();
    final firstName = firstNames[random.nextInt(firstNames.length)];
    final lastName = lastNames[random.nextInt(lastNames.length)];

    return '$firstName $lastName';
  }

  /// Gets a random motorcycle model for a mock rider
  static String _getRandomMotorcycleModel() {
    final models = [
      'Honda CG 125',
      'Bajaj Boxer 150',
      'TVS Star 100',
      'Yamaha Crux',
      'Hero Splendor',
      'Suzuki GN 125',
      'Haojue HJ125-7',
      'Boxer BM 150',
      'TVS Apache 180',
      'Yamaha YBR 125',
    ];

    final random = Random();
    return models[random.nextInt(models.length)];
  }

  /// Gets a random color for a mock rider's motorcycle
  static String _getRandomColor() {
    final colors = [
      'Red', 'Blue', 'Black', 'White', 'Silver', 'Green',
      'Yellow', 'Orange', 'Grey', 'Brown',
    ];

    final random = Random();
    return colors[random.nextInt(colors.length)];
  }
}
