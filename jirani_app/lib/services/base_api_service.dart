import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:jirani_app/providers/message_provider.dart';
import 'api_config.dart';
import 'cache_manager.dart';

final baseApiServiceProvider = Provider<BaseApiService>((ref) {
  return BaseApiService(ref);
});

/// Base API service for handling HTTP requests
class BaseApiService {
  final Ref _ref;

  /// Get the base URL for the API
  String get baseUrl => ApiConfig.getBaseUrl();

  /// Default cache duration
  static const Duration defaultCacheDuration = Duration(hours: 1);

  /// Whether to use caching
  final bool useCaching;

  /// Creates a new instance of [BaseApiService]
  BaseApiService(this._ref, {this.useCaching = true});

  /// Make a GET request to the API
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
    String? token,
    bool useCache = true,
    Duration? cacheDuration,
  }) async {
    try {
      // Try to get from cache first if caching is enabled
      if (useCaching && useCache) {
        final cachedData = await AppCacheManager.getCachedApiResponse(endpoint);
        if (cachedData != null) {
          if (kDebugMode) {
            print('Using cached data for $endpoint');
          }
          return cachedData;
        }
      }

      // Make the network request
      final response = await http.get(
        Uri.parse('$baseUrl/$endpoint'),
        headers: _buildHeaders(headers, token),
      );

      final data = _handleResponse(response, endpoint);

      // Cache the response if caching is enabled
      if (useCaching && useCache) {
        await AppCacheManager.cacheApiResponse(
          endpoint,
          data,
          duration: cacheDuration ?? defaultCacheDuration,
        );
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('Error in GET request to $endpoint: $e');
      }

      // Try to get from cache if network request failed
      if (useCaching && useCache) {
        final cachedData = await AppCacheManager.getCachedApiResponse(endpoint);
        if (cachedData != null) {
          if (kDebugMode) {
            print('Using cached data for $endpoint after network error');
          }
          return cachedData;
        }
      }

      rethrow;
    }
  }

  /// Make a POST request to the API
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, String>? headers,
    String? token,
    dynamic body,
    bool invalidateCache = true,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: _buildHeaders(headers, token),
        body: body != null ? jsonEncode(body) : null,
      );

      final data = _handleResponse(response, endpoint);

      // Invalidate cache for this endpoint if needed
      if (useCaching && invalidateCache) {
        await AppCacheManager.clearApiCacheForEndpoint(endpoint);
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('Error in POST request to $endpoint: $e');
      }
      rethrow;
    }
  }

  /// Make a PUT request to the API
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, String>? headers,
    String? token,
    dynamic body,
    bool invalidateCache = true,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/$endpoint'),
        headers: _buildHeaders(headers, token),
        body: body != null ? jsonEncode(body) : null,
      );

      final data = _handleResponse(response, endpoint);

      // Invalidate cache for this endpoint if needed
      if (useCaching && invalidateCache) {
        await AppCacheManager.clearApiCacheForEndpoint(endpoint);
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('Error in PUT request to $endpoint: $e');
      }
      rethrow;
    }
  }

  /// Make a DELETE request to the API
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
    String? token,
    bool invalidateCache = true,
  }) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/$endpoint'),
        headers: _buildHeaders(headers, token),
      );

      final data = _handleResponse(response, endpoint);

      // Invalidate cache for this endpoint if needed
      if (useCaching && invalidateCache) {
        await AppCacheManager.clearApiCacheForEndpoint(endpoint);
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('Error in DELETE request to $endpoint: $e');
      }
      rethrow;
    }
  }

  /// Build headers for the request
  Map<String, String> _buildHeaders(
    Map<String, String>? headers,
    String? token,
  ) {
    final Map<String, String> defaultHeaders = {
      'Content-Type': 'application/json',
    };

    if (token != null) {
      defaultHeaders['Authorization'] = 'Bearer $token';
    }

    return {...defaultHeaders, ...?headers};
  }

  /// Handle the response from the API
  Map<String, dynamic> _handleResponse(http.Response response, String endpoint) {
    Map<String, dynamic> data;
    try {
      data = jsonDecode(response.body);
    } catch (e) {
      // If response body is not valid JSON (e.g. plain text error)
      _ref.read(messageProvider.notifier).showMessage(
        text: 'Received non-JSON response from server. Status: ${response.statusCode}',
        type: MessageType.error,
      );
      throw Exception('Server returned non-JSON response for $endpoint. Status: ${response.statusCode}');
    }
    
    final messageNotifier = _ref.read(messageProvider.notifier);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      // Attempt to get a success message from the response, otherwise use a generic one
      final successMessage = data['message'] as String? ?? data['success'] as String?;
      if (successMessage != null && successMessage.isNotEmpty) {
        messageNotifier.showMessage(text: successMessage, type: MessageType.success);
      }
      // Optionally, show a generic success message if no specific one is provided
      // else {
      //   messageNotifier.showMessage(text: 'Operation successful for $endpoint', type: MessageType.success);
      // }
      return data;
    } else {
      final errorMessage = data['error'] as String? ?? 
                           data['message'] as String? ?? 
                           'Request failed for $endpoint. Status: ${response.statusCode}';
      
      MessageType type = MessageType.error;
      if (response.statusCode == 401 || response.statusCode == 403) {
        type = MessageType.warning; // Or error, depending on desired UX for auth issues
      }
      
      messageNotifier.showMessage(text: errorMessage, type: type);
      throw Exception(errorMessage);
    }
  }
}
