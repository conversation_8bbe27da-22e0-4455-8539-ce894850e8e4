import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/websocket_message.dart';
import '../services/logging_service.dart';

/// WebSocket service for real-time communication
class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  WebSocketChannel? _channel;
  StreamController<WebSocketMessage>? _messageController;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  static const Duration _reconnectDelay = Duration(seconds: 5);

  // Advanced message handling
  final Map<String, StreamController<WebSocketMessage>> _messageRouters = {};
  final List<WebSocketMessage> _messageQueue = [];
  final Map<String, int> _messageRetryCount = {};
  static const int _maxRetryAttempts = 3;

  // Stream for incoming messages
  Stream<WebSocketMessage> get messageStream =>
      _messageController?.stream ?? const Stream.empty();

  // Connection status
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect() async {
    if (_isConnected) return;

    try {
      // Get real authenticated user ID from secure storage
      const storage = FlutterSecureStorage();
      final userId = await storage.read(key: 'user_id');
      if (userId == null || userId.isEmpty) {
        LoggingService.w('WebSocket: No authenticated user found');
        return;
      }

      final wsUrl = _getWebSocketUrl();
      final uri = Uri.parse('$wsUrl?user_id=$userId&role=user');

      LoggingService.i('WebSocket: Connecting to $uri');

      _channel = WebSocketChannel.connect(uri);
      _messageController = StreamController<WebSocketMessage>.broadcast();

      // Listen for messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      _startHeartbeat();

      // Process any queued messages
      _processQueuedMessages();

      LoggingService.i('WebSocket: Connected successfully');
    } catch (e) {
      LoggingService.e('WebSocket: Connection failed', error: e);
      _scheduleReconnect();
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    _shouldReconnect = false;
    _stopHeartbeat();
    _stopReconnectTimer();

    if (_channel != null) {
      await _channel!.sink.close(status.goingAway);
      _channel = null;
    }

    if (_messageController != null) {
      await _messageController!.close();
      _messageController = null;
    }

    _isConnected = false;
    LoggingService.i('WebSocket: Disconnected');
  }

  /// Send a message through WebSocket with retry capability
  void sendMessage(WebSocketMessage message, {bool isHighPriority = false}) {
    if (!_isConnected || _channel == null) {
      LoggingService.w('WebSocket: Cannot send message - not connected');
      // Queue message for retry when connection is restored
      _queueMessage(message);
      return;
    }

    try {
      final jsonMessage = jsonEncode(message.toJson());
      _channel!.sink.add(jsonMessage);
      LoggingService.i('WebSocket: Message sent: ${message.type}');

      // Remove from retry count if successful
      final messageId = _getMessageId(message);
      _messageRetryCount.remove(messageId);
    } catch (e) {
      LoggingService.e('WebSocket: Failed to send message', error: e);
      _handleSendFailure(message);
    }
  }

  /// Send a high-priority message (for critical updates)
  void sendHighPriorityMessage(WebSocketMessage message) {
    sendMessage(message, isHighPriority: true);
  }

  /// Queue message for retry when connection is restored
  void _queueMessage(WebSocketMessage message) {
    final messageId = _getMessageId(message);
    final retryCount = _messageRetryCount[messageId] ?? 0;

    if (retryCount < _maxRetryAttempts) {
      _messageQueue.add(message);
      _messageRetryCount[messageId] = retryCount + 1;
      LoggingService.i(
          'WebSocket: Message queued for retry (attempt ${retryCount + 1})');
    } else {
      LoggingService.w('WebSocket: Message dropped after max retry attempts');
      _messageRetryCount.remove(messageId);
    }
  }

  /// Handle message send failure
  void _handleSendFailure(WebSocketMessage message) {
    final messageId = _getMessageId(message);
    final retryCount = _messageRetryCount[messageId] ?? 0;

    if (retryCount < _maxRetryAttempts) {
      _messageRetryCount[messageId] = retryCount + 1;
      // Retry after a short delay
      Timer(const Duration(milliseconds: 500), () {
        sendMessage(message);
      });
    } else {
      LoggingService.e('WebSocket: Message failed after max retry attempts');
      _messageRetryCount.remove(messageId);
    }
  }

  /// Generate unique message ID for tracking
  String _getMessageId(WebSocketMessage message) {
    return '${message.type.value}_${message.timestamp}';
  }

  /// Process queued messages after reconnection
  void _processQueuedMessages() {
    if (_messageQueue.isEmpty) return;

    LoggingService.i(
        'WebSocket: Processing ${_messageQueue.length} queued messages');
    final messagesToSend = List<WebSocketMessage>.from(_messageQueue);
    _messageQueue.clear();

    for (final message in messagesToSend) {
      sendMessage(message);
    }
  }

  /// Send location update (for drivers)
  void sendLocationUpdate(double latitude, double longitude, {String? rideId}) {
    final message = WebSocketMessage(
      type: WebSocketMessageType.locationUpdate,
      data: {
        'latitude': latitude,
        'longitude': longitude,
        if (rideId != null) 'ride_id': rideId,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
    sendMessage(message);
  }

  /// Send chat message
  void sendChatMessage(String rideId, String messageText, String toUserId) {
    final message = WebSocketMessage(
      type: WebSocketMessageType.chat,
      data: {
        'ride_id': rideId,
        'message': messageText,
        'to': toUserId,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
    sendMessage(message);
  }

  /// Handle incoming messages with enhanced routing
  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> json = jsonDecode(data);
      final message = WebSocketMessage.fromJson(json);

      LoggingService.i('WebSocket: Message received: ${message.type}');

      // Route to main message stream
      _messageController?.add(message);

      // Route to specialized streams
      _routeMessage(message);
    } catch (e) {
      LoggingService.e('WebSocket: Failed to parse message', error: e);
    }
  }

  /// Handle WebSocket errors
  void _onError(error) {
    LoggingService.e('WebSocket: Error', error: error);
    _isConnected = false;
    _scheduleReconnect();
  }

  /// Handle WebSocket disconnection
  void _onDisconnected() {
    LoggingService.i('WebSocket: Disconnected');
    _isConnected = false;
    _stopHeartbeat();

    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_isConnected) {
        final heartbeat = WebSocketMessage(
          type: WebSocketMessageType.heartbeat,
          data: {'status': 'alive'},
          timestamp: DateTime.now().millisecondsSinceEpoch,
        );
        sendMessage(heartbeat);
      }
    });
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (!_shouldReconnect || _reconnectAttempts >= _maxReconnectAttempts) {
      LoggingService.w('WebSocket: Max reconnect attempts reached');
      return;
    }

    _stopReconnectTimer();
    _reconnectAttempts++;

    LoggingService.i(
        'WebSocket: Scheduling reconnect attempt $_reconnectAttempts');

    _reconnectTimer = Timer(_reconnectDelay, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  /// Stop reconnect timer
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Get WebSocket URL based on environment
  String _getWebSocketUrl() {
    if (kIsWeb) {
      // For web, use the same host as the current page
      return 'wss://api.jirani.tufiked.live/ws';
    } else if (Platform.isAndroid) {
      // For Android emulator, use ********
      return 'ws://********:8080/ws';
    } else if (Platform.isIOS) {
      // For iOS simulator, use localhost
      return 'ws://localhost:8080/ws';
    } else {
      // For other platforms, use production URL
      return 'wss://api.jirani.tufiked.live/ws';
    }
  }

  /// Create a specialized stream for specific message types
  Stream<WebSocketMessage> getMessageStream(WebSocketMessageType messageType) {
    final routerKey = messageType.value;

    if (!_messageRouters.containsKey(routerKey)) {
      _messageRouters[routerKey] =
          StreamController<WebSocketMessage>.broadcast();
    }

    return _messageRouters[routerKey]!.stream;
  }

  /// Create a stream for ride-specific messages
  Stream<WebSocketMessage> getRideMessageStream(String rideId) {
    final routerKey = 'ride_$rideId';

    if (!_messageRouters.containsKey(routerKey)) {
      _messageRouters[routerKey] =
          StreamController<WebSocketMessage>.broadcast();
    }

    return _messageRouters[routerKey]!.stream;
  }

  /// Route message to appropriate specialized streams
  void _routeMessage(WebSocketMessage message) {
    // Route by message type
    final typeRouterKey = message.type.value;
    if (_messageRouters.containsKey(typeRouterKey)) {
      _messageRouters[typeRouterKey]!.add(message);
    }

    // Route by ride ID if available
    if (message.rideId != null) {
      final rideRouterKey = 'ride_${message.rideId}';
      if (_messageRouters.containsKey(rideRouterKey)) {
        _messageRouters[rideRouterKey]!.add(message);
      }
    }

    // Route by user ID if available
    if (message.userId != null) {
      final userRouterKey = 'user_${message.userId}';
      if (_messageRouters.containsKey(userRouterKey)) {
        _messageRouters[userRouterKey]!.add(message);
      }
    }
  }

  /// Get connection statistics
  Map<String, dynamic> getConnectionStats() {
    return {
      'isConnected': _isConnected,
      'reconnectAttempts': _reconnectAttempts,
      'queuedMessages': _messageQueue.length,
      'activeRouters': _messageRouters.length,
      'retryingMessages': _messageRetryCount.length,
    };
  }

  /// Dispose resources
  void dispose() {
    // Close all message routers
    for (final router in _messageRouters.values) {
      router.close();
    }
    _messageRouters.clear();

    // Clear queues and retry counts
    _messageQueue.clear();
    _messageRetryCount.clear();

    disconnect();
  }
}
