import 'dart:convert';
import 'dart:math' show cos, pi;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import '../models/lat_lng.dart' as custom;
import '../models/place_result.dart';

/// Service for interacting with Mapbox APIs using the official Mapbox SDK
class MapboxServiceNew {
  /// Mapbox access token
  static const String accessToken =
      '********************************************************************************************';

  /// Base URL for Mapbox API
  static const String baseUrl = 'https://api.mapbox.com';

  /// Custom map style URL - can be created in Mapbox Studio
  static const String customMapStyle =
      'mapbox://styles/mapbox/navigation-night-v1';

  /// Creates a map view widget with enhanced features
  static Widget createMapView({
    required Point initialCameraPosition,
    double initialZoom = 15.0,
    bool myLocationEnabled = true,
    bool compassEnabled = true,
    bool logoEnabled = false,
    bool attributionEnabled = false,
    String styleUri = MapboxStyles.MAPBOX_STREETS,
    Function(MapboxMap)? onMapCreated,
    Function(Map<String, dynamic>)? onMapClick,
  }) {
    // Initialize Mapbox options
    MapboxOptions.setAccessToken(accessToken);

    return MapWidget(
      key: const ValueKey('mapbox'),
      onMapCreated: (MapboxMap mapboxMap) async {
        // Set the style
        await mapboxMap.loadStyleURI(styleUri);

        // Set initial camera position
        await mapboxMap.setCamera(
          CameraOptions(
            center: initialCameraPosition,
            zoom: initialZoom,
          ),
        );

        // Configure map settings
        await _configureMapSettings(
          mapboxMap,
          myLocationEnabled: myLocationEnabled,
          compassEnabled: compassEnabled,
          logoEnabled: logoEnabled,
          attributionEnabled: attributionEnabled,
        );

        // Set up map click listener
        if (onMapClick != null) {
          // For Mapbox SDK, we need to use a different approach for click handling
          mapboxMap.gestures.updateSettings(
            GesturesSettings(
              doubleTapToZoomInEnabled: true,
              doubleTouchToZoomOutEnabled: true,
              pinchToZoomEnabled: true,
              rotateEnabled: true,
              scrollEnabled: true,
            ),
          );

          // For Mapbox SDK, we need to handle map clicks differently
          // The implementation depends on the SDK version
          // For now, we'll use a simpler approach
          debugPrint(
              'Map click handler set up. Tap on the map to select locations.');

          // Note: In a real implementation, we would use the appropriate
          // method to handle map clicks based on the SDK version.
          // For example, in some versions we might use:
          // mapboxMap.addOnMapClickListener(...)
          // or mapboxMap.gestures.addOnMapClickListener(...)
          // or other methods depending on the SDK version.
        }

        // Call the onMapCreated callback
        if (onMapCreated != null) {
          onMapCreated(mapboxMap);
        }
      },
    );
  }

  /// Configure map settings
  static Future<void> _configureMapSettings(
    MapboxMap mapboxMap, {
    bool myLocationEnabled = true,
    bool compassEnabled = true,
    bool logoEnabled = true,
    bool attributionEnabled = true,
  }) async {
    // Configure location component
    if (myLocationEnabled) {
      await mapboxMap.location.updateSettings(
        LocationComponentSettings(
          enabled: true,
          pulsingEnabled: true,
          // Note: Some settings might not be available in all SDK versions
          // puckBearingEnabled: true,
          // puckBearingSource: PuckBearingSource.HEADING,
        ),
      );
    }

    // Configure compass
    if (!compassEnabled) {
      await mapboxMap.compass.updateSettings(
        CompassSettings(enabled: false),
      );
    }

    // Configure logo
    if (!logoEnabled) {
      await mapboxMap.logo.updateSettings(
        LogoSettings(enabled: false),
      );
    }

    // Configure attribution
    if (!attributionEnabled) {
      await mapboxMap.attribution.updateSettings(
        AttributionSettings(enabled: false),
      );
    }
  }

  /// Converts a LatLng to a Point
  static Point latLngToPoint(double latitude, double longitude) {
    return Point(coordinates: Position(longitude, latitude));
  }

  /// Gets directions between two points
  static Future<Map<String, dynamic>> getDirections(
      Point start, Point end) async {
    try {
      final startCoords = '${start.coordinates.lng},${start.coordinates.lat}';
      final endCoords = '${end.coordinates.lng},${end.coordinates.lat}';

      final url =
          '$baseUrl/directions/v5/mapbox/driving/$startCoords;$endCoords'
          '?alternatives=true&geometries=geojson&language=en&overview=full'
          '&steps=true&access_token=$accessToken';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        if (kDebugMode) {
          print('Failed to get directions: ${response.body}');
        }
        return {
          'routes': [
            {
              'distance': 5000, // 5 km
              'duration': 900, // 15 minutes
              'geometry': {
                'coordinates': [
                  [start.coordinates.lng, start.coordinates.lat],
                  [end.coordinates.lng, end.coordinates.lat],
                ],
              },
            }
          ],
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting directions: $e');
      }
      return {
        'routes': [
          {
            'distance': 5000, // 5 km
            'duration': 900, // 15 minutes
            'geometry': {
              'coordinates': [
                [start.coordinates.lng, start.coordinates.lat],
                [end.coordinates.lng, end.coordinates.lat],
              ],
            },
          }
        ],
      };
    }
  }

  /// Calculates the fare for a ride
  static double calculateFare(double distanceInKm, double durationInMinutes) {
    // Base fare
    double baseFare = 100.0;

    // Distance fare (KSh per km)
    double distanceFare = distanceInKm * 50.0;

    // Time fare (KSh per minute)
    double timeFare = durationInMinutes * 5.0;

    // Total fare
    double totalFare = baseFare + distanceFare + timeFare;

    // Round to nearest 10
    return (totalFare / 10).round() * 10;
  }

  /// Geocodes an address to get its coordinates
  static Future<custom.LatLng?> geocodeAddress(String address) async {
    try {
      final encodedAddress = Uri.encodeComponent(address);
      final url = '$baseUrl/geocoding/v5/mapbox.places/$encodedAddress.json'
          '?access_token=$accessToken'
          '&limit=1';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['features'].isNotEmpty) {
          final feature = data['features'][0];
          final coordinates = feature['center'];

          return custom.LatLng(coordinates[1], coordinates[0]);
        }
      } else {
        if (kDebugMode) {
          print('Failed to geocode address: ${response.body}');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error geocoding address: $e');
      }
      return null;
    }
  }

  /// Reverse geocodes coordinates to get an address
  static Future<String?> reverseGeocode(custom.LatLng coordinates) async {
    try {
      final url = '$baseUrl/geocoding/v5/mapbox.places/'
          '${coordinates.longitude},${coordinates.latitude}.json'
          '?access_token=$accessToken'
          '&limit=1';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['features'].isNotEmpty) {
          final feature = data['features'][0];
          return feature['place_name'];
        }
      } else {
        if (kDebugMode) {
          print('Failed to reverse geocode: ${response.body}');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error reverse geocoding: $e');
      }
      return null;
    }
  }

  /// Searches for places based on a query string
  static Future<List<PlaceResult>> searchPlaces(String query,
      {custom.LatLng? proximity}) async {
    try {
      if (query.isEmpty) {
        return [];
      }

      final encodedQuery = Uri.encodeComponent(query);
      var url = '$baseUrl/geocoding/v5/mapbox.places/$encodedQuery.json'
          '?access_token=$accessToken'
          '&limit=5'
          '&types=address,poi,place';

      // Add proximity if available for better local results
      if (proximity != null) {
        url += '&proximity=${proximity.longitude},${proximity.latitude}';
      }

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final features = data['features'] as List;

        return features
            .map((feature) => PlaceResult.fromMapboxFeature(feature))
            .toList();
      } else {
        if (kDebugMode) {
          print('Failed to search places: ${response.body}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error searching places: $e');
      }
      return [];
    }
  }

  /// Gets place suggestions as the user types (autocomplete)
  static Future<List<PlaceResult>> getPlaceSuggestions(String query,
      {custom.LatLng? proximity}) async {
    try {
      if (query.length < 3) {
        return [];
      }

      final encodedQuery = Uri.encodeComponent(query);
      var url = '$baseUrl/geocoding/v5/mapbox.places/$encodedQuery.json'
          '?access_token=$accessToken'
          '&limit=5'
          '&types=address,poi,place'
          '&autocomplete=true';

      // Add proximity if available for better local results
      if (proximity != null) {
        url += '&proximity=${proximity.longitude},${proximity.latitude}';
      }

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final features = data['features'] as List;

        return features
            .map((feature) => PlaceResult.fromMapboxFeature(feature))
            .toList();
      } else {
        if (kDebugMode) {
          print('Failed to get place suggestions: ${response.body}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting place suggestions: $e');
      }
      return [];
    }
  }

  /// Creates a line layer for route visualization
  static Future<void> addRouteLayer(
    MapboxMap mapboxMap,
    List<List<double>> routeCoordinates, {
    String layerId = 'route-layer',
    String sourceId = 'route-source',
    int routeColor = 0xFF3887BE, // Mapbox blue
    double routeWidth = 5.0,
    double routeOpacity = 0.8,
  }) async {
    try {
      // Add the route visualization
      // Note: The exact API depends on the Mapbox SDK version
      try {
        // Create a GeoJsonSource
        final source = GeoJsonSource(
          id: sourceId,
          data: json.encode({
            'type': 'Feature',
            'properties': {},
            'geometry': {
              'type': 'LineString',
              'coordinates': routeCoordinates,
            }
          }),
        );

        // Add the source to the map
        await mapboxMap.style.addSource(source);

        // Create a line layer
        final layer = LineLayer(
          id: layerId,
          sourceId: sourceId,
        );

        // Set layer properties
        layer.lineColor = Colors.blue.value;
        layer.lineWidth = routeWidth;
        layer.lineOpacity = routeOpacity;

        // Add the layer to the map
        await mapboxMap.style.addLayer(layer);
      } catch (e) {
        // Log any errors
        if (kDebugMode) {
          print('Error adding route visualization: $e');
        }
      }

      if (kDebugMode) {
        print('Route layer added successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding route layer: $e');
      }
    }
  }

  /// Draws a route between two points on the map
  static Future<void> drawRouteBetweenPoints(
    MapboxMap mapboxMap,
    Point origin,
    Point destination, {
    String layerId = 'route-layer',
    String sourceId = 'route-source',
    Color routeColor = Colors.blue,
    double routeWidth = 5.0,
    double routeOpacity = 0.8,
  }) async {
    try {
      // Get directions between the two points
      final directions = await getDirections(origin, destination);

      if (directions.isEmpty ||
          directions['routes'] == null ||
          (directions['routes'] as List).isEmpty) {
        if (kDebugMode) {
          print('No route found between the points');
        }
        return;
      }

      // Extract route coordinates
      final route = directions['routes'][0];
      final geometry = route['geometry'] as Map<String, dynamic>;
      final coordinates = geometry['coordinates'] as List;

      // Convert to List<List<double>>
      final routeCoordinates = coordinates.map((coord) {
        final lng = (coord[0] as num).toDouble();
        final lat = (coord[1] as num).toDouble();
        return [lng, lat];
      }).toList();

      // Draw the route on the map
      await addRouteLayer(
        mapboxMap,
        routeCoordinates,
        layerId: layerId,
        sourceId: sourceId,
        routeColor: routeColor.value,
        routeWidth: routeWidth,
        routeOpacity: routeOpacity,
      );

      // Fit the map to show the entire route
      _fitMapToRoute(mapboxMap, routeCoordinates);
    } catch (e) {
      if (kDebugMode) {
        print('Error drawing route: $e');
      }
    }
  }

  /// Fits the map view to show the entire route
  static void _fitMapToRoute(
      MapboxMap mapboxMap, List<List<double>> routeCoordinates) {
    if (routeCoordinates.isEmpty) return;

    // Find the bounds of the route
    double minLat = 90.0;
    double maxLat = -90.0;
    double minLng = 180.0;
    double maxLng = -180.0;

    for (final coord in routeCoordinates) {
      final lng = coord[0];
      final lat = coord[1];

      if (lat < minLat) minLat = lat;
      if (lat > maxLat) maxLat = lat;
      if (lng < minLng) minLng = lng;
      if (lng > maxLng) maxLng = lng;
    }

    // Add some padding
    minLat -= 0.01;
    maxLat += 0.01;
    minLng -= 0.01;
    maxLng += 0.01;

    // Create a camera options to fit the bounds
    final centerLat = (minLat + maxLat) / 2;
    final centerLng = (minLng + maxLng) / 2;
    final zoom = 14.0;

    // Animate to the new camera position
    mapboxMap.flyTo(
      CameraOptions(
        center: latLngToPoint(centerLat, centerLng),
        zoom: zoom,
      ),
      MapAnimationOptions(duration: 1000),
    );
  }

  /// Simulates rider movement along a route
  static Future<void> simulateRiderMovement(
    MapboxMap mapboxMap,
    List<List<double>> routeCoordinates,
    PointAnnotationManager annotationManager,
    PointAnnotation riderMarker, {
    int durationMs = 10000, // 10 seconds
    int steps = 100,
    Function(Point)? onPositionUpdate,
  }) async {
    if (routeCoordinates.isEmpty) return;

    final totalSteps = steps.clamp(1, routeCoordinates.length);
    final stepDuration = durationMs ~/ totalSteps;

    // Sample points along the route
    final sampledPoints = <Point>[];

    for (int i = 0; i < totalSteps; i++) {
      final index =
          (i * (routeCoordinates.length - 1) / (totalSteps - 1)).round();
      final coord =
          routeCoordinates[index.clamp(0, routeCoordinates.length - 1)];
      sampledPoints.add(Point(coordinates: Position(coord[0], coord[1])));
    }

    // Animate the marker along the route
    for (int i = 0; i < sampledPoints.length; i++) {
      final point = sampledPoints[i];

      // Update marker position
      // Note: The exact API depends on the Mapbox SDK version
      // In some versions, we might need to use a different approach
      try {
        // Create new options with the updated position
        final options = PointAnnotationOptions(
          geometry: point,
          iconImage: riderMarker.iconImage,
          iconSize: riderMarker.iconSize,
        );

        // Delete the old marker and create a new one
        await annotationManager.delete(riderMarker);
        riderMarker = await annotationManager.create(options);
      } catch (e) {
        if (kDebugMode) {
          print('Error updating marker position: $e');
        }
      }

      // Call the position update callback
      if (onPositionUpdate != null) {
        onPositionUpdate(point);
      }

      // Wait for the next step
      if (i < sampledPoints.length - 1) {
        await Future.delayed(Duration(milliseconds: stepDuration));
      }
    }
  }

  /// Adds traffic data to the map
  static Future<void> addTrafficLayer(MapboxMap mapboxMap) async {
    try {
      // Check if the traffic layer already exists
      final style = mapboxMap.style;
      final layers = await style.getStyleLayers();

      // If traffic layer already exists, return
      for (final layer in layers) {
        if (layer?.id == 'mapbox-traffic') {
          return;
        }
      }

      // Add the traffic source if it doesn't exist
      try {
        await style.addSource(VectorSource(
          id: 'mapbox-traffic',
          url: 'mapbox://mapbox.mapbox-traffic-v1',
        ));
      } catch (e) {
        // Source might already exist
        if (kDebugMode) {
          print('Traffic source already exists or error: $e');
        }
      }

      // Add traffic layer
      final trafficLayer = LineLayer(
        id: 'mapbox-traffic-layer',
        sourceId: 'mapbox-traffic',
        sourceLayer: 'traffic',
      );

      // Set traffic layer properties
      trafficLayer.lineWidth = 2.0;
      trafficLayer.lineColor = Colors.red.value;
      trafficLayer.lineOpacity = 0.7;

      // Add the layer to the map
      await style.addLayer(trafficLayer);

      if (kDebugMode) {
        print('Traffic layer added successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding traffic layer: $e');
      }
    }
  }

  /// Removes traffic layer from the map
  static Future<void> removeTrafficLayer(MapboxMap mapboxMap) async {
    try {
      final style = mapboxMap.style;

      // Remove the traffic layer
      try {
        // Check if layer exists first
        final layers = await style.getStyleLayers();
        bool layerExists = false;
        for (final layer in layers) {
          if (layer?.id == 'mapbox-traffic-layer') {
            layerExists = true;
            break;
          }
        }

        if (layerExists) {
          // Use the correct method to remove the layer
          await style.removeStyleLayer('mapbox-traffic-layer');
        }
      } catch (e) {
        // Layer might not exist
        if (kDebugMode) {
          print('Traffic layer does not exist or error: $e');
        }
      }

      // Remove the traffic source
      try {
        // Check if source exists first
        final sources = await style.getStyleSources();
        bool sourceExists = false;
        for (final source in sources) {
          if (source?.id == 'mapbox-traffic') {
            sourceExists = true;
            break;
          }
        }

        if (sourceExists) {
          // Use the correct method to remove the source
          await style.removeStyleSource('mapbox-traffic');
        }
      } catch (e) {
        // Source might not exist
        if (kDebugMode) {
          print('Traffic source does not exist or error: $e');
        }
      }

      if (kDebugMode) {
        print('Traffic layer removed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error removing traffic layer: $e');
      }
    }
  }

  /// Caches map tiles for offline use
  static Future<void> cacheMapTiles(
    MapboxMap mapboxMap,
    Point center,
    double zoomLevel,
    double radius, // in kilometers
  ) async {
    try {
      // Calculate the bounding box for the area to cache
      final double latDegreePerKm =
          1 / 111.32; // 1 degree latitude is approximately 111.32 km
      final double lonDegreePerKm =
          1 / (111.32 * cos(center.coordinates.lat * pi / 180));

      final double latDelta = radius * latDegreePerKm;
      final double lonDelta = radius * lonDegreePerKm;

      final double minLat = center.coordinates.lat - latDelta;
      final double maxLat = center.coordinates.lat + latDelta;
      final double minLon = center.coordinates.lng - lonDelta;
      final double maxLon = center.coordinates.lng + lonDelta;

      // Create a bounding box for the area to cache
      final CoordinateBounds bounds = CoordinateBounds(
        southwest: Point(coordinates: Position(minLon, minLat)),
        northeast: Point(coordinates: Position(maxLon, maxLat)),
        infiniteBounds: false,
      );

      // Cache the tiles
      // Note: This is a simplified version. In a real app, you would use the Mapbox offline manager
      // to download and manage offline regions.
      if (kDebugMode) {
        print('Caching map tiles for area: $bounds at zoom level: $zoomLevel');
      }

      // In a real implementation, you would use:
      // final offlineManager = await mapboxMap.offlineManager;
      // final region = await offlineManager.createOfflineRegion(
      //   OfflineRegionDefinition(
      //     bounds: bounds,
      //     minZoom: zoomLevel - 2,
      //     maxZoom: zoomLevel + 2,
      //     mapStyleUrl: mapboxMap.styleUri,
      //   ),
      //   metadata: {'name': 'Offline region'},
      // );
      // await region.setDownloadState(true);

      if (kDebugMode) {
        print('Map tiles cached successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching map tiles: $e');
      }
    }
  }
}
