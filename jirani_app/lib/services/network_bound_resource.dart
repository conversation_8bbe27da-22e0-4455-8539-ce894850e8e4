import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'cache_manager.dart';

/// Resource status
enum ResourceStatus {
  /// Loading from cache
  loadingFromCache,
  
  /// Loading from network
  loadingFromNetwork,
  
  /// Success
  success,
  
  /// Error
  error,
}

/// Resource class for handling network bound resources
class Resource<T> {
  /// Resource status
  final ResourceStatus status;
  
  /// Resource data
  final T? data;
  
  /// Resource error
  final Object? error;
  
  /// Resource stack trace
  final StackTrace? stackTrace;
  
  /// Whether the data is from cache
  final bool isFromCache;
  
  /// Creates a new instance of [Resource]
  const Resource({
    required this.status,
    this.data,
    this.error,
    this.stackTrace,
    this.isFromCache = false,
  });
  
  /// Creates a loading from cache resource
  factory Resource.loadingFromCache() {
    return const Resource(
      status: ResourceStatus.loadingFromCache,
      isFromCache: true,
    );
  }
  
  /// Creates a loading from network resource
  factory Resource.loadingFromNetwork({T? data}) {
    return Resource(
      status: ResourceStatus.loadingFromNetwork,
      data: data,
      isFromCache: false,
    );
  }
  
  /// Creates a success resource
  factory Resource.success(T data, {bool isFromCache = false}) {
    return Resource(
      status: ResourceStatus.success,
      data: data,
      isFromCache: isFromCache,
    );
  }
  
  /// Creates an error resource
  factory Resource.error(Object error, StackTrace stackTrace, {T? data}) {
    return Resource(
      status: ResourceStatus.error,
      data: data,
      error: error,
      stackTrace: stackTrace,
      isFromCache: false,
    );
  }
  
  /// Whether the resource is loading
  bool get isLoading => status == ResourceStatus.loadingFromCache || status == ResourceStatus.loadingFromNetwork;
  
  /// Whether the resource is success
  bool get isSuccess => status == ResourceStatus.success;
  
  /// Whether the resource is error
  bool get isError => status == ResourceStatus.error;
  
  /// Copy with
  Resource<T> copyWith({
    ResourceStatus? status,
    T? data,
    Object? error,
    StackTrace? stackTrace,
    bool? isFromCache,
  }) {
    return Resource<T>(
      status: status ?? this.status,
      data: data ?? this.data,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }
}

/// Network bound resource class
class NetworkBoundResource<T> {
  /// Cache key
  final String cacheKey;
  
  /// Fetch from network function
  final Future<T> Function() fetchFromNetwork;
  
  /// Parse response function
  final T Function(dynamic) parseResponse;
  
  /// Should fetch from network function
  final bool Function(T?)? shouldFetchFromNetwork;
  
  /// Cache duration
  final Duration cacheDuration;
  
  /// Creates a new instance of [NetworkBoundResource]
  NetworkBoundResource({
    required this.cacheKey,
    required this.fetchFromNetwork,
    required this.parseResponse,
    this.shouldFetchFromNetwork,
    this.cacheDuration = const Duration(hours: 1),
  });
  
  /// Fetch data
  Stream<Resource<T>> fetch() async* {
    // Emit loading from cache
    yield Resource.loadingFromCache();
    
    try {
      // Try to get data from cache
      final cachedData = await AppCacheManager.getCachedApiResponse(cacheKey);
      
      if (cachedData != null) {
        // Parse cached data
        final parsedData = parseResponse(cachedData);
        
        // Emit success with cached data
        yield Resource.success(parsedData, isFromCache: true);
        
        // Check if we should fetch from network
        final shouldFetch = shouldFetchFromNetwork?.call(parsedData) ?? true;
        
        if (!shouldFetch) {
          return;
        }
      }
      
      // Emit loading from network
      yield Resource.loadingFromNetwork(
        data: cachedData != null ? parseResponse(cachedData) : null,
      );
      
      // Fetch from network
      final networkData = await fetchFromNetwork();
      
      // Cache network data
      await AppCacheManager.cacheApiResponse(
        cacheKey,
        networkData,
        duration: cacheDuration,
      );
      
      // Emit success with network data
      yield Resource.success(networkData);
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error fetching data: $e');
        print(stackTrace);
      }
      
      // Try to get data from cache if network request failed
      final cachedData = await AppCacheManager.getCachedApiResponse(cacheKey);
      
      if (cachedData != null) {
        // Parse cached data
        final parsedData = parseResponse(cachedData);
        
        // Emit error with cached data
        yield Resource.error(e, stackTrace, data: parsedData);
      } else {
        // Emit error without data
        yield Resource.error(e, stackTrace);
      }
    }
  }
}

/// Network bound resource notifier
class NetworkBoundResourceNotifier<T> extends StateNotifier<Resource<T>> {
  /// Network bound resource
  final NetworkBoundResource<T> resource;
  
  /// Creates a new instance of [NetworkBoundResourceNotifier]
  NetworkBoundResourceNotifier({
    required this.resource,
  }) : super(Resource.loadingFromCache());
  
  /// Fetch data
  Future<void> fetch() async {
    await for (final resource in this.resource.fetch()) {
      state = resource;
    }
  }
  
  /// Refresh data
  Future<void> refresh() async {
    state = Resource.loadingFromNetwork(data: state.data);
    
    try {
      final networkData = await resource.fetchFromNetwork();
      
      // Cache network data
      await AppCacheManager.cacheApiResponse(
        resource.cacheKey,
        networkData,
        duration: resource.cacheDuration,
      );
      
      state = Resource.success(networkData);
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error refreshing data: $e');
        print(stackTrace);
      }
      
      state = Resource.error(e, stackTrace, data: state.data);
    }
  }
}
