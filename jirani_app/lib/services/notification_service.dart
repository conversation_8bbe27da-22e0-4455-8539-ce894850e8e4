import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/websocket_message.dart';
import 'logging_service.dart';

/// Local notification service for ride updates
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  bool _isInitialized = false;
  final StreamController<RideNotification> _notificationController = StreamController.broadcast();

  /// Stream of notifications
  Stream<RideNotification> get notificationStream => _notificationController.stream;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize platform-specific notification services
      if (!kIsWeb) {
        await _initializePlatformNotifications();
      }
      
      _isInitialized = true;
      LoggingService.i('Notification service initialized');
    } catch (e) {
      LoggingService.e('Failed to initialize notification service', error: e);
    }
  }

  /// Initialize platform-specific notifications
  Future<void> _initializePlatformNotifications() async {
    // This would integrate with flutter_local_notifications
    // For now, we'll use a simple implementation
  }

  /// Show a ride notification
  Future<void> showRideNotification(RideNotification notification) async {
    try {
      // Add to stream for in-app notifications
      _notificationController.add(notification);

      // Show system notification if app is in background
      if (!kIsWeb && _isInitialized) {
        await _showSystemNotification(notification);
      }

      // Haptic feedback for important notifications
      if (notification.priority == NotificationPriority.high) {
        HapticFeedback.heavyImpact();
      } else {
        HapticFeedback.lightImpact();
      }

      LoggingService.i('Notification shown: ${notification.title}');
    } catch (e) {
      LoggingService.e('Failed to show notification', error: e);
    }
  }

  /// Show system notification
  Future<void> _showSystemNotification(RideNotification notification) async {
    // This would use flutter_local_notifications to show system notifications
    // For now, we'll just log it
    LoggingService.i('System notification: ${notification.title} - ${notification.body}');
  }

  /// Handle WebSocket notification message
  void handleWebSocketNotification(WebSocketMessage message) {
    try {
      final title = message.data['title'] as String? ?? 'Ride Update';
      final body = message.data['body'] as String? ?? '';
      final type = _parseNotificationType(message.data['type'] as String?);
      final priority = _parseNotificationPriority(message.data['priority'] as String?);

      final notification = RideNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        body: body,
        type: type,
        priority: priority,
        timestamp: DateTime.now(),
        data: message.data,
      );

      showRideNotification(notification);
    } catch (e) {
      LoggingService.e('Failed to handle WebSocket notification', error: e);
    }
  }

  /// Parse notification type from string
  RideNotificationType _parseNotificationType(String? typeString) {
    switch (typeString) {
      case 'rider_assigned':
        return RideNotificationType.riderAssigned;
      case 'rider_arriving':
        return RideNotificationType.riderArriving;
      case 'rider_arrived':
        return RideNotificationType.riderArrived;
      case 'trip_started':
        return RideNotificationType.tripStarted;
      case 'trip_completed':
        return RideNotificationType.tripCompleted;
      case 'ride_cancelled':
        return RideNotificationType.rideCancelled;
      case 'payment_required':
        return RideNotificationType.paymentRequired;
      case 'emergency':
        return RideNotificationType.emergency;
      default:
        return RideNotificationType.general;
    }
  }

  /// Parse notification priority from string
  NotificationPriority _parseNotificationPriority(String? priorityString) {
    switch (priorityString) {
      case 'high':
        return NotificationPriority.high;
      case 'medium':
        return NotificationPriority.medium;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.medium;
    }
  }

  /// Create ride status notification
  static RideNotification createRideStatusNotification({
    required String status,
    String? riderName,
    String? estimatedTime,
  }) {
    String title;
    String body;
    RideNotificationType type;
    NotificationPriority priority = NotificationPriority.medium;

    switch (status) {
      case 'rider_assigned':
        title = 'Rider Assigned';
        body = riderName != null 
            ? '$riderName will pick you up'
            : 'A rider has been assigned to your trip';
        type = RideNotificationType.riderAssigned;
        break;
      case 'rider_arriving':
        title = 'Rider Arriving';
        body = estimatedTime != null
            ? 'Your rider will arrive in $estimatedTime'
            : 'Your rider is on the way';
        type = RideNotificationType.riderArriving;
        break;
      case 'rider_arrived':
        title = 'Rider Arrived';
        body = 'Your rider has arrived at the pickup location';
        type = RideNotificationType.riderArrived;
        priority = NotificationPriority.high;
        break;
      case 'trip_started':
        title = 'Trip Started';
        body = 'Your trip has begun. Enjoy the ride!';
        type = RideNotificationType.tripStarted;
        break;
      case 'trip_completed':
        title = 'Trip Completed';
        body = 'You have arrived at your destination';
        type = RideNotificationType.tripCompleted;
        break;
      case 'ride_cancelled':
        title = 'Ride Cancelled';
        body = 'Your ride has been cancelled';
        type = RideNotificationType.rideCancelled;
        priority = NotificationPriority.high;
        break;
      default:
        title = 'Ride Update';
        body = 'Your ride status has been updated';
        type = RideNotificationType.general;
    }

    return RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      body: body,
      type: type,
      priority: priority,
      timestamp: DateTime.now(),
    );
  }

  /// Create emergency notification
  static RideNotification createEmergencyNotification({
    required String message,
    Map<String, dynamic>? data,
  }) {
    return RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'Emergency Alert',
      body: message,
      type: RideNotificationType.emergency,
      priority: NotificationPriority.high,
      timestamp: DateTime.now(),
      data: data,
    );
  }

  /// Dispose resources
  void dispose() {
    _notificationController.close();
  }
}

/// Ride notification model
class RideNotification {
  final String id;
  final String title;
  final String body;
  final RideNotificationType type;
  final NotificationPriority priority;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const RideNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    required this.timestamp,
    this.data,
  });

  @override
  String toString() {
    return 'RideNotification(id: $id, title: $title, body: $body, type: $type, priority: $priority, timestamp: $timestamp)';
  }
}

/// Notification types
enum RideNotificationType {
  general,
  riderAssigned,
  riderArriving,
  riderArrived,
  tripStarted,
  tripCompleted,
  rideCancelled,
  paymentRequired,
  emergency,
}

/// Notification priorities
enum NotificationPriority {
  low,
  medium,
  high,
}

/// In-app notification widget
class InAppNotificationWidget extends StatefulWidget {
  final RideNotification notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const InAppNotificationWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  State<InAppNotificationWidget> createState() => _InAppNotificationWidgetState();
}

class _InAppNotificationWidgetState extends State<InAppNotificationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);

    _animationController.forward();

    // Auto-dismiss after 5 seconds for non-high priority notifications
    if (widget.notification.priority != NotificationPriority.high) {
      Timer(const Duration(seconds: 5), () {
        if (mounted) {
          _dismiss();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() {
    _animationController.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }

  Color _getNotificationColor() {
    switch (widget.notification.type) {
      case RideNotificationType.emergency:
        return Colors.red;
      case RideNotificationType.riderArrived:
        return Colors.green;
      case RideNotificationType.rideCancelled:
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  IconData _getNotificationIcon() {
    switch (widget.notification.type) {
      case RideNotificationType.emergency:
        return Icons.warning;
      case RideNotificationType.riderAssigned:
        return Icons.person_add;
      case RideNotificationType.riderArriving:
        return Icons.directions_car;
      case RideNotificationType.riderArrived:
        return Icons.location_on;
      case RideNotificationType.tripStarted:
        return Icons.play_arrow;
      case RideNotificationType.tripCompleted:
        return Icons.check_circle;
      case RideNotificationType.rideCancelled:
        return Icons.cancel;
      case RideNotificationType.paymentRequired:
        return Icons.payment;
      default:
        return Icons.info;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getNotificationColor(),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(
                _getNotificationIcon(),
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.notification.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (widget.notification.body.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        widget.notification.body,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              IconButton(
                onPressed: _dismiss,
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
