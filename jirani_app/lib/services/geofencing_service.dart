import 'dart:async';
import 'dart:math';
import '../models/lat_lng.dart' as custom;
import 'logging_service.dart';

/// Geofencing service for location-based triggers
class GeofencingService {
  static final GeofencingService _instance = GeofencingService._internal();
  factory GeofencingService() => _instance;
  GeofencingService._internal();

  final Map<String, Geofence> _geofences = {};
  final StreamController<GeofenceEvent> _eventController = StreamController.broadcast();
  Timer? _monitoringTimer;
  custom.LatLng? _lastKnownLocation;

  /// Stream of geofence events
  Stream<GeofenceEvent> get eventStream => _eventController.stream;

  /// Add a geofence
  void addGeofence(Geofence geofence) {
    _geofences[geofence.id] = geofence;
    LoggingService.i('Geofence added: ${geofence.id}');
    
    // Start monitoring if not already started
    if (_monitoringTimer == null) {
      _startMonitoring();
    }
  }

  /// Remove a geofence
  void removeGeofence(String geofenceId) {
    _geofences.remove(geofenceId);
    LoggingService.i('Geofence removed: $geofenceId');
    
    // Stop monitoring if no geofences left
    if (_geofences.isEmpty) {
      _stopMonitoring();
    }
  }

  /// Update current location and check geofences
  void updateLocation(custom.LatLng location) {
    final previousLocation = _lastKnownLocation;
    _lastKnownLocation = location;

    // Check all geofences
    for (final geofence in _geofences.values) {
      _checkGeofence(geofence, location, previousLocation);
    }
  }

  /// Check if a specific geofence is triggered
  void _checkGeofence(
    Geofence geofence,
    custom.LatLng currentLocation,
    custom.LatLng? previousLocation,
  ) {
    final currentDistance = _calculateDistance(currentLocation, geofence.center);
    final isCurrentlyInside = currentDistance <= geofence.radius;

    bool? wasPreviouslyInside;
    if (previousLocation != null) {
      final previousDistance = _calculateDistance(previousLocation, geofence.center);
      wasPreviouslyInside = previousDistance <= geofence.radius;
    }

    // Determine event type
    GeofenceEventType? eventType;
    
    if (wasPreviouslyInside == null) {
      // First location update
      if (isCurrentlyInside) {
        eventType = GeofenceEventType.enter;
      }
    } else {
      // Subsequent location updates
      if (!wasPreviouslyInside && isCurrentlyInside) {
        eventType = GeofenceEventType.enter;
      } else if (wasPreviouslyInside && !isCurrentlyInside) {
        eventType = GeofenceEventType.exit;
      } else if (isCurrentlyInside) {
        eventType = GeofenceEventType.dwell;
      }
    }

    // Fire event if applicable
    if (eventType != null) {
      final event = GeofenceEvent(
        geofenceId: geofence.id,
        eventType: eventType,
        location: currentLocation,
        distance: currentDistance,
        timestamp: DateTime.now(),
      );

      _eventController.add(event);
      LoggingService.i('Geofence event: ${geofence.id} - $eventType');
    }
  }

  /// Calculate distance between two points in meters
  double _calculateDistance(custom.LatLng point1, custom.LatLng point2) {
    const double earthRadius = 6371000; // Earth's radius in meters
    
    final lat1Rad = point1.latitude * pi / 180;
    final lat2Rad = point2.latitude * pi / 180;
    final deltaLatRad = (point2.latitude - point1.latitude) * pi / 180;
    final deltaLngRad = (point2.longitude - point1.longitude) * pi / 180;

    final a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  /// Start monitoring geofences
  void _startMonitoring() {
    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      // This would typically be triggered by location updates
      // For now, it's just a placeholder
    });
    LoggingService.i('Geofence monitoring started');
  }

  /// Stop monitoring geofences
  void _stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    LoggingService.i('Geofence monitoring stopped');
  }

  /// Create a pickup geofence
  static Geofence createPickupGeofence(String rideId, custom.LatLng location) {
    return Geofence(
      id: 'pickup_$rideId',
      center: location,
      radius: 50, // 50 meters
      type: GeofenceType.pickup,
    );
  }

  /// Create a destination geofence
  static Geofence createDestinationGeofence(String rideId, custom.LatLng location) {
    return Geofence(
      id: 'destination_$rideId',
      center: location,
      radius: 100, // 100 meters
      type: GeofenceType.destination,
    );
  }

  /// Create a driver proximity geofence
  static Geofence createDriverProximityGeofence(String rideId, custom.LatLng location) {
    return Geofence(
      id: 'driver_proximity_$rideId',
      center: location,
      radius: 200, // 200 meters
      type: GeofenceType.driverProximity,
    );
  }

  /// Dispose resources
  void dispose() {
    _stopMonitoring();
    _eventController.close();
    _geofences.clear();
  }
}

/// Geofence model
class Geofence {
  final String id;
  final custom.LatLng center;
  final double radius; // in meters
  final GeofenceType type;
  final Map<String, dynamic>? metadata;

  const Geofence({
    required this.id,
    required this.center,
    required this.radius,
    required this.type,
    this.metadata,
  });

  @override
  String toString() {
    return 'Geofence(id: $id, center: $center, radius: $radius, type: $type)';
  }
}

/// Geofence types
enum GeofenceType {
  pickup,
  destination,
  driverProximity,
  emergencyZone,
  restrictedArea,
}

/// Geofence event types
enum GeofenceEventType {
  enter,
  exit,
  dwell,
}

/// Geofence event model
class GeofenceEvent {
  final String geofenceId;
  final GeofenceEventType eventType;
  final custom.LatLng location;
  final double distance;
  final DateTime timestamp;

  const GeofenceEvent({
    required this.geofenceId,
    required this.eventType,
    required this.location,
    required this.distance,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'GeofenceEvent(geofenceId: $geofenceId, eventType: $eventType, location: $location, distance: $distance, timestamp: $timestamp)';
  }
}

/// Geofencing provider for ride-specific geofences
class RideGeofencingManager {
  final GeofencingService _geofencingService = GeofencingService();
  final Map<String, List<String>> _rideGeofences = {};

  /// Set up geofences for a ride
  void setupRideGeofences(String rideId, custom.LatLng pickup, custom.LatLng destination) {
    final geofences = [
      GeofencingService.createPickupGeofence(rideId, pickup),
      GeofencingService.createDestinationGeofence(rideId, destination),
    ];

    final geofenceIds = <String>[];
    for (final geofence in geofences) {
      _geofencingService.addGeofence(geofence);
      geofenceIds.add(geofence.id);
    }

    _rideGeofences[rideId] = geofenceIds;
    LoggingService.i('Geofences set up for ride: $rideId');
  }

  /// Add driver proximity geofence
  void addDriverProximityGeofence(String rideId, custom.LatLng driverLocation) {
    final geofence = GeofencingService.createDriverProximityGeofence(rideId, driverLocation);
    _geofencingService.addGeofence(geofence);
    
    _rideGeofences[rideId]?.add(geofence.id);
    LoggingService.i('Driver proximity geofence added for ride: $rideId');
  }

  /// Clean up geofences for a ride
  void cleanupRideGeofences(String rideId) {
    final geofenceIds = _rideGeofences[rideId];
    if (geofenceIds != null) {
      for (final geofenceId in geofenceIds) {
        _geofencingService.removeGeofence(geofenceId);
      }
      _rideGeofences.remove(rideId);
      LoggingService.i('Geofences cleaned up for ride: $rideId');
    }
  }

  /// Update location for geofence monitoring
  void updateLocation(custom.LatLng location) {
    _geofencingService.updateLocation(location);
  }

  /// Get geofence event stream
  Stream<GeofenceEvent> get eventStream => _geofencingService.eventStream;

  /// Dispose resources
  void dispose() {
    for (final rideId in _rideGeofences.keys.toList()) {
      cleanupRideGeofences(rideId);
    }
    _geofencingService.dispose();
  }
}
