import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../widgets/animations/animated_scale_button.dart';

/// Onboarding screen
class OnboardingScreen extends StatefulWidget {
  /// Creates a new instance of [OnboardingScreen]
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    const OnboardingPage(
      title: 'Discover Local Services',
      description: 'Find the best services in your neighborhood with just a few taps.',
      image: Icons.search,
      color: Color(0xFF0A7EA4),
    ),
    const OnboardingPage(
      title: 'Book Instantly',
      description: 'Book services instantly and get confirmation in seconds.',
      image: Icons.calendar_today,
      color: Color(0xFFFF6B00),
    ),
    const OnboardingPage(
      title: 'Pay Securely',
      description: 'Pay for services securely using your preferred payment method.',
      image: Icons.payment,
      color: Color(0xFF4CAF50),
    ),
    const OnboardingPage(
      title: 'Rate & Review',
      description: 'Share your experience and help others find the best services.',
      image: Icons.star,
      color: Color(0xFFFF4785),
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _onGetStarted();
    }
  }

  void _onGetStarted() async {
    HapticFeedback.mediumImpact();

    // TODO: Save that user has seen onboarding using shared preferences

    // Navigate to login screen
    context.goNamed('login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background color
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            color: _pages[_currentPage].color,
          ),

          // Skip button
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: AnimatedScaleButton(
              onTap: _onGetStarted,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'Skip',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),

          // Page view
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _pages.length,
            itemBuilder: (context, index) {
              return _OnboardingPageView(
                page: _pages[index],
                isActive: _currentPage == index,
              );
            },
          ),

          // Bottom navigation
          Positioned(
            bottom: MediaQuery.of(context).padding.bottom + 32,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Page indicator
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _pages.length,
                    (index) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: _currentPage == index ? 24 : 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _currentPage == index
                            ? Colors.white
                            : Colors.white.withOpacity(0.4),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Next button
                AnimatedScaleButton(
                  onTap: _nextPage,
                  hapticFeedbackType: HapticFeedbackType.medium,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Text(
                      _currentPage < _pages.length - 1 ? 'Next' : 'Get Started',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: _pages[_currentPage].color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Onboarding page view
class _OnboardingPageView extends StatelessWidget {
  final OnboardingPage page;
  final bool isActive;

  const _OnboardingPageView({
    required this.page,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image
          Container(
            width: 160,
            height: 160,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(80),
            ),
            child: Icon(
              page.image,
              size: 80,
              color: Colors.white,
            ),
          )
          .animate(target: isActive ? 1 : 0)
          .scale(
            begin: const Offset(0.8, 0.8),
            end: const Offset(1, 1),
            duration: 600.ms,
            curve: Curves.easeOut,
          )
          .then(delay: 200.ms)
          .shimmer(
            duration: 1200.ms,
            color: Colors.white.withOpacity(0.8),
          ),

          const SizedBox(height: 48),

          // Title
          Text(
            page.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          )
          .animate(target: isActive ? 1 : 0)
          .fadeIn(
            duration: 600.ms,
            curve: Curves.easeOut,
            delay: 300.ms,
          )
          .slideY(
            begin: 0.2,
            end: 0,
            duration: 600.ms,
            curve: Curves.easeOut,
            delay: 300.ms,
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            page.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          )
          .animate(target: isActive ? 1 : 0)
          .fadeIn(
            duration: 600.ms,
            curve: Curves.easeOut,
            delay: 500.ms,
          )
          .slideY(
            begin: 0.2,
            end: 0,
            duration: 600.ms,
            curve: Curves.easeOut,
            delay: 500.ms,
          ),
        ],
      ),
    );
  }
}

/// Onboarding page model
class OnboardingPage {
  /// The title of the page
  final String title;

  /// The description of the page
  final String description;

  /// The image of the page
  final IconData image;

  /// The color of the page
  final Color color;

  /// Creates a new instance of [OnboardingPage]
  const OnboardingPage({
    required this.title,
    required this.description,
    required this.image,
    required this.color,
  });
}
