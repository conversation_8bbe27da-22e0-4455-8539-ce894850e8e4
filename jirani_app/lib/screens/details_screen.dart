import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/service.dart';
import '../providers/service_provider.dart';
import '../providers/favorites_provider.dart';
import '../widgets/animations/animated_scale_button.dart';
import '../widgets/common/glassmorphic_container.dart';
import '../widgets/common/skeleton_loader.dart';

/// Details screen
class DetailsScreen extends ConsumerWidget {
  /// The ID of the service
  final String id;

  /// Creates a new instance of [DetailsScreen]
  const DetailsScreen({
    super.key,
    required this.id,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serviceAsync = ref.watch(serviceByIdProvider(id));
    final favoriteIds = ref.watch(favoriteServiceIdsProvider);

    return Scaffold(
      body: serviceAsync.when(
        data: (service) {
          if (service == null) {
            return const Center(
              child: Text('Service not found'),
            );
          }

          return _buildServiceDetails(context, ref, service, favoriteIds);
        },
        loading: () => _buildLoadingState(context),
        error: (_, __) => const Center(
          child: Text('Failed to load service details'),
        ),
      ),
    );
  }

  Widget _buildServiceDetails(
    BuildContext context,
    WidgetRef ref,
    Service service,
    List<String> favoriteIds,
  ) {
    final isFavorite = favoriteIds.contains(service.id);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                children: [
                  // Service image
                  Positioned.fill(
                    child: Hero(
                      tag: 'service_image_${service.id}',
                      child: Image.network(
                        service.imageUrl,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),

                  // Gradient overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                          stops: const [0.6, 1.0],
                        ),
                      ),
                    ),
                  ),

                  // Service name and provider
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          service.name,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          service.provider.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Price tag
                  Positioned(
                    top: MediaQuery.of(context).padding.top + 16,
                    right: 16,
                    child: GlassmorphicContainer(
                      width: 100,
                      height: 40,
                      borderRadius: BorderRadius.circular(20),
                      blur: 10,
                      opacity: 0.2,
                      child: Center(
                        child: Text(
                          'KSh ${service.price.toInt()}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            leading: AnimatedScaleButton(
              onTap: () => context.pop(),
              child: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                ),
              ),
            ),
            actions: [
              AnimatedScaleButton(
                onTap: () {
                  ref.read(favoriteServiceIdsProvider.notifier).toggleFavorite(service.id);
                  HapticFeedback.lightImpact();
                },
                child: Container(
                  margin: const EdgeInsets.all(8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : Colors.white,
                  ),
                ),
              ),
            ],
          ),

          // Service details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Rating and reviews
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Theme.of(context).colorScheme.secondary,
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${service.rating}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${service.reviewCount} reviews)',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          service.category,
                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Description
                  Text(
                    'Description',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    service.description,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),

                  const SizedBox(height: 24),

                  // Provider details
                  Text(
                    'Provider',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Provider image
                        CircleAvatar(
                          radius: 32,
                          backgroundImage: NetworkImage(service.provider.imageUrl),
                        ),

                        const SizedBox(width: 16),

                        // Provider details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                service.provider.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                service.provider.address,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  _buildProviderInfoChip(
                                    context,
                                    icon: Icons.phone,
                                    label: 'Call',
                                    onTap: () async {
                                      final phoneNumber = service.provider.phone;
                                      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

                                      // Store the BuildContext in a local variable
                                      final currentContext = context;

                                      try {
                                        if (await canLaunchUrl(phoneUri)) {
                                          await launchUrl(phoneUri);
                                        } else {
                                          if (currentContext.mounted) {
                                            ScaffoldMessenger.of(currentContext).showSnackBar(
                                              const SnackBar(
                                                content: Text('Could not launch phone app'),
                                              ),
                                            );
                                          }
                                        }
                                      } catch (e) {
                                        if (currentContext.mounted) {
                                          ScaffoldMessenger.of(currentContext).showSnackBar(
                                            SnackBar(
                                              content: Text('Error: $e'),
                                            ),
                                          );
                                        }
                                      }
                                    },
                                  ),
                                  const SizedBox(width: 8),
                                  _buildProviderInfoChip(
                                    context,
                                    icon: Icons.email,
                                    label: 'Email',
                                    onTap: () async {
                                      final email = service.provider.email;
                                      final Uri emailUri = Uri(
                                        scheme: 'mailto',
                                        path: email,
                                        queryParameters: {
                                          'subject': 'Inquiry about ${service.name}',
                                          'body': 'Hello ${service.provider.name},\n\nI am interested in your service "${service.name}".\n\nRegards,\n'
                                        }
                                      );

                                      // Store the BuildContext in a local variable
                                      final currentContext = context;

                                      try {
                                        if (await canLaunchUrl(emailUri)) {
                                          await launchUrl(emailUri);
                                        } else {
                                          if (currentContext.mounted) {
                                            ScaffoldMessenger.of(currentContext).showSnackBar(
                                              const SnackBar(
                                                content: Text('Could not launch email app'),
                                              ),
                                            );
                                          }
                                        }
                                      } catch (e) {
                                        if (currentContext.mounted) {
                                          ScaffoldMessenger.of(currentContext).showSnackBar(
                                            SnackBar(
                                              content: Text('Error: $e'),
                                            ),
                                          );
                                        }
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 80), // Add extra space for the floating button
                ],
              ),
            ),
          ),
        ],
      ),
      // Book now button
      floatingActionButton: AnimatedScaleButton(
        onTap: () {
          // TODO: Implement booking
          HapticFeedback.mediumImpact();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 16,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.calendar_today,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Book Now',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildProviderInfoChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return AnimatedScaleButton(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                children: [
                  // Skeleton image
                  const Positioned.fill(
                    child: SkeletonLoader(
                      height: double.infinity,
                      width: double.infinity,
                    ),
                  ),

                  // Back button
                  Positioned(
                    top: MediaQuery.of(context).padding.top,
                    left: 8,
                    child: AnimatedScaleButton(
                      onTap: () => context.pop(),
                      child: Container(
                        margin: const EdgeInsets.all(8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Skeleton content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Rating and reviews
                  Row(
                    children: [
                      const SkeletonLoader(
                        height: 20,
                        width: 100,
                      ),
                      const Spacer(),
                      SkeletonLoader(
                        height: 24,
                        width: 80,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Description
                  const SkeletonLoader(
                    height: 24,
                    width: 120,
                  ),
                  const SizedBox(height: 8),
                  const SkeletonLoader(
                    height: 16,
                    width: double.infinity,
                  ),
                  const SizedBox(height: 8),
                  const SkeletonLoader(
                    height: 16,
                    width: double.infinity,
                  ),
                  const SizedBox(height: 8),
                  const SkeletonLoader(
                    height: 16,
                    width: 200,
                  ),

                  const SizedBox(height: 24),

                  // Provider details
                  const SkeletonLoader(
                    height: 24,
                    width: 100,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Provider image
                        const SkeletonLoader(
                          height: 64,
                          width: 64,
                          borderRadius: BorderRadius.all(Radius.circular(32)),
                        ),

                        const SizedBox(width: 16),

                        // Provider details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SkeletonLoader(
                                height: 20,
                                width: 150,
                              ),
                              const SizedBox(height: 8),
                              const SkeletonLoader(
                                height: 16,
                                width: 200,
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  SkeletonLoader(
                                    height: 32,
                                    width: 80,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  const SizedBox(width: 8),
                                  SkeletonLoader(
                                    height: 32,
                                    width: 80,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: SkeletonLoader(
        height: 56,
        width: 160,
        borderRadius: BorderRadius.circular(24),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
