import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/category.dart';
import '../models/service.dart';
import '../providers/category_provider.dart';
import '../providers/service_provider.dart';
import '../providers/favorites_provider.dart';
import '../widgets/animations/animated_scale_button.dart';
import '../widgets/common/skeleton_loader.dart';

/// Explore screen
class ExploreScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [ExploreScreen]
  const ExploreScreen({super.key});

  @override
  ConsumerState<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends ConsumerState<ExploreScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 0, vsync: this);

    // Initialize the tab controller when categories are loaded
    ref.listenManual(categoriesProvider, (previous, next) {
      next.whenData((categories) {
        _tabController = TabController(
          length: categories.length,
          vsync: this,
        );

        // Set the initial tab based on the selected category
        final selectedCategory = ref.read(selectedCategoryProvider);
        if (selectedCategory != null) {
          final index = categories.indexWhere((c) => c.id == selectedCategory);
          if (index != -1) {
            _tabController.animateTo(index);
          }
        }

        // Listen for tab changes
        _tabController.addListener(() {
          if (!_tabController.indexIsChanging) {
            ref.read(selectedCategoryProvider.notifier).state =
                categories[_tabController.index].id;
          }
        });
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onServiceTap(Service service) {
    context.goNamed('details', pathParameters: {'id': service.id});
  }

  void _onSearchChanged(String value) {
    ref.read(serviceSearchProvider.notifier).state = value;
  }

  void _toggleFavorite(String serviceId) {
    ref.read(favoriteServiceIdsProvider.notifier).toggleFavorite(serviceId);
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);
    // Uncomment when needed:
    // final selectedCategory = ref.watch(selectedCategoryProvider);
    final favoriteIds = ref.watch(favoriteServiceIdsProvider);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // App bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Explore',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () {
                      // TODO: Implement filters
                    },
                  ),
                ],
              ),
            ),

            // Search bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: TextField(
                controller: _searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: 'Search services...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
                ),
              ),
            ),

            // Category tabs
            categoriesAsync.when(
              data: (categories) {
                if (_tabController.length != categories.length) {
                  return const SizedBox.shrink();
                }

                return TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  indicatorSize: TabBarIndicatorSize.label,
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: Theme.of(context).colorScheme.onSurface,
                  tabs: categories.map((category) {
                    return Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(category.icon, size: 20),
                          const SizedBox(width: 8),
                          Text(category.name),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
              loading: () => const SizedBox(
                height: 48,
                child: Center(
                  child: LinearProgressIndicator(),
                ),
              ),
              error: (_, __) => const SizedBox(
                height: 48,
                child: Center(
                  child: Text('Failed to load categories'),
                ),
              ),
            ),

            // Services list
            Expanded(
              child: categoriesAsync.when(
                data: (categories) {
                  if (_tabController.length != categories.length) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return TabBarView(
                    controller: _tabController,
                    children: categories.map((category) {
                      return _CategoryServicesTab(
                        category: category,
                        onServiceTap: _onServiceTap,
                        toggleFavorite: _toggleFavorite,
                        favoriteIds: favoriteIds,
                      );
                    }).toList(),
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (_, __) => const Center(
                  child: Text('Failed to load categories'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Category services tab
class _CategoryServicesTab extends ConsumerWidget {
  final Category category;
  final void Function(Service) onServiceTap;
  final void Function(String) toggleFavorite;
  final List<String> favoriteIds;

  const _CategoryServicesTab({
    required this.category,
    required this.onServiceTap,
    required this.toggleFavorite,
    required this.favoriteIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final servicesByCategoryAsync = ref.watch(servicesByCategoryProvider(category.name));

    return servicesByCategoryAsync.when(
      data: (services) {
        if (services.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  category.icon,
                  size: 64,
                  color: category.color.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No services found in this category',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          );
        }

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: services.length,
          itemBuilder: (context, index) {
            final service = services[index];
            return AnimatedScaleButton(
              onTap: () => onServiceTap(service),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                clipBehavior: Clip.antiAlias,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Service image
                    Stack(
                      children: [
                        AspectRatio(
                          aspectRatio: 1.5,
                          child: Image.network(
                            service.imageUrl,
                            fit: BoxFit.cover,
                          ),
                        ),

                        // Favorite button
                        Positioned(
                          top: 8,
                          right: 8,
                          child: AnimatedScaleButton(
                            onTap: () => toggleFavorite(service.id),
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.8),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Icon(
                                favoriteIds.contains(service.id)
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: favoriteIds.contains(service.id)
                                    ? Colors.red
                                    : Colors.grey,
                                size: 16,
                              ),
                            ),
                          ),
                        ),

                        // Price tag
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                              ),
                            ),
                            child: Text(
                              'KSh ${service.price.toInt()}',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Service details
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            service.name,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            service.provider.name,
                            style: Theme.of(context).textTheme.bodySmall,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${service.rating}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '(${service.reviewCount})',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
            .animate()
            .fadeIn(
              duration: 600.ms,
              delay: (index * 50).ms,
              curve: Curves.easeOut,
            )
            .scale(
              begin: const Offset(0.9, 0.9),
              end: const Offset(1, 1),
              duration: 600.ms,
              delay: (index * 50).ms,
              curve: Curves.easeOut,
            );
          },
        );
      },
      loading: () => GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: 6,
        itemBuilder: (context, index) {
          return const SkeletonGridItem(
            aspectRatio: 1.5,
            hasTitle: true,
            hasSubtitle: true,
          );
        },
      ),
      error: (_, __) => const Center(
        child: Text('Failed to load services'),
      ),
    );
  }
}
