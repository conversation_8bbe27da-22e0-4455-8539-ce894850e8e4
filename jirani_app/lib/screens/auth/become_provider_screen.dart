import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:jirani_app/widgets/custom_button.dart';
import 'package:jirani_app/widgets/custom_text_field.dart'; // Assuming this exists and is suitable
import 'package:jirani_app/services/auth_service.dart'; // Changed import for authServiceProvider
import 'package:jirani_app/providers/message_provider.dart'; // Ensure this is with other imports

// Define provider types
enum ProviderType { none, bodaBoda, restaurant, plumber }

class BecomeProviderScreen extends ConsumerStatefulWidget {
  const BecomeProviderScreen({super.key});

  @override
  ConsumerState<BecomeProviderScreen> createState() => _BecomeProviderScreenState();
}

class _BecomeProviderScreenState extends ConsumerState<BecomeProviderScreen> {
  final _formKey = GlobalKey<FormState>();
  ProviderType _selectedProviderType = ProviderType.none;
  bool _isLoading = false;
  String? _errorMessage;

  // Text editing controllers
  final _licenseNumberController = TextEditingController();
  final _idNumberController = TextEditingController();
  final _restaurantNameController = TextEditingController();
  final _businessRegController = TextEditingController();
  final _foodHygieneController = TextEditingController();
  final _tradeLicenseController = TextEditingController();
  final _experienceController = TextEditingController();
  final _certificationController = TextEditingController();

  @override
  void dispose() {
    _licenseNumberController.dispose();
    _idNumberController.dispose();
    _restaurantNameController.dispose();
    _businessRegController.dispose();
    _foodHygieneController.dispose();
    _tradeLicenseController.dispose();
    _experienceController.dispose();
    _certificationController.dispose();
    super.dispose();
  }

  Future<void> _submitProviderRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    if (_selectedProviderType == ProviderType.none) {
      setState(() {
        _errorMessage = 'Please select a provider type.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider); // Assuming authServiceProvider exposes AuthService instance

      if (_selectedProviderType == ProviderType.bodaBoda) {
        await authService.registerAsDriver(
          licenseNumber: _licenseNumberController.text.trim(),
          idNumber: _idNumberController.text.trim(),
        );
        // Success message will be shown by MessageProvider
        if (mounted) {
          // ScaffoldMessenger.of(context).showSnackBar(
          //   const SnackBar(
          //     content: Text('Successfully registered as Boda Boda Driver. Account pending verification.'),
          //     backgroundColor: Colors.green,
          //   ),
          // );
          // TODO: Update local app state to reflect provider status
          Navigator.of(context).pop(); // Go back to edit profile
        }
      } else if (_selectedProviderType == ProviderType.restaurant) {
        // Placeholder for Restaurant Owner
        // MessageProvider can be used here if a specific message is desired for placeholders
        ref.read(messageProvider.notifier).showMessage(
            text: 'Restaurant Owner registration will be available soon.',
            type: MessageType.info);
      } else if (_selectedProviderType == ProviderType.plumber) {
        // Placeholder for Plumber
        ref.read(messageProvider.notifier).showMessage(
            text: 'Plumber registration will be available soon.',
            type: MessageType.info);
      }
    } catch (e) {
      // Error message will be shown by MessageProvider
      // if (mounted) {
      //   setState(() {
      //     _errorMessage = e.toString();
      //   });
      // }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Become a Service Provider'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Select Service Type',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<ProviderType>(
                value: _selectedProviderType,
                decoration: InputDecoration(
                  border: const OutlineInputBorder(),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.5),
                ),
                hint: const Text('Select a service'),
                items: const [
                  DropdownMenuItem(value: ProviderType.none, child: Text('Select a type...')),
                  DropdownMenuItem(value: ProviderType.bodaBoda, child: Text('Boda Boda Driver')),
                  DropdownMenuItem(value: ProviderType.restaurant, child: Text('Restaurant Owner')),
                  DropdownMenuItem(value: ProviderType.plumber, child: Text('Plumber')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedProviderType = value ?? ProviderType.none;
                    _errorMessage = null; // Clear error when type changes
                  });
                },
                validator: (value) {
                  if (value == null || value == ProviderType.none) {
                    return 'Please select a provider type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              if (_selectedProviderType != ProviderType.none)
                Text(
                  'Enter Details for ${_providerTypeToString(_selectedProviderType)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
              const SizedBox(height: 16),
              _buildConditionalFields(),
              // _errorMessage display can be removed as MessageProvider handles it
              // if (_errorMessage != null) ...[
              //   const SizedBox(height: 16),
              //   Text(
              //     _errorMessage!,
              //     style: TextStyle(color: Theme.of(context).colorScheme.error),
              //     textAlign: TextAlign.center,
              //   ),
              // ],
              const SizedBox(height: 32),
              CustomButton(
                text: 'Submit Registration',
                onPressed: _selectedProviderType == ProviderType.none ? null : _submitProviderRegistration,
                isLoading: _isLoading,
                width: double.infinity,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _providerTypeToString(ProviderType type) {
    switch (type) {
      case ProviderType.bodaBoda:
        return 'Boda Boda Driver';
      case ProviderType.restaurant:
        return 'Restaurant Owner';
      case ProviderType.plumber:
        return 'Plumber';
      default:
        return '';
    }
  }

  Widget _buildConditionalFields() {
    switch (_selectedProviderType) {
      case ProviderType.bodaBoda:
        return Column(
          children: [
            CustomTextField(
              controller: _licenseNumberController,
              labelText: 'License Number',
              hintText: 'Enter your driving license number',
              prefixIcon: Icons.badge_outlined,
              validator: (value) => (value == null || value.isEmpty) ? 'License number is required' : null,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _idNumberController,
              labelText: 'National ID Number',
              hintText: 'Enter your national ID number',
              prefixIcon: Icons.perm_identity_outlined,
              validator: (value) => (value == null || value.isEmpty) ? 'ID number is required' : null,
            ),
          ],
        );
      case ProviderType.restaurant:
        return Column(
          children: [
            CustomTextField(
              controller: _restaurantNameController,
              labelText: 'Restaurant Name',
              hintText: 'Enter your restaurant name',
              prefixIcon: Icons.storefront_outlined,
              validator: (value) => (value == null || value.isEmpty) ? 'Restaurant name is required' : null,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _businessRegController,
              labelText: 'Business Registration Number',
              hintText: 'Enter business registration no.',
              prefixIcon: Icons.business_center_outlined,
              validator: (value) => (value == null || value.isEmpty) ? 'Business registration is required' : null,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _foodHygieneController,
              labelText: 'Food Hygiene Certificate (Optional)',
              hintText: 'Enter certificate number if any',
              prefixIcon: Icons.health_and_safety_outlined,
            ),
          ],
        );
      case ProviderType.plumber:
        return Column(
          children: [
            CustomTextField(
              controller: _tradeLicenseController,
              labelText: 'Trade License Number',
              hintText: 'Enter your trade license number',
              prefixIcon: Icons.construction_outlined,
              validator: (value) => (value == null || value.isEmpty) ? 'Trade license is required' : null,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _experienceController,
              labelText: 'Years of Experience',
              hintText: 'Enter years of experience',
              prefixIcon: Icons.work_history_outlined,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) return 'Experience is required';
                if (int.tryParse(value) == null) return 'Enter a valid number';
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _certificationController,
              labelText: 'Certification (Optional)',
              hintText: 'Enter any relevant certification',
              prefixIcon: Icons.school_outlined,
            ),
          ],
        );
      default:
        return const SizedBox.shrink(); // No fields if no type or 'none' is selected
    }
  }
}
