import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import 'package:go_router/go_router.dart';
import 'package:jirani_app/services/auth_service.dart';
import 'package:jirani_app/widgets/custom_button.dart';

/// Verify email screen
class VerifyEmailScreen extends ConsumerStatefulWidget { // Extend ConsumerStatefulWidget
  /// Token for email verification
  final String token;

  /// Creates a new verify email screen
  const VerifyEmailScreen({super.key, required this.token});

  @override
  ConsumerState<VerifyEmailScreen> createState() => _VerifyEmailScreenState(); // Return ConsumerState
}

class _VerifyEmailScreenState extends ConsumerState<VerifyEmailScreen> { // Extend ConsumerState
  bool _isLoading = true;
  bool _isVerified = false;
  // String? _errorMessage; // Can be removed as MessageProvider will handle API errors

  @override
  void initState() {
    super.initState();
    _verifyEmail();
  }

  Future<void> _verifyEmail() async {
    setState(() {
      _isLoading = true;
      // _errorMessage = null; // No longer needed to set locally
    });

    try {
      final authService = ref.read(authServiceProvider); // Use ref.read
      await authService.verifyEmail(token: widget.token);
      
      if (mounted) { // Check if mounted before setting state
        setState(() {
          _isVerified = true;
        });
      }
    } catch (e) {
      // Error message will be shown by MessageProvider
      // if (mounted) { // Check if mounted before setting state
      //   setState(() {
      //     _errorMessage = e.toString();
      //   });
      // }
    } finally {
      if (mounted) { // Check if mounted before setting state
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Verification'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 24),
              if (_isLoading) ...[
                const Center(
                  child: CircularProgressIndicator(),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Verifying your email...',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ] else if (_isVerified) ...[
                const Icon(
                  Icons.check_circle,
                  size: 80,
                  color: Colors.green,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Email Verified!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Your email has been successfully verified. You can now log in to your account.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                CustomButton(
                  text: 'Go to Login',
                  onPressed: () => context.go('/login'),
                ),
              ] else ...[
                const Icon(
                  Icons.error,
                  size: 80,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Verification Failed',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                // _errorMessage display can be removed as MessageProvider handles it
                // Text(
                //   _errorMessage ?? 'An error occurred during verification.',
                //   style: const TextStyle(
                //     fontSize: 16,
                //     color: Colors.grey,
                //   ),
                //   textAlign: TextAlign.center,
                // ),
                // Display a generic message if _errorMessage was used for UI state
                const Text(
                  'If verification failed, please try again or contact support.',
                   style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                CustomButton(
                  text: 'Try Again',
                  onPressed: _verifyEmail,
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () => context.go('/login'),
                  child: const Text('Back to Login'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
