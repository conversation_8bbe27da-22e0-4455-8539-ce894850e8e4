import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:jirani_app/services/auth_service.dart';
import 'package:jirani_app/widgets/custom_button.dart';
import 'package:jirani_app/widgets/custom_text_field.dart';
import 'package:jirani_app/screens/auth/become_provider_screen.dart'; // Import for navigation

/// Edit profile screen
class EditProfileScreen extends ConsumerStatefulWidget {
  /// Creates a new edit profile screen
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();

  bool _isLoading = false;
  String? _errorMessage;
  File? _imageFile;
  Map<String, dynamic>? _userData;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider); // Use ref.read
      final userData = await authService.getUserProfile();

      setState(() {
        _userData = userData['user'];
        _fullNameController.text = _userData?['full_name'] ?? '';
        _phoneNumberController.text = _userData?['phone_number'] ?? '';
      });
    } catch (e) {
      if (mounted) {
        // Error message will be shown by MessageProvider
        // setState(() {
        //   _errorMessage = e.toString();
        // });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _imageFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider); // Use ref.read

      // Update profile
      await authService.updateProfile(
        fullName: _fullNameController.text,
        phoneNumber: _phoneNumberController.text,
      );
      
      // Upload profile image if selected
      if (_imageFile != null) {
        final imageBytes = await _imageFile!.readAsBytes();
        await authService.uploadProfileImage(imageBytes: imageBytes);
      }
      
      // Success message will be shown by MessageProvider
      if (mounted) {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   const SnackBar(
        //     content: Text('Profile updated successfully'),
        //     backgroundColor: Colors.green,
        //   ),
        // );
        Navigator.of(context).pop(); // Still pop the screen on success
      }
    } catch (e) {
      if (mounted) {
        // Error message will be shown by MessageProvider
        // setState(() {
        //   _errorMessage = e.toString();
        // });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
      ),
      body: _isLoading && _userData == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 24),
                    
                    // Profile image
                    GestureDetector(
                      onTap: _pickImage,
                      child: Stack(
                        children: [
                          CircleAvatar(
                            radius: 60,
                            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                            backgroundImage: _imageFile != null
                                ? FileImage(_imageFile!)
                                : _userData != null && _userData!['profile_image'] != null
                                    ? NetworkImage(_userData!['profile_image']) as ImageProvider
                                    : null,
                            child: _imageFile == null && (_userData == null || _userData!['profile_image'] == null)
                                ? Icon(
                                    Icons.person,
                                    size: 60,
                                    color: Theme.of(context).colorScheme.primary,
                                  )
                                : null,
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Full name
                    CustomTextField(
                      controller: _fullNameController,
                      labelText: 'Full Name',
                      hintText: 'Enter your full name',
                      prefixIcon: Icons.person,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your full name';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Phone number
                    CustomTextField(
                      controller: _phoneNumberController,
                      labelText: 'Phone Number',
                      hintText: 'Enter your phone number',
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        return null;
                      },
                    ),
                    
                    // _errorMessage display can be removed as MessageProvider handles it
                    // if (_errorMessage != null) ...[
                    //   const SizedBox(height: 16),
                    //   Text(
                    //     _errorMessage!,
                    //     style: TextStyle(
                    //       color: Theme.of(context).colorScheme.error,
                    //     ),
                    //   ),
                    // ],
                    
                    const SizedBox(height: 32),
                    
                    // Update button
                    CustomButton(
                      text: 'Update Profile',
                      onPressed: _updateProfile,
                      isLoading: _isLoading,
                      width: double.infinity,
                    ),

                    const SizedBox(height: 24),
                    const Divider(),
                    const SizedBox(height: 24),

                    // Section for "Become a Provider"
                    _buildBecomeProviderSection(context),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBecomeProviderSection(BuildContext context) {
    // TODO: Check if user is already a provider and display info or "Become a Provider" button
    // This will require fetching provider status, possibly from a Riverpod provider
    // that gets updated after registerAsDriver or from an enhanced getUserProfile.
    // For now, always show "Become a Provider" button.

    // Placeholder: In a real scenario, you'd check a provider status from a state management solution
    final bool isAlreadyProvider = false; // Replace with actual check
    final String providerType = "Boda Boda Driver"; // Replace with actual provider type if isAlreadyProvider is true

    if (isAlreadyProvider) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Service Provider Status',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(Icons.verified_user_outlined, color: Theme.of(context).colorScheme.primary, size: 28),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'You are registered as a $providerType.',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Optionally, add a button to "Manage Provider Settings" or "View Dashboard"
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Service Provider Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          CustomButton(
            text: 'Become a Service Provider',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const BecomeProviderScreen()),
              ).then((_) {
                // Optional: Refresh profile data or check provider status after returning
                _loadUserData(); 
              });
            },
             color: Theme.of(context).colorScheme.secondary,
            textColor: Theme.of(context).colorScheme.onSecondary,
            width: double.infinity,
 
          ),
          const SizedBox(height: 8),
          Text(
            'Offer your services on Jirani and start earning.',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      );
    }
  }
}
