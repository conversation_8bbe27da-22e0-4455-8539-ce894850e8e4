import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/category.dart';
import '../models/service.dart';
import '../providers/category_provider.dart';
import '../providers/service_provider.dart';
import '../providers/favorites_provider.dart';
import '../widgets/animations/animated_scale_button.dart';
import '../widgets/animations/parallax_card.dart';
import '../widgets/common/glassmorphic_container.dart';
import '../widgets/common/skeleton_loader.dart';

/// Home screen
class HomeScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [HomeScreen]
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onServiceTap(Service service) {
    context.goNamed('details', pathParameters: {'id': service.id});
  }

  void _onCategoryTap(Category category) {
    if (category.name == 'Food') {
      context.goNamed('food');
    } else if (category.name == 'Boda Boda') {
      // Use the modern boda boda screen instead of the old one
      context.goNamed('modern-boda');
    } else {
      ref.read(selectedCategoryProvider.notifier).state = category.id;
      context.goNamed('explore');
    }
  }

  void _onSearchChanged(String value) {
    ref.read(serviceSearchProvider.notifier).state = value;
  }

  void _toggleFavorite(String serviceId) {
    ref.read(favoriteServiceIdsProvider.notifier).toggleFavorite(serviceId);
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);
    final featuredServicesAsync = ref.watch(featuredServicesProvider);
    final popularServicesAsync = ref.watch(popularServicesProvider);
    final favoriteIds = ref.watch(favoriteServiceIdsProvider);

    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App bar
            SliverAppBar(
              floating: true,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              title: Text(
                'Jirani',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    // TODO: Implement notifications
                  },
                ),
              ],
            ),

            // Search bar
            SliverToBoxAdapter(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'Search services...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Theme.of(context)
                        .colorScheme
                        .surfaceContainerHighest
                        .withOpacity(0.3),
                  ),
                ),
              ),
            ),

            // Categories
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Categories',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    TextButton(
                      onPressed: () {
                        context.goNamed('explore');
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Categories list
            SliverToBoxAdapter(
              child: SizedBox(
                height: 120,
                child: categoriesAsync.when(
                  data: (categories) => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: AnimatedScaleButton(
                          onTap: () => _onCategoryTap(category),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 64,
                                height: 64,
                                decoration: BoxDecoration(
                                  color: category.color.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Icon(
                                  category.icon,
                                  color: category.color,
                                  size: 32,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                category.name,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        )
                            .animate()
                            .fadeIn(
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            )
                            .slideX(
                              begin: 0.2,
                              end: 0,
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            ),
                      );
                    },
                  ),
                  loading: () => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: 6,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SkeletonLoader(
                              height: 64,
                              width: 64,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(16)),
                            ),
                            const SizedBox(height: 8),
                            SkeletonLoader(
                              height: 16,
                              width: 64,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  error: (_, __) => const Center(
                    child: Text('Failed to load categories'),
                  ),
                ),
              ),
            ),

            // Featured services
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Featured',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Implement see all featured
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Featured services list
            SliverToBoxAdapter(
              child: SizedBox(
                height: 200,
                child: featuredServicesAsync.when(
                  data: (services) => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: services.length,
                    itemBuilder: (context, index) {
                      final service = services[index];
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: AnimatedScaleButton(
                          onTap: () => _onServiceTap(service),
                          child: SizedBox(
                            width: 280,
                            child: Stack(
                              children: [
                                // Card with parallax effect
                                ParallaxCard(
                                  imageUrl: service.imageUrl,
                                  title: service.name,
                                  subtitle: service.provider.name,
                                  height: 200,
                                  width: 280,
                                ),

                                // Favorite button
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: AnimatedScaleButton(
                                    onTap: () => _toggleFavorite(service.id),
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.3),
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Icon(
                                        favoriteIds.contains(service.id)
                                            ? Icons.favorite
                                            : Icons.favorite_border,
                                        color: favoriteIds.contains(service.id)
                                            ? Colors.red
                                            : Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),

                                // Price tag
                                Positioned(
                                  top: 8,
                                  left: 8,
                                  child: GlassmorphicContainer(
                                    width: 80,
                                    height: 32,
                                    borderRadius: BorderRadius.circular(16),
                                    blur: 10,
                                    opacity: 0.1,
                                    child: Center(
                                      child: Text(
                                        'KSh ${service.price.toInt()}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelMedium
                                            ?.copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                            .animate()
                            .fadeIn(
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            )
                            .slideX(
                              begin: 0.2,
                              end: 0,
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            ),
                      );
                    },
                  ),
                  loading: () => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: SkeletonLoader(
                          height: 200,
                          width: 280,
                          borderRadius: BorderRadius.circular(16),
                        ),
                      );
                    },
                  ),
                  error: (_, __) => const Center(
                    child: Text('Failed to load featured services'),
                  ),
                ),
              ),
            ),

            // Popular services
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Popular',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Implement see all popular
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Popular services list
            SliverPadding(
              padding: const EdgeInsets.only(bottom: 16),
              sliver: popularServicesAsync.when(
                data: (services) => SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final service = services[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: AnimatedScaleButton(
                          onTap: () => _onServiceTap(service),
                          child: Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  // Service image
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      service.imageUrl,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    ),
                                  ),

                                  const SizedBox(width: 16),

                                  // Service details
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          service.name,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          service.provider.name,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium,
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.star,
                                              size: 16,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .secondary,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '${service.rating} (${service.reviewCount})',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall,
                                            ),
                                            const SizedBox(width: 16),
                                            Text(
                                              'KSh ${service.price.toInt()}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Favorite button
                                  IconButton(
                                    icon: Icon(
                                      favoriteIds.contains(service.id)
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      color: favoriteIds.contains(service.id)
                                          ? Colors.red
                                          : null,
                                    ),
                                    onPressed: () =>
                                        _toggleFavorite(service.id),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                            .animate()
                            .fadeIn(
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            )
                            .slideY(
                              begin: 0.2,
                              end: 0,
                              duration: 600.ms,
                              delay: (index * 100).ms,
                              curve: Curves.easeOut,
                            ),
                      );
                    },
                    childCount: services.length,
                  ),
                ),
                loading: () => SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) => const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: SkeletonListItem(
                        hasThumbnail: true,
                        lines: 3,
                      ),
                    ),
                    childCount: 3,
                  ),
                ),
                error: (_, __) => const SliverToBoxAdapter(
                  child: Center(
                    child: Text('Failed to load popular services'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
