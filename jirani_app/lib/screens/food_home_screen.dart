import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../data/food_data.dart';
import '../models/food_item.dart';
import '../widgets/animations/animated_scale_button.dart';
import '../widgets/common/glassmorphic_container.dart';

/// Food home screen
class FoodHomeScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [FoodHomeScreen]
  const FoodHomeScreen({super.key});

  @override
  ConsumerState<FoodHomeScreen> createState() => _FoodHomeScreenState();
}

class _FoodHomeScreenState extends ConsumerState<FoodHomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<String> _favoriteIds = [];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onFoodItemTap(FoodItem foodItem) {
    // TODO: Navigate to food details
    // context.goNamed('food_details', pathParameters: {'id': foodItem.id});
  }

  void _onCategoryTap(FoodCategory category) {
    // TODO: Filter by category
  }

  void _onSearchChanged(String value) {
    // TODO: Implement search
  }

  void _toggleFavorite(String foodItemId) {
    setState(() {
      if (_favoriteIds.contains(foodItemId)) {
        _favoriteIds.remove(foodItemId);
      } else {
        _favoriteIds.add(foodItemId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App bar
            SliverAppBar(
              floating: true,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              title: Text(
                'Jirani Food',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  context.pop();
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    // TODO: Implement notifications
                  },
                ),
              ],
            ),

            // Search bar
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'Search for food...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
                  ),
                ),
              ),
            ),

            // Categories
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Categories',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Navigate to all categories
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Categories list
            SliverToBoxAdapter(
              child: SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: foodCategories.length,
                  itemBuilder: (context, index) {
                    final category = foodCategories[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: AnimatedScaleButton(
                        onTap: () => _onCategoryTap(category),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 64,
                              height: 64,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primaryContainer,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Icon(
                                category.icon,
                                color: Theme.of(context).colorScheme.primary,
                                size: 32,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              category.name,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      )
                      .slideX(
                        begin: 0.2,
                        end: 0,
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      ),
                    );
                  },
                ),
              ),
            ),

            // Featured items
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Featured',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Navigate to all featured
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Featured items list
            SliverToBoxAdapter(
              child: SizedBox(
                height: 220,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: foodItems.where((item) => item.isFeatured).length,
                  itemBuilder: (context, index) {
                    final foodItem = foodItems.where((item) => item.isFeatured).toList()[index];
                    final isFavorite = _favoriteIds.contains(foodItem.id);

                    return Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: AnimatedScaleButton(
                        onTap: () => _onFoodItemTap(foodItem),
                        child: SizedBox(
                          width: 200,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Food image with favorite button
                              Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(16),
                                    child: Image.network(
                                      foodItem.imageUrl,
                                      width: 200,
                                      height: 150,
                                      fit: BoxFit.cover,
                                    ),
                                  ),

                                  // Favorite button
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: AnimatedScaleButton(
                                      onTap: () => _toggleFavorite(foodItem.id),
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(20),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.1),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: Icon(
                                          isFavorite ? Icons.favorite : Icons.favorite_border,
                                          color: isFavorite ? Colors.red : Colors.grey,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Price tag
                                  Positioned(
                                    bottom: 8,
                                    left: 8,
                                    child: GlassmorphicContainer(
                                      width: 80,
                                      height: 32,
                                      borderRadius: BorderRadius.circular(16),
                                      blur: 10,
                                      opacity: 0.2,
                                      child: Center(
                                        child: Text(
                                          '\$${foodItem.price.toStringAsFixed(2)}',
                                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 8),

                              // Food name
                              Text(
                                foodItem.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),

                              const SizedBox(height: 4),

                              // Rating
                              Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    color: Theme.of(context).colorScheme.secondary,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${foodItem.rating} (${foodItem.reviewCount})',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${foodItem.deliveryTimeMinutes} min',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      )
                      .slideX(
                        begin: 0.2,
                        end: 0,
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      ),
                    );
                  },
                ),
              ),
            ),

            // Popular items
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Popular',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Navigate to all popular
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Popular items list
            SliverPadding(
              padding: const EdgeInsets.only(bottom: 16),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final foodItem = foodItems.where((item) => item.isPopular).toList()[index];
                    final isFavorite = _favoriteIds.contains(foodItem.id);

                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: AnimatedScaleButton(
                        onTap: () => _onFoodItemTap(foodItem),
                        child: Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                // Food image
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    foodItem.imageUrl,
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                  ),
                                ),

                                const SizedBox(width: 16),

                                // Food details
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        foodItem.name,
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        foodItem.description,
                                        style: Theme.of(context).textTheme.bodySmall,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.star,
                                            size: 16,
                                            color: Theme.of(context).colorScheme.secondary,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${foodItem.rating}',
                                            style: Theme.of(context).textTheme.bodySmall,
                                          ),
                                          const SizedBox(width: 16),
                                          Icon(
                                            Icons.access_time,
                                            size: 16,
                                            color: Theme.of(context).colorScheme.secondary,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${foodItem.deliveryTimeMinutes} min',
                                            style: Theme.of(context).textTheme.bodySmall,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),

                                // Price and favorite
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      '\$${foodItem.price.toStringAsFixed(2)}',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    IconButton(
                                      icon: Icon(
                                        isFavorite ? Icons.favorite : Icons.favorite_border,
                                        color: isFavorite ? Colors.red : null,
                                      ),
                                      onPressed: () => _toggleFavorite(foodItem.id),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      )
                      .slideY(
                        begin: 0.2,
                        end: 0,
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      ),
                    );
                  },
                  childCount: foodItems.where((item) => item.isPopular).length,
                ),
              ),
            ),

            // Restaurants
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Restaurants',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Navigate to all restaurants
                      },
                      child: const Text('See All'),
                    ),
                  ],
                ),
              ),
            ),

            // Restaurants list
            SliverToBoxAdapter(
              child: SizedBox(
                height: 220,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: restaurants.where((r) => r.isFeatured).length,
                  itemBuilder: (context, index) {
                    final restaurant = restaurants.where((r) => r.isFeatured).toList()[index];

                    return Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: AnimatedScaleButton(
                        onTap: () {
                          // TODO: Navigate to restaurant details
                        },
                        child: SizedBox(
                          width: 200,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Restaurant image
                              Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(16),
                                    child: Image.network(
                                      restaurant.coverImageUrl,
                                      width: 200,
                                      height: 120,
                                      fit: BoxFit.cover,
                                    ),
                                  ),

                                  // Restaurant logo
                                  Positioned(
                                    bottom: -20,
                                    left: 10,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 2,
                                        ),
                                      ),
                                      child: CircleAvatar(
                                        radius: 20,
                                        backgroundImage: NetworkImage(restaurant.logoUrl),
                                      ),
                                    ),
                                  ),

                                  // Delivery time
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: GlassmorphicContainer(
                                      width: 70,
                                      height: 30,
                                      borderRadius: BorderRadius.circular(15),
                                      blur: 10,
                                      opacity: 0.2,
                                      child: Center(
                                        child: Text(
                                          '${restaurant.deliveryTimeMinutes} min',
                                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 24),

                              // Restaurant name
                              Text(
                                restaurant.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),

                              const SizedBox(height: 4),

                              // Rating and delivery fee
                              Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    color: Theme.of(context).colorScheme.secondary,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${restaurant.rating}',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '\$${restaurant.deliveryFee.toStringAsFixed(2)} delivery',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                      .animate()
                      .fadeIn(
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      )
                      .slideX(
                        begin: 0.2,
                        end: 0,
                        duration: 600.ms,
                        delay: (index * 100).ms,
                        curve: Curves.easeOut,
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
