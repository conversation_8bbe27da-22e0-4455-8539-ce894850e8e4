import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';

/// Splash screen
class SplashScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [SplashScreen]
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    // Simulate loading
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;

    // Check if user is logged in
    // Use ref.read to get the AuthService instance
    final authService = ref.read(authServiceProvider);
    final isLoggedIn = await authService.isLoggedIn();

    if (!mounted) return;

    if (isLoggedIn) {
      // User is logged in, go to home
      context.goNamed('home');
    } else {
      // Check if user has seen onboarding
      final hasSeenOnboarding = await _hasSeenOnboarding();

      if (!mounted) return;

      if (hasSeenOnboarding) {
        // User has seen onboarding, go to login
        context.goNamed('login');
      } else {
        // User has not seen onboarding, go to onboarding
        context.goNamed('onboarding');
      }
    }
  }

  Future<bool> _hasSeenOnboarding() async {
    // TODO: Implement this with shared preferences
    // For now, always return false to show onboarding
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primaryContainer,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Center(
                  child: Image.asset(
                    'assets/images/jirani_logo.png',
                    width: 80,
                    height: 80,
                  ),
                ),
              )
              .animate()
              .scale(
                duration: 600.ms,
                curve: Curves.easeOut,
                begin: const Offset(0.8, 0.8),
                end: const Offset(1, 1),
              )
              .then(delay: 200.ms)
              .shimmer(
                duration: 1200.ms,
                color: Colors.white.withOpacity(0.8),
              ),

              const SizedBox(height: 32),

              // App name
              Text(
                'Jirani',
                style: Theme.of(context).textTheme.displayMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                curve: Curves.easeOut,
                delay: 300.ms,
              )
              .slideY(
                begin: 0.2,
                end: 0,
                duration: 600.ms,
                curve: Curves.easeOut,
                delay: 300.ms,
              ),

              const SizedBox(height: 8),

              // Tagline
              Text(
                'Your neighborhood services',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                curve: Curves.easeOut,
                delay: 500.ms,
              )
              .slideY(
                begin: 0.2,
                end: 0,
                duration: 600.ms,
                curve: Curves.easeOut,
                delay: 500.ms,
              ),

              const SizedBox(height: 64),

              // Loading indicator
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                curve: Curves.easeOut,
                delay: 800.ms,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
