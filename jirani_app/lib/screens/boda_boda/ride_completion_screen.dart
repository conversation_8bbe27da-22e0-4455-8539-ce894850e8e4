import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/theme.dart';
import '../../models/ride.dart';
import '../../models/rider.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';
import '../../providers/ride_provider.dart';
import '../../services/logging_service.dart';

/// Step 10: Trip Completion Screen
/// Shows trip summary, payment, and rating interface
class RideCompletionScreen extends ConsumerStatefulWidget {
  final Ride ride;
  final Rider? rider;

  const RideCompletionScreen({
    super.key,
    required this.ride,
    this.rider,
  });

  @override
  ConsumerState<RideCompletionScreen> createState() =>
      _RideCompletionScreenState();
}

class _RideCompletionScreenState extends ConsumerState<RideCompletionScreen>
    with TickerProviderStateMixin {
  late AnimationController _successAnimationController;
  late AnimationController _ratingAnimationController;
  late Animation<double> _successAnimation;
  late Animation<double> _ratingAnimation;

  int _selectedRating = 0;
  final TextEditingController _reviewController = TextEditingController();
  bool _isSubmittingRating = false;
  bool _showPaymentDetails = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _showSuccessAnimation();
  }

  @override
  void dispose() {
    _successAnimationController.dispose();
    _ratingAnimationController.dispose();
    _reviewController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _ratingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successAnimationController,
      curve: Curves.elasticOut,
    ));

    _ratingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _ratingAnimationController,
      curve: Curves.easeOut,
    ));
  }

  void _showSuccessAnimation() {
    _successAnimationController.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _ratingAnimationController.forward();
      });
    });
  }

  void _selectRating(int rating) {
    setState(() {
      _selectedRating = rating;
    });
  }

  void _submitRating() async {
    if (_selectedRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a rating')),
      );
      return;
    }

    setState(() {
      _isSubmittingRating = true;
    });

    try {
      await ref.read(currentRideProvider.notifier).rateRide(
            rating: _selectedRating,
            review: _reviewController.text.trim().isEmpty
                ? null
                : _reviewController.text.trim(),
          );

      // Navigate back to home
      Navigator.of(context).popUntil((route) => route.isFirst);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Thank you for your feedback!')),
      );
    } catch (e) {
      LoggingService.e('Failed to submit rating', error: e);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Failed to submit rating. Please try again.')),
      );
    } finally {
      setState(() {
        _isSubmittingRating = false;
      });
    }
  }

  void _togglePaymentDetails() {
    setState(() {
      _showPaymentDetails = !_showPaymentDetails;
    });
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Success Animation
                AnimatedBuilder(
                  animation: _successAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _successAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.3),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 60,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 30),

                Text(
                  'Trip Completed!',
                  style: GoogleFonts.inter(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 10),

                Text(
                  'You have arrived at your destination',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // Trip Summary Card
                GlassmorphicContainer(
                  borderRadius: BorderRadius.circular(16),
                  blur: 10,
                  opacity: 0.2,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Trip Summary',
                          style: GoogleFonts.inter(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),

                        const SizedBox(height: 20),

                        _buildSummaryRow(
                          'Distance',
                          '${widget.ride.distance.toStringAsFixed(1)} km',
                          Icons.straighten,
                        ),

                        _buildSummaryRow(
                          'Duration',
                          _formatDuration(widget.ride.duration),
                          Icons.access_time,
                        ),

                        _buildSummaryRow(
                          'Fare',
                          'KES ${widget.ride.actualFare.toStringAsFixed(0)}',
                          Icons.payments,
                        ),

                        if (widget.rider != null) ...[
                          const SizedBox(height: 16),
                          const Divider(color: Colors.white24),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundImage:
                                    NetworkImage(widget.rider!.imageUrl),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.rider!.name,
                                      style: GoogleFonts.inter(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                    Text(
                                      '${widget.rider!.motorcycleModel} • ${widget.rider!.licensePlate}',
                                      style: GoogleFonts.inter(
                                        fontSize: 12,
                                        color: Colors.white70,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],

                        const SizedBox(height: 16),

                        // Payment Details Toggle
                        AnimatedScaleButton(
                          onTap: _togglePaymentDetails,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Payment Details',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                              ),
                              Icon(
                                _showPaymentDetails
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: Colors.white70,
                              ),
                            ],
                          ),
                        ),

                        if (_showPaymentDetails) ...[
                          const SizedBox(height: 12),
                          _buildPaymentDetails(),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // Rating Section
                AnimatedBuilder(
                  animation: _ratingAnimation,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, 50 * (1 - _ratingAnimation.value)),
                      child: Opacity(
                        opacity: _ratingAnimation.value,
                        child: GlassmorphicContainer(
                          borderRadius: BorderRadius.circular(16),
                          blur: 10,
                          opacity: 0.2,
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Rate Your Ride',
                                  style: GoogleFonts.inter(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Star Rating
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(5, (index) {
                                    final starIndex = index + 1;
                                    return AnimatedScaleButton(
                                      onTap: () => _selectRating(starIndex),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        child: Icon(
                                          starIndex <= _selectedRating
                                              ? Icons.star
                                              : Icons.star_border,
                                          color: starIndex <= _selectedRating
                                              ? Colors.amber
                                              : Colors.white54,
                                          size: 40,
                                        ),
                                      ),
                                    );
                                  }),
                                ),

                                const SizedBox(height: 20),

                                // Review Text Field
                                TextField(
                                  controller: _reviewController,
                                  style: GoogleFonts.inter(color: Colors.white),
                                  maxLines: 3,
                                  decoration: InputDecoration(
                                    hintText:
                                        'Share your experience (optional)',
                                    hintStyle: GoogleFonts.inter(
                                        color: Colors.white54),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide:
                                          BorderSide(color: Colors.white24),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide:
                                          BorderSide(color: Colors.white24),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                          color: AppTheme.primaryColor),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 20),

                                // Submit Button
                                AnimatedScaleButton(
                                  onTap: _isSubmittingRating
                                      ? null
                                      : _submitRating,
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    decoration: BoxDecoration(
                                      gradient: _selectedRating > 0
                                          ? AppTheme.primaryGradient
                                          : LinearGradient(
                                              colors: [
                                                Colors.grey,
                                                Colors.grey.shade600
                                              ],
                                            ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: _isSubmittingRating
                                        ? const Center(
                                            child: SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                color: Colors.white,
                                                strokeWidth: 2,
                                              ),
                                            ),
                                          )
                                        : Text(
                                            'Submit Rating',
                                            textAlign: TextAlign.center,
                                            style: GoogleFonts.inter(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 20),

                // Skip Rating Button
                TextButton(
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  child: Text(
                    'Skip Rating',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70, size: 20),
          const SizedBox(width: 12),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Column(
      children: [
        _buildPaymentRow('Base Fare', 'KES 100.00'),
        _buildPaymentRow(
            'Distance (${widget.ride.distance.toStringAsFixed(1)} km)',
            'KES ${(widget.ride.actualFare - 100).toStringAsFixed(0)}'),
        if (widget.ride.surgeFactor > 1)
          _buildPaymentRow('Surge (${widget.ride.surgeFactor}x)',
              'KES ${((widget.ride.actualFare * widget.ride.surgeFactor) - widget.ride.actualFare).toStringAsFixed(0)}'),
        const Divider(color: Colors.white24),
        _buildPaymentRow(
            'Total', 'KES ${widget.ride.actualFare.toStringAsFixed(0)}',
            isTotal: true),
      ],
    );
  }

  Widget _buildPaymentRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal ? Colors.white : Colors.white70,
            ),
          ),
          Text(
            amount,
            style: GoogleFonts.inter(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal ? Colors.white : Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}
