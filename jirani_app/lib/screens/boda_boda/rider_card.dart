import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/rider.dart';
import '../../providers/location_provider.dart';
import '../../services/rider_service.dart';
import '../../widgets/animations/animated_scale_button.dart';

/// A card widget displaying rider information
class RiderCard extends ConsumerWidget {
  /// The rider to display
  final Rider rider;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Creates a new instance of [RiderCard]
  const RiderCard({
    super.key,
    required this.rider,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userLocationAsync = ref.watch(userLocationProvider);

    // Calculate distance from user
    double? distanceFromUser;
    if (userLocationAsync is AsyncData && userLocationAsync.value != null) {
      distanceFromUser = RiderService.calculateDistance(
        lat1: userLocationAsync.value!.latitude,
        lon1: userLocationAsync.value!.longitude,
        lat2: rider.location.latitude,
        lon2: rider.location.longitude,
      );
    }

    return AnimatedScaleButton(
      onTap: onTap,
      child: Card(
        margin: const EdgeInsets.only(bottom: 8),
        color: Colors.white.withOpacity(0.9),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Rider image
              CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(rider.imageUrl),
              ),
              const SizedBox(width: 12),

              // Rider info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      rider.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${rider.rating.toStringAsFixed(1)} (${rider.reviewCount})',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(width: 8),
                        if (distanceFromUser != null) ...[
                          const Icon(
                            Icons.location_on,
                            color: Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${distanceFromUser.toStringAsFixed(1)} km',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Call button
              IconButton(
                icon: const Icon(
                  Icons.phone,
                  color: Colors.green,
                ),
                onPressed: () async {
                  final phoneUri = Uri(
                    scheme: 'tel',
                    path: rider.phoneNumber,
                  );

                  if (await canLaunchUrl(phoneUri)) {
                    await launchUrl(phoneUri);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
