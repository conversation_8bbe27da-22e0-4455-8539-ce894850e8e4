import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Size;

import '../../app/theme.dart';
import '../../models/lat_lng.dart' as custom;
import '../../models/place_result.dart';
import '../../providers/location_provider.dart' as legacy_location;
import '../../providers/enhanced_location_provider.dart' as enhanced_location;
import '../../services/mapbox_service_new.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';

import 'fare_estimation_screen.dart';

/// Screen for selecting pickup and dropoff locations
class LocationSelectionScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [LocationSelectionScreen]
  const LocationSelectionScreen({super.key});

  @override
  ConsumerState<LocationSelectionScreen> createState() =>
      _LocationSelectionScreenState();
}

class _LocationSelectionScreenState
    extends ConsumerState<LocationSelectionScreen> {
  MapboxMap? _mapController;
  bool _isMapInitialized = false;
  bool _isPickupSelected = true;
  bool _isSearchFocused = false;
  bool _showTraffic = true; // Default to showing traffic
  final TextEditingController _pickupController = TextEditingController();
  final TextEditingController _dropoffController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _pickupFocusNode = FocusNode();
  final FocusNode _dropoffFocusNode = FocusNode();
  final FocusNode _searchFocusNode = FocusNode();
  PointAnnotationManager? _annotationManager;
  List<PlaceResult> _searchResults = [];
  bool _isSearching = false;
  Timer? _searchDebounce;

  @override
  void initState() {
    super.initState();
    _pickupFocusNode.addListener(_onPickupFocusChange);
    _dropoffFocusNode.addListener(_onDropoffFocusChange);

    // Load real user location history and current location
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRealUserData();
    });
  }

  Future<void> _loadRealUserData() async {
    try {
      // Load user's location history
      final locationHistoryNotifier =
          ref.read(enhanced_location.locationHistoryStateProvider.notifier);
      await locationHistoryNotifier.refresh();

      // Get recent locations
      final recentLocations =
          ref.read(enhanced_location.recentLocationsProvider);
      if (recentLocations.isNotEmpty) {
        final recentPickup = recentLocations
            .where((loc) => loc.locationType == 'pickup')
            .firstOrNull;
        if (recentPickup != null) {
          final pickupLatLng =
              custom.LatLng(recentPickup.latitude, recentPickup.longitude);
          ref
              .read(legacy_location.pickupLocationProvider.notifier)
              .setLocation(pickupLatLng);
          _pickupController.text = recentPickup.address;
        }

        final recentDropoff = recentLocations
            .where((loc) => loc.locationType == 'destination')
            .firstOrNull;
        if (recentDropoff != null) {
          final dropoffLatLng =
              custom.LatLng(recentDropoff.latitude, recentDropoff.longitude);
          ref
              .read(legacy_location.dropoffLocationProvider.notifier)
              .setLocation(dropoffLatLng);
          _dropoffController.text = recentDropoff.address;
        }
      }

      // Get current location if no recent pickup
      if (ref.read(legacy_location.pickupLocationProvider) == null) {
        await _setCurrentLocationAsPickup();
      }

      _updateMapMarkers();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading real user data: $e');
      }
      // Fallback to current location
      await _setCurrentLocationAsPickup();
    }
  }

  Future<void> _setCurrentLocationAsPickup() async {
    try {
      final locationService =
          ref.read(enhanced_location.enhancedLocationServiceProvider);
      final currentLocation = await locationService.getCurrentLocation();

      if (currentLocation.latitude != null &&
          currentLocation.longitude != null) {
        final address = await locationService.getAddressFromCoordinates(
          currentLocation.latitude!,
          currentLocation.longitude!,
        );

        final currentLatLng = custom.LatLng(
            currentLocation.latitude!, currentLocation.longitude!);
        ref
            .read(legacy_location.pickupLocationProvider.notifier)
            .setLocation(currentLatLng);
        _pickupController.text = address;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current location: $e');
      }
    }
  }

  @override
  void dispose() {
    _pickupController.dispose();
    _dropoffController.dispose();
    _searchController.dispose();
    _pickupFocusNode.removeListener(_onPickupFocusChange);
    _dropoffFocusNode.removeListener(_onDropoffFocusChange);
    _pickupFocusNode.dispose();
    _dropoffFocusNode.dispose();
    _searchFocusNode.dispose();
    _searchDebounce?.cancel();
    super.dispose();
  }

  /// Search for places based on the query
  void _searchPlaces(String query) {
    // Cancel any previous search
    _searchDebounce?.cancel();

    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Debounce the search to avoid too many API calls
    _searchDebounce = Timer(const Duration(milliseconds: 500), () async {
      try {
        // Get the user's current location for better local results
        final userLocation = ref.read(enhanced_location.userLocationProvider);
        custom.LatLng? proximity;

        if (userLocation is AsyncData && userLocation.value != null) {
          final locationData = userLocation.value!;
          if (locationData.latitude != null && locationData.longitude != null) {
            proximity =
                custom.LatLng(locationData.latitude!, locationData.longitude!);
          }
        }

        // Get place suggestions
        final results = await MapboxServiceNew.getPlaceSuggestions(
          query,
          proximity: proximity,
        );

        if (mounted) {
          setState(() {
            _searchResults = results;
            _isSearching = false;
          });
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error searching places: $e');
        }
        if (mounted) {
          setState(() {
            _isSearching = false;
          });
        }
      }
    });
  }

  /// Select a place from search results
  void _selectSearchResult(PlaceResult result) {
    final latLng = custom.LatLng(result.latitude, result.longitude);

    if (_isPickupSelected) {
      ref
          .read(legacy_location.pickupLocationProvider.notifier)
          .setLocation(latLng);
      _pickupController.text = result.name;
    } else {
      ref
          .read(legacy_location.dropoffLocationProvider.notifier)
          .setLocation(latLng);
      _dropoffController.text = result.name;
    }

    // Close search and move map to selected location
    setState(() {
      _searchResults = [];
      _isSearchFocused = false;
      _searchController.clear();
    });

    FocusScope.of(context).unfocus();
    _moveToLocation(latLng);
    _updateMapMarkers();
  }

  void _onPickupFocusChange() {
    if (_pickupFocusNode.hasFocus) {
      setState(() {
        _isPickupSelected = true;
        _isSearchFocused = true;
      });
    } else if (!_dropoffFocusNode.hasFocus) {
      setState(() {
        _isSearchFocused = false;
      });
    }
  }

  void _onDropoffFocusChange() {
    if (_dropoffFocusNode.hasFocus) {
      setState(() {
        _isPickupSelected = false;
        _isSearchFocused = true;
      });
    } else if (!_pickupFocusNode.hasFocus) {
      setState(() {
        _isSearchFocused = false;
      });
    }
  }

  void _onMapCreated(MapboxMap controller) {
    _mapController = controller;
    setState(() {
      _isMapInitialized = true;
    });

    _initializeAnnotationManager();

    // Add traffic data to the map
    MapboxServiceNew.addTrafficLayer(controller);

    // Move to user's location when available
    final userLocation = ref.read(enhanced_location.userLocationProvider);
    if (userLocation is AsyncData && userLocation.value != null) {
      final locationData = userLocation.value!;
      if (locationData.latitude != null && locationData.longitude != null) {
        final customLatLng =
            custom.LatLng(locationData.latitude!, locationData.longitude!);
        _moveToLocation(customLatLng);
      }
    }
  }

  Future<void> _initializeAnnotationManager() async {
    if (_mapController != null) {
      _annotationManager =
          await _mapController!.annotations.createPointAnnotationManager();
      _updateMapMarkers();
    }
  }

  void _moveToLocation(custom.LatLng location) {
    if (_mapController != null) {
      _mapController!.flyTo(
        CameraOptions(
          center: MapboxServiceNew.latLngToPoint(
              location.latitude, location.longitude),
          zoom: 15.0,
        ),
        MapAnimationOptions(duration: 1000),
      );
    }
  }

  void _updateMapMarkers() {
    if (!_isMapInitialized ||
        _mapController == null ||
        _annotationManager == null) return;

    // Clear existing markers
    _annotationManager!.deleteAll();

    final options = <PointAnnotationOptions>[];

    // Add pickup marker if available
    final pickupLocation = ref.read(legacy_location.pickupLocationProvider);
    if (pickupLocation != null) {
      options.add(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            pickupLocation.latitude,
            pickupLocation.longitude,
          ),
          iconImage: 'assets/icons/marker.png',
          iconSize: 1.5,
          iconAnchor: IconAnchor.BOTTOM,
          textField: "P",
          textColor: AppTheme.primaryColor.value,
        ),
      );
    }

    // Add dropoff marker if available
    final dropoffLocation = ref.read(legacy_location.dropoffLocationProvider);
    if (dropoffLocation != null) {
      options.add(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            dropoffLocation.latitude,
            dropoffLocation.longitude,
          ),
          iconImage: 'assets/icons/marker.png',
          iconSize: 1.5,
          iconAnchor: IconAnchor.BOTTOM,
          textField: "D",
          textColor: Colors.red.value,
        ),
      );
    }

    // Add all markers at once
    if (options.isNotEmpty) {
      _annotationManager!.createMulti(options);
    }
  }

  void _onMapTap(Map<String, dynamic> point) async {
    // Convert point to LatLng
    final coordinates = point['coordinates'] as Map<String, dynamic>;
    final lat = coordinates['lat'] as num;
    final lng = coordinates['lng'] as num;
    final location = custom.LatLng(lat.toDouble(), lng.toDouble());

    // Update the selected location (pickup or dropoff)
    if (_isPickupSelected) {
      ref
          .read(legacy_location.pickupLocationProvider.notifier)
          .setLocation(location);

      // Get address for the location
      _pickupController.text =
          'Pickup (${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)})';
    } else {
      ref
          .read(legacy_location.dropoffLocationProvider.notifier)
          .setLocation(location);

      // Get address for the location
      _dropoffController.text =
          'Dropoff (${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)})';
    }

    // Update map markers
    _updateMapMarkers();
  }

  void _onUseCurrentLocation() {
    // Get user's current location
    final userLocation = ref.read(enhanced_location.userLocationProvider);
    if (userLocation is AsyncData && userLocation.value != null) {
      final locationData = userLocation.value!;
      if (locationData.latitude != null && locationData.longitude != null) {
        final customLatLng =
            custom.LatLng(locationData.latitude!, locationData.longitude!);
        ref
            .read(legacy_location.pickupLocationProvider.notifier)
            .setLocation(customLatLng);

        // Update pickup text
        _pickupController.text =
            'Current Location (${customLatLng.latitude.toStringAsFixed(4)}, ${customLatLng.longitude.toStringAsFixed(4)})';

        // Move map to current location
        if (_mapController != null) {
          _mapController!.flyTo(
            CameraOptions(
              center: MapboxServiceNew.latLngToPoint(
                customLatLng.latitude,
                customLatLng.longitude,
              ),
              zoom: 15.0,
            ),
            MapAnimationOptions(duration: 1000),
          );
        }
      }
    }

    // Update map markers
    _updateMapMarkers();
  }

  void _onContinue() {
    final pickupLocation = ref.read(legacy_location.pickupLocationProvider);
    final dropoffLocation = ref.read(legacy_location.dropoffLocationProvider);

    if (pickupLocation != null && dropoffLocation != null) {
      // Cache map tiles for offline use along the route
      _cacheMapTilesForRoute(pickupLocation, dropoffLocation);

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const FareEstimationScreen(),
        ),
      );
    } else {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select both pickup and dropoff locations'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Cache map tiles for the route between pickup and dropoff locations
  Future<void> _cacheMapTilesForRoute(
      custom.LatLng pickup, custom.LatLng dropoff) async {
    if (_mapController == null) return;

    try {
      // Show a loading indicator
      final snackBar = SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            const Text('Caching map data for offline use...'),
          ],
        ),
        duration: const Duration(seconds: 2),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);

      // Calculate the center point between pickup and dropoff
      final centerLat = (pickup.latitude + dropoff.latitude) / 2;
      final centerLng = (pickup.longitude + dropoff.longitude) / 2;
      final center = Point(coordinates: Position(centerLng, centerLat));

      // Calculate the distance between pickup and dropoff (rough estimate)
      final latDiff = (pickup.latitude - dropoff.latitude).abs();
      final lngDiff = (pickup.longitude - dropoff.longitude).abs();
      final distance =
          sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111.32; // Convert to km

      // Add some buffer to ensure we cache enough area
      final radius = distance * 1.5;

      // Cache the map tiles
      await MapboxServiceNew.cacheMapTiles(
        _mapController!,
        center,
        15.0, // Zoom level
        radius.clamp(2.0, 20.0), // Limit the radius to a reasonable range
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error caching map tiles: $e');
      }
    }
  }

  Widget _buildSavedLocationsList() {
    // In a real app, this would come from a database or API
    final savedLocations = [
      {
        'name': 'Home',
        'address': '123 Main St, Nairobi',
        'lat': -1.2864,
        'lng': 36.8172,
      },
      {
        'name': 'Work',
        'address': '456 Business Ave, Nairobi',
        'lat': -1.2673,
        'lng': 36.8035,
      },
      {
        'name': 'Gym',
        'address': '789 Fitness Rd, Nairobi',
        'lat': -1.2841,
        'lng': 36.7883,
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: savedLocations.length,
      itemBuilder: (context, index) {
        final location = savedLocations[index];
        return InkWell(
          onTap: () {
            // Set the location
            final latLng = custom.LatLng(
              location['lat'] as double,
              location['lng'] as double,
            );

            if (_isPickupSelected) {
              ref
                  .read(legacy_location.pickupLocationProvider.notifier)
                  .setLocation(latLng);
              _pickupController.text = location['name'] as String;
            } else {
              ref
                  .read(legacy_location.dropoffLocationProvider.notifier)
                  .setLocation(latLng);
              _dropoffController.text = location['name'] as String;
            }

            // Unfocus and hide search results
            FocusScope.of(context).unfocus();
            setState(() {
              _isSearchFocused = false;
            });

            // Update map markers
            _updateMapMarkers();

            // Move map to the selected location
            _moveToLocation(latLng);
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Icon(
                    _getIconForName(location['name'] as String),
                    color: AppTheme.primaryColor,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        location['name'] as String,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        location['address'] as String,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentLocationsList() {
    // In a real app, this would come from a database or API
    final recentLocations = [
      {
        'name': 'Nairobi CBD',
        'address': 'Central Business District, Nairobi',
        'lat': -1.2864,
        'lng': 36.8172,
      },
      {
        'name': 'Westlands',
        'address': 'Westlands, Nairobi',
        'lat': -1.2673,
        'lng': 36.8035,
      },
      {
        'name': 'Kilimani',
        'address': 'Kilimani, Nairobi',
        'lat': -1.2841,
        'lng': 36.7883,
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recentLocations.length,
      itemBuilder: (context, index) {
        final location = recentLocations[index];
        return InkWell(
          onTap: () {
            // Set the location
            final latLng = custom.LatLng(
              location['lat'] as double,
              location['lng'] as double,
            );

            if (_isPickupSelected) {
              ref
                  .read(legacy_location.pickupLocationProvider.notifier)
                  .setLocation(latLng);
              _pickupController.text = location['name'] as String;
            } else {
              ref
                  .read(legacy_location.dropoffLocationProvider.notifier)
                  .setLocation(latLng);
              _dropoffController.text = location['name'] as String;
            }

            // Unfocus and hide search results
            FocusScope.of(context).unfocus();
            setState(() {
              _isSearchFocused = false;
            });

            // Update map markers
            _updateMapMarkers();

            // Move map to the selected location
            _moveToLocation(latLng);
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Icon(
                    Icons.history,
                    color: Colors.grey[700],
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        location['name'] as String,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        location['address'] as String,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getIconForName(String name) {
    switch (name.toLowerCase()) {
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'gym':
        return Icons.fitness_center;
      default:
        return Icons.location_on;
    }
  }

  @override
  Widget build(BuildContext context) {
    final userLocationAsync = ref.watch(enhanced_location.userLocationProvider);
    // Watch these providers to rebuild when they change
    ref.watch(legacy_location.pickupLocationProvider);
    ref.watch(legacy_location.dropoffLocationProvider);

    // Update map markers when locations change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateMapMarkers();
    });

    // Define the primary color from the app theme
    final primaryColor = AppTheme.primaryColor;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 140,
        automaticallyImplyLeading: false,
        flexibleSpace: Padding(
          padding: const EdgeInsets.only(top: 40, left: 16, right: 16),
          child: Column(
            children: [
              // Glassmorphic app bar with back button
              GlassmorphicContainer(
                width: double.infinity,
                height: 50,
                borderRadius: BorderRadius.circular(16),
                blur: 10,
                opacity: 0.2,
                child: Row(
                  children: [
                    Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                        iconSize: 20,
                      ),
                    ),
                    const Expanded(
                      child: Center(
                        child: Text(
                          'Select Location',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 40), // Balance the layout
                  ],
                ),
              ),
              const SizedBox(height: 8),
              // Location input container
              GlassmorphicContainer(
                width: double.infinity,
                height: 80,
                borderRadius: BorderRadius.circular(16),
                blur: 10,
                opacity: 0.2,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Pickup input (compact)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPickupSelected = true;
                            _isSearchFocused = true;
                          });
                        },
                        child: Row(
                          children: [
                            Icon(Icons.circle, color: primaryColor, size: 12),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _pickupController.text.isEmpty
                                    ? 'Pickup location'
                                    : _pickupController.text,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        child: Divider(height: 1, color: Colors.white30),
                      ),
                      // Dropoff input (compact)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPickupSelected = false;
                            _isSearchFocused = true;
                          });
                        },
                        child: Row(
                          children: [
                            const Icon(Icons.place,
                                color: Colors.red, size: 12),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _dropoffController.text.isEmpty
                                    ? 'Where to?'
                                    : _dropoffController.text,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          // Map
          Positioned.fill(
            child: userLocationAsync.when(
              data: (location) {
                if (location.latitude == null || location.longitude == null) {
                  return const Center(
                    child: Text('Location not available'),
                  );
                }

                return GestureDetector(
                  onTap: () {
                    // Unfocus text fields when tapping on the map
                    FocusScope.of(context).unfocus();
                    setState(() {
                      _isSearchFocused = false;
                    });
                  },
                  child: MapboxServiceNew.createMapView(
                    initialCameraPosition: MapboxServiceNew.latLngToPoint(
                        location.latitude!, location.longitude!),
                    initialZoom: 15.0,
                    myLocationEnabled: true,
                    compassEnabled: true,
                    logoEnabled: false,
                    attributionEnabled: false,
                    styleUri: MapboxServiceNew.customMapStyle,
                    onMapCreated: _onMapCreated,
                    onMapClick: (point) => _onMapTap(point),
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, _) => Center(
                child: Text('Error: $error'),
              ),
            ),
          ),

          // Map control buttons
          Positioned(
            bottom: 120,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Traffic toggle button
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      _showTraffic ? Icons.traffic : Icons.traffic_outlined,
                      color: _showTraffic ? AppTheme.primaryColor : Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _showTraffic = !_showTraffic;
                      });

                      if (_mapController != null) {
                        if (_showTraffic) {
                          MapboxServiceNew.addTrafficLayer(_mapController!);
                        } else {
                          MapboxServiceNew.removeTrafficLayer(_mapController!);
                        }
                      }
                    },
                  ),
                ),

                // Current location button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(Icons.my_location, color: AppTheme.primaryColor),
                    onPressed: _onUseCurrentLocation,
                  ),
                ),
              ],
            ),
          ),

          // Location search panel (only visible when focused)
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            top: _isSearchFocused ? 80 : -300, // Slide in from top when focused
            left: 16,
            right: 16,
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Search fields
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Pickup input
                        TextField(
                          controller: _pickupController,
                          focusNode: _pickupFocusNode,
                          onTap: () {
                            setState(() {
                              _isPickupSelected = true;
                              _isSearchFocused = true;
                            });
                          },
                          decoration: InputDecoration(
                            hintText: 'Pickup location',
                            prefixIcon: Icon(Icons.circle,
                                color: AppTheme.primaryColor, size: 12),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.my_location, size: 18),
                              onPressed: _onUseCurrentLocation,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Colors.grey[100],
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Dropoff input
                        TextField(
                          controller: _dropoffController,
                          focusNode: _dropoffFocusNode,
                          onTap: () {
                            setState(() {
                              _isPickupSelected = false;
                              _isSearchFocused = true;
                            });
                          },
                          decoration: InputDecoration(
                            hintText: 'Where to?',
                            prefixIcon: const Icon(Icons.place,
                                color: Colors.red, size: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Colors.grey[100],
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Search box
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        hintText: _isPickupSelected
                            ? 'Search for pickup location'
                            : 'Search for dropoff location',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _isSearching
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchResults = [];
                                  });
                                },
                              ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      onChanged: _searchPlaces,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Locations list
                  Expanded(
                    child: _searchResults.isNotEmpty
                        ? ListView.builder(
                            itemCount: _searchResults.length,
                            itemBuilder: (context, index) {
                              final result = _searchResults[index];
                              return ListTile(
                                leading: const Icon(Icons.location_on),
                                title: Text(result.name),
                                subtitle: Text(
                                  result.address,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                onTap: () => _selectSearchResult(result),
                              );
                            },
                          )
                        : SingleChildScrollView(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Saved locations
                                  const Text(
                                    'Saved Places',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  _buildSavedLocationsList(),

                                  const SizedBox(height: 16),

                                  // Recent locations
                                  const Text(
                                    'Recent Locations',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  _buildRecentLocationsList(),
                                ],
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),

          // Continue button
          Positioned(
            bottom: 24,
            left: 16,
            right: 16,
            child: SafeArea(
              child: AnimatedScaleButton(
                onTap: _onContinue,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Continue',
                        style: GoogleFonts.nunitoSans(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
