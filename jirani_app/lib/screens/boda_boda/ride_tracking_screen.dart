import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Size;
import 'package:url_launcher/url_launcher.dart';
import '../../app/theme.dart';
import '../../models/ride.dart';
import '../../models/rider.dart';
import '../../providers/ride_provider.dart';
import '../../services/mapbox_service_new.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';

/// Screen for tracking an ongoing ride
class RideTrackingScreen extends ConsumerStatefulWidget {
  /// The rider for this ride
  final Rider rider;

  /// Creates a new instance of [RideTrackingScreen]
  const RideTrackingScreen({
    super.key,
    required this.rider,
  });

  @override
  ConsumerState<RideTrackingScreen> createState() => _RideTrackingScreenState();
}

class _RideTrackingScreenState extends ConsumerState<RideTrackingScreen> {
  MapboxMap? _mapController;
  bool _isMapInitialized = false;
  Timer? _rideUpdateTimer;

  @override
  void initState() {
    super.initState();

    // Start a timer to simulate ride updates
    _rideUpdateTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _simulateRideUpdate(),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _rideUpdateTimer?.cancel();
    super.dispose();
  }

  void _onMapCreated(MapboxMap controller) {
    _mapController = controller;
    setState(() {
      _isMapInitialized = true;
    });

    // Map style will be set by default

    // Update map with ride information
    _updateMapWithRideInfo();
  }

  void _updateMapWithRideInfo() {
    if (!_isMapInitialized || _mapController == null) return;

    final currentRideAsync = ref.read(currentRideProvider);

    if (currentRideAsync is! AsyncData || currentRideAsync.value == null) {
      return;
    }

    final ride = currentRideAsync.value!;

    // With the new Mapbox SDK, we would use PointAnnotationManager
    // This is a simplified implementation
    if (kDebugMode) {
      print('Updating map with ride info');
      print('Pickup: ${ride.pickup.latitude}, ${ride.pickup.longitude}');
      print('Dropoff: ${ride.dropoff.latitude}, ${ride.dropoff.longitude}');
    }

    // Add route between pickup and dropoff
    _updateRouteOnMap();

    // Calculate bounds to fit both points
    final minLat = math.min(ride.pickup.latitude, ride.dropoff.latitude);
    final maxLat = math.max(ride.pickup.latitude, ride.dropoff.latitude);
    final minLng = math.min(ride.pickup.longitude, ride.dropoff.longitude);
    final maxLng = math.max(ride.pickup.longitude, ride.dropoff.longitude);

    // Calculate a center point and zoom level
    final centerLat = (minLat + maxLat) / 2;
    final centerLng = (minLng + maxLng) / 2;

    // Calculate a zoom level based on the distance
    final latDiff = maxLat - minLat;
    final lngDiff = maxLng - minLng;
    final maxDiff = math.max(latDiff, lngDiff);
    final zoom =
        14 - math.log(maxDiff * 111) / math.ln2; // 111 km per degree at equator

    // Fly to the center point
    _mapController!.flyTo(
      CameraOptions(
        center: MapboxServiceNew.latLngToPoint(centerLat, centerLng),
        zoom: zoom.clamp(10.0, 16.0),
      ),
      MapAnimationOptions(duration: 1000),
    );
  }

  void _updateRouteOnMap() async {
    if (!_isMapInitialized || _mapController == null) return;

    final currentRideAsync = ref.read(currentRideProvider);

    if (currentRideAsync is! AsyncData || currentRideAsync.value == null) {
      return;
    }

    final ride = currentRideAsync.value!;

    // Get points for pickup and dropoff
    final pickupPoint = MapboxServiceNew.latLngToPoint(
        ride.pickup.latitude, ride.pickup.longitude);
    final dropoffPoint = MapboxServiceNew.latLngToPoint(
        ride.dropoff.latitude, ride.dropoff.longitude);

    // Draw the route between pickup and dropoff
    await MapboxServiceNew.drawRouteBetweenPoints(
      _mapController!,
      pickupPoint,
      dropoffPoint,
      routeColor: AppTheme.primaryColor,
      routeWidth: 4.0,
      routeOpacity: 0.8,
    );

    if (kDebugMode) {
      print('Route drawn between pickup and dropoff');
    }
  }

  void _simulateRideUpdate() {
    final currentRideAsync = ref.read(currentRideProvider);

    if (currentRideAsync is! AsyncData || currentRideAsync.value == null) {
      return;
    }

    final ride = currentRideAsync.value!;

    // Simulate ride status progression
    switch (ride.status) {
      case RideStatus.requested:
        // Simulate rider accepting the ride
        ref
            .read(currentRideProvider.notifier)
            .updateRideStatus(RideStatus.accepted);
        break;
      case RideStatus.accepted:
        // Simulate rider en route to pickup
        ref
            .read(currentRideProvider.notifier)
            .updateRideStatus(RideStatus.enRouteToPickup);
        break;
      case RideStatus.enRouteToPickup:
        // Simulate rider arriving at pickup
        ref
            .read(currentRideProvider.notifier)
            .updateRideStatus(RideStatus.arrivedAtPickup);
        break;
      case RideStatus.arrivedAtPickup:
        // Simulate ride in progress
        ref
            .read(currentRideProvider.notifier)
            .updateRideStatus(RideStatus.inProgress);
        break;
      case RideStatus.inProgress:
        // Simulate ride completion
        ref
            .read(currentRideProvider.notifier)
            .updateRideStatus(RideStatus.completed);

        // Cancel the timer as the ride is complete
        _rideUpdateTimer?.cancel();

        // Show ride completion dialog
        _showRideCompletionDialog();
        break;
      default:
        // Do nothing for other statuses
        break;
    }
  }

  void _showRideCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Ride Completed'),
        content: const Text(
            'Your ride has been completed. How would you rate your experience?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showRatingDialog();
            },
            child: const Text('Rate Now'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('Later'),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    int rating = 5;
    String? review;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (builderContext, setState) => AlertDialog(
          title: const Text('Rate Your Ride'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('How was your experience?'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  5,
                  (index) => IconButton(
                    icon: Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: index < rating ? Colors.amber : Colors.grey,
                      size: 32,
                    ),
                    onPressed: () {
                      setState(() {
                        rating = index + 1;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const TextField(
                decoration: InputDecoration(
                  hintText: 'Add a comment (optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Submit rating
                _submitRatingAndNavigateBack(rating, review);
              },
              child: const Text('Submit'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitRatingAndNavigateBack(int rating, String? review) async {
    try {
      // Submit rating
      await ref.read(currentRideProvider.notifier).rateRide(
            rating: rating,
            review: review,
          );

      // Return to previous screen if still mounted
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting rating: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onCancelRideTap() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Cancel Ride'),
        content: const Text('Are you sure you want to cancel this ride?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              // Close the dialog
              Navigator.of(dialogContext).pop();

              // Cancel the ride and navigate back
              _cancelRideAndNavigateBack();
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelRideAndNavigateBack() async {
    try {
      // Cancel the ride
      await ref.read(currentRideProvider.notifier).cancelRide();

      // Return to previous screen if still mounted
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling ride: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getRideStatusText(RideStatus status) {
    switch (status) {
      case RideStatus.requested:
        return 'Requesting a ride...';
      case RideStatus.accepted:
        return 'Ride accepted! Rider is preparing.';
      case RideStatus.enRouteToPickup:
        return 'Rider is on the way to pick you up.';
      case RideStatus.arrivedAtPickup:
        return 'Rider has arrived at pickup location.';
      case RideStatus.inProgress:
        return 'Ride in progress.';
      case RideStatus.completed:
        return 'Ride completed.';
      case RideStatus.cancelled:
        return 'Ride cancelled.';
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentRideAsync = ref.watch(currentRideProvider);
    final primaryColor = AppTheme.primaryColor;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          margin: const EdgeInsets.only(top: 16, left: 16, right: 16),
          child: GlassmorphicContainer(
            width: double.infinity,
            height: kToolbarHeight,
            borderRadius: BorderRadius.circular(16),
            blur: 10,
            opacity: 0.2,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              title: Text(
                'Track Ride',
                style: GoogleFonts.nunitoSans(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              leading: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
          ),
        ),
      ),
      body: currentRideAsync.when(
        data: (ride) {
          if (ride == null) {
            return const Center(
              child: Text('No active ride'),
            );
          }

          return Stack(
            children: [
              // Map
              Positioned.fill(
                child: MapboxServiceNew.createMapView(
                  initialCameraPosition: MapboxServiceNew.latLngToPoint(
                      ride.pickup.latitude, ride.pickup.longitude),
                  initialZoom: 15.0,
                  myLocationEnabled: true,
                  compassEnabled: true,
                  logoEnabled: false,
                  attributionEnabled: false,
                  styleUri: MapboxServiceNew.customMapStyle,
                  onMapCreated: _onMapCreated,
                ),
              ),

              // Ride status bar
              Positioned(
                top: 80,
                left: 16,
                right: 16,
                child: GlassmorphicContainer(
                  width: double.infinity,
                  height: 60,
                  borderRadius: BorderRadius.circular(16),
                  blur: 10,
                  opacity: 0.2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            ride.status == RideStatus.requested
                                ? Icons.search
                                : (ride.status == RideStatus.accepted ||
                                        ride.status ==
                                            RideStatus.enRouteToPickup
                                    ? Icons.motorcycle
                                    : (ride.status == RideStatus.arrivedAtPickup
                                        ? Icons.location_on
                                        : Icons.directions_bike)),
                            color: Colors.white,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _getRideStatusText(ride.status),
                            style: GoogleFonts.nunitoSans(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Ride details panel
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Handle
                      Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),

                      // Ride info
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: Icon(
                              Icons.motorcycle,
                              color: primaryColor,
                              size: 30,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Boda Boda Ride',
                                  style: GoogleFonts.nunitoSans(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Estimated fare: KSh ${ride.estimatedFare.toStringAsFixed(0)}',
                                  style: GoogleFonts.nunitoSans(
                                    fontSize: 14,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (ride.status != RideStatus.completed &&
                              ride.status != RideStatus.cancelled)
                            AnimatedScaleButton(
                              onTap: _onCancelRideTap,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.red,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: GoogleFonts.nunitoSans(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      // Locations
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            children: [
                              Icon(Icons.circle, color: primaryColor, size: 12),
                              Container(
                                width: 1,
                                height: 30,
                                color: Colors.grey.withOpacity(0.5),
                              ),
                              const Icon(Icons.place,
                                  color: Colors.red, size: 12),
                            ],
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  ride.pickup.address,
                                  style: GoogleFonts.nunitoSans(
                                    fontSize: 14,
                                    color: Colors.grey[800],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  ride.dropoff.address,
                                  style: GoogleFonts.nunitoSans(
                                    fontSize: 14,
                                    color: Colors.grey[800],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Call button
                      if (ride.riderId != null &&
                          ride.status != RideStatus.completed &&
                          ride.status != RideStatus.cancelled)
                        SizedBox(
                          width: double.infinity,
                          child: AnimatedScaleButton(
                            onTap: () async {
                              // In a real app, this would use the actual rider's phone number
                              final phoneUri = Uri(
                                scheme: 'tel',
                                path: '+254712345678',
                              );

                              if (await canLaunchUrl(phoneUri)) {
                                await launchUrl(phoneUri);
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              decoration: BoxDecoration(
                                color: primaryColor,
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: primaryColor.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.phone,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Call Rider',
                                    style: GoogleFonts.nunitoSans(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => Center(
          child: CircularProgressIndicator(color: primaryColor),
        ),
        error: (error, _) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
