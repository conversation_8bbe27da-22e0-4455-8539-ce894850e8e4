import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:location/location.dart' as loc;
import '../../models/lat_lng.dart' as custom;
import '../../models/rider.dart';
import '../../providers/location_provider.dart' as legacy_location;
import '../../providers/rider_provider.dart';
import '../../services/mapbox_service_new.dart';
import '../../services/location_service.dart';
import '../../services/location_integration_adapter.dart';
import '../../providers/enhanced_location_provider.dart' as enhanced_location;
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'rider_card.dart';
import 'rider_details_screen.dart';
// Removed unused import
import 'enhanced_location_selection_screen.dart';

/// Main screen for the Boda Boda feature
class BodaBodaScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [BodaBodaScreen]
  const BodaBodaScreen({super.key});

  @override
  ConsumerState<BodaBodaScreen> createState() => _BodaBodaScreenState();
}

class _BodaBodaScreenState extends ConsumerState<BodaBodaScreen> {
  MapboxMap? _mapController;
  bool _isMapInitialized = false;
  bool _isRiderListExpanded = false;
  LocationService? _locationService;
  StreamSubscription<loc.LocationData>? _locationSubscription;

  @override
  void initState() {
    super.initState();
    _initLocationService();
  }

  Future<void> _initLocationService() async {
    try {
      _locationService = ref.read(legacy_location.locationServiceProvider);
      await _locationService!.initialize();

      // Initialize enhanced location service integration
      await LocationIntegrationAdapter.initialize(ref);

      // Start tracking location updates
      final position = await _locationService!.getCurrentPosition();
      if (kDebugMode) {
        print('Initial position: ${position.latitude}, ${position.longitude}');
      }

      // Update map with initial location
      if (_isMapInitialized &&
          _mapController != null &&
          position.latitude != null &&
          position.longitude != null) {
        _moveToLocation(custom.LatLng(position.latitude!, position.longitude!));
      }

      // Update map markers
      _updateMapMarkers();
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing location service: $e');
      }
    }
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _locationService?.stopLocationTracking();
    super.dispose();
  }

  void _onMapCreated(MapboxMap controller) {
    _mapController = controller;
    setState(() {
      _isMapInitialized = true;
    });

    // Map style will be set by default

    // Move to user's location when available
    final userLocation = ref.read(enhanced_location.userLocationProvider);
    if (userLocation is AsyncData && userLocation.value != null) {
      final customLatLng = custom.LatLng(
          userLocation.value!.latitude!, userLocation.value!.longitude!);
      _moveToLocation(customLatLng);
    }
  }

  void _moveToLocation(custom.LatLng location) {
    if (_mapController != null) {
      _mapController!.flyTo(
        CameraOptions(
          center: MapboxServiceNew.latLngToPoint(
              location.latitude, location.longitude),
          zoom: 15.0,
        ),
        MapAnimationOptions(duration: 2000),
      );
    }
  }

  void _onRiderTap(Rider rider) {
    ref.read(selectedRiderProvider.notifier).selectRider(rider);

    // Show rider details bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const RiderDetailsScreen(),
    );
  }

  void _onBookRideTap() {
    // Check if enhanced location service is available
    if (LocationIntegrationAdapter.isEnhancedLocationAvailable(ref)) {
      // Navigate to enhanced location selection screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const EnhancedLocationSelectionScreen(),
        ),
      );
    } else {
      // Fallback to enhanced location selection screen (default)
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const EnhancedLocationSelectionScreen(),
        ),
      );
    }
  }

  void _updateMapMarkers() async {
    if (!_isMapInitialized || _mapController == null) return;

    // With the new Mapbox SDK, we would use PointAnnotationManager
    if (kDebugMode) {
      print('Updating map markers');
    }

    // Add user location marker
    final userLocation = ref.read(enhanced_location.userLocationProvider);
    if (userLocation is AsyncData && userLocation.value != null) {
      if (kDebugMode) {
        print(
            'User location: ${userLocation.value!.latitude}, ${userLocation.value!.longitude}');
      }

      // Create a point annotation manager
      final pointAnnotationManager =
          await _mapController!.annotations.createPointAnnotationManager();

      // Add rider markers
      final nearbyRiders = ref.read(nearbyRidersProvider);
      nearbyRiders.whenData((riders) {
        if (kDebugMode) {
          print('Found ${riders.length} nearby riders');
        }

        // Create a list of point annotation options
        final options = <PointAnnotationOptions>[];

        for (final rider in riders) {
          if (rider.status == RiderStatus.offline) continue;

          if (kDebugMode) {
            print(
                'Rider: ${rider.name} at ${rider.location.latitude}, ${rider.location.longitude}');
          }

          // Add rider marker
          options.add(
            PointAnnotationOptions(
              geometry: MapboxServiceNew.latLngToPoint(
                rider.location.latitude,
                rider.location.longitude,
              ),
              textField: rider.name,
              textSize: 12.0,
              textOffset: [0.0, 2.0],
              textColor: Colors.black.value,
              textHaloColor: Colors.white.value,
              textHaloWidth: 1.0,
            ),
          );
        }

        // Add all markers at once
        if (options.isNotEmpty) {
          pointAnnotationManager.createMulti(options);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userLocationAsync = ref.watch(enhanced_location.userLocationProvider);
    final nearbyRidersAsync = ref.watch(nearbyRidersProvider);

    // Update map markers when user location or nearby riders change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateMapMarkers();
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Boda Boda'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: () {
              final userLocation =
                  ref.read(enhanced_location.userLocationProvider);
              if (userLocation is AsyncData && userLocation.value != null) {
                final customLatLng = custom.LatLng(
                    userLocation.value!.latitude!,
                    userLocation.value!.longitude!);
                _moveToLocation(customLatLng);
              }
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // Map
          userLocationAsync.when(
            data: (location) {
              // Enhanced location service always provides non-null location data
              return MapboxServiceNew.createMapView(
                initialCameraPosition: MapboxServiceNew.latLngToPoint(
                    location.latitude!, location.longitude!),
                initialZoom: 15.0,
                myLocationEnabled: true,
                onMapCreated: _onMapCreated,
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, _) => Center(
              child: Text('Error: $error'),
            ),
          ),

          // Rider list toggle button
          Positioned(
            top: 16,
            right: 16,
            child: AnimatedScaleButton(
              onTap: () {
                setState(() {
                  _isRiderListExpanded = !_isRiderListExpanded;
                });
              },
              child: GlassmorphicContainer(
                width: 48,
                height: 48,
                borderRadius: BorderRadius.circular(24),
                blur: 10,
                opacity: 0.2,
                child: Icon(
                  _isRiderListExpanded ? Icons.close : Icons.list,
                  color: Colors.white,
                ),
              ),
            ),
          ),

          // Rider list
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            top: 16,
            right: _isRiderListExpanded ? 16 : -300,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.8,
              child: GlassmorphicContainer(
                width: MediaQuery.of(context).size.width * 0.8, // Added width
                height: 400,
                borderRadius: BorderRadius.circular(16),
                blur: 10,
                opacity: 0.2,
                child: nearbyRidersAsync.when(
                  data: (riders) {
                    final availableRiders = riders
                        .where((r) => r.status == RiderStatus.available)
                        .toList();

                    if (availableRiders.isEmpty) {
                      return const Center(
                        child: Text(
                          'No riders available nearby',
                          style: TextStyle(color: Colors.white),
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: availableRiders.length,
                      itemBuilder: (context, index) {
                        final rider = availableRiders[index];
                        return RiderCard(
                          rider: rider,
                          onTap: () => _onRiderTap(rider),
                        );
                      },
                    );
                  },
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, _) => Center(
                    child: Text(
                      'Error: $error',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Book ride button
          Positioned(
            bottom: 48, // Added extra padding
            left: 0,
            right: 0,
            child: Center(
              child: AnimatedScaleButton(
                onTap: _onBookRideTap,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.motorcycle,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Book a Ride',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
