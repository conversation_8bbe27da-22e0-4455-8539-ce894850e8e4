import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Size;
import '../../app/theme.dart';
import '../../models/lat_lng.dart' as custom;
import '../../providers/location_provider.dart';
import '../../providers/ride_provider.dart';
import '../../services/mapbox_service_new.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'rider_selection_screen.dart';
import 'widgets/fare_breakdown_card.dart';

/// Screen for displaying fare estimation
class FareEstimationScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [FareEstimationScreen]
  const FareEstimationScreen({super.key});

  @override
  ConsumerState<FareEstimationScreen> createState() =>
      _FareEstimationScreenState();
}

class _FareEstimationScreenState extends ConsumerState<FareEstimationScreen> {
  MapboxMap? _mapController;
  bool _isMapInitialized = false;
  List<custom.LatLng>? _routeCoordinates;
  PointAnnotationManager? _pointAnnotationManager;

  @override
  void initState() {
    super.initState();

    // Fetch route when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchRoute();
    });
  }

  void _onMapCreated(MapboxMap controller) {
    _mapController = controller;
    setState(() {
      _isMapInitialized = true;
    });

    _initializeAnnotationManagers();
  }

  Future<void> _initializeAnnotationManagers() async {
    if (_mapController != null) {
      _pointAnnotationManager =
          await _mapController!.annotations.createPointAnnotationManager();

      // Draw route if available
      if (_routeCoordinates != null) {
        _drawRoute();
      }
    }
  }

  Future<void> _fetchRoute() async {
    final pickupLocation = ref.read(pickupLocationProvider);
    final dropoffLocation = ref.read(dropoffLocationProvider);

    if (pickupLocation == null || dropoffLocation == null) return;

    try {
      final point1 = MapboxServiceNew.latLngToPoint(
          pickupLocation.latitude, pickupLocation.longitude);
      final point2 = MapboxServiceNew.latLngToPoint(
          dropoffLocation.latitude, dropoffLocation.longitude);

      final directions = await MapboxServiceNew.getDirections(point1, point2);

      if (directions['routes'] == null ||
          (directions['routes'] as List).isEmpty) {
        return;
      }

      // Extract the route coordinates
      final route = directions['routes'][0];
      final geometry = route['geometry'] as Map<String, dynamic>;
      final coordinates = geometry['coordinates'] as List;

      // Convert to LatLng list
      setState(() {
        _routeCoordinates = coordinates.map((coord) {
          final lng = (coord[0] as num).toDouble();
          final lat = (coord[1] as num).toDouble();
          return custom.LatLng(lat, lng);
        }).toList();
      });

      // Draw route on map
      if (_isMapInitialized && _pointAnnotationManager != null) {
        _drawRoute();
      }

      // Fit map to show the entire route
      _fitMapToRoute();
    } catch (e) {
      debugPrint('Error fetching route: $e');
    }
  }

  void _drawRoute() async {
    if (_mapController == null || _pointAnnotationManager == null) {
      return;
    }

    // Clear existing annotations
    _pointAnnotationManager!.deleteAll();

    // Get pickup and dropoff locations
    final pickupLocation = ref.read(pickupLocationProvider);
    final dropoffLocation = ref.read(dropoffLocationProvider);

    if (pickupLocation == null || dropoffLocation == null) {
      return;
    }

    // Add markers for pickup and dropoff
    _pointAnnotationManager!.create(
      PointAnnotationOptions(
        geometry: MapboxServiceNew.latLngToPoint(
          pickupLocation.latitude,
          pickupLocation.longitude,
        ),
        iconImage: 'assets/icons/pickup_marker.png',
        iconSize: 1.5,
      ),
    );

    _pointAnnotationManager!.create(
      PointAnnotationOptions(
        geometry: MapboxServiceNew.latLngToPoint(
          dropoffLocation.latitude,
          dropoffLocation.longitude,
        ),
        iconImage: 'assets/icons/dropoff_marker.png',
        iconSize: 1.5,
      ),
    );

    // Draw route between pickup and dropoff using the new method
    final pickupPoint = MapboxServiceNew.latLngToPoint(
        pickupLocation.latitude, pickupLocation.longitude);
    final dropoffPoint = MapboxServiceNew.latLngToPoint(
        dropoffLocation.latitude, dropoffLocation.longitude);

    await MapboxServiceNew.drawRouteBetweenPoints(
      _mapController!,
      pickupPoint,
      dropoffPoint,
      routeColor: AppTheme.primaryColor,
      routeWidth: 4.0,
      routeOpacity: 0.8,
    );
  }

  void _fitMapToRoute() {
    if (_routeCoordinates == null ||
        _routeCoordinates!.isEmpty ||
        _mapController == null) {
      return;
    }

    // Find the bounds of the route
    double minLat = _routeCoordinates![0].latitude;
    double maxLat = _routeCoordinates![0].latitude;
    double minLng = _routeCoordinates![0].longitude;
    double maxLng = _routeCoordinates![0].longitude;

    for (final coord in _routeCoordinates!) {
      if (coord.latitude < minLat) minLat = coord.latitude;
      if (coord.latitude > maxLat) maxLat = coord.latitude;
      if (coord.longitude < minLng) minLng = coord.longitude;
      if (coord.longitude > maxLng) maxLng = coord.longitude;
    }

    // Add some padding
    minLat -= 0.01;
    maxLat += 0.01;
    minLng -= 0.01;
    maxLng += 0.01;

    // Create a camera options to fit the bounds
    final centerLat = (minLat + maxLat) / 2;
    final centerLng = (minLng + maxLng) / 2;
    final zoom = 14.0;

    // Animate to the new camera position
    _mapController!.flyTo(
      CameraOptions(
        center: MapboxServiceNew.latLngToPoint(centerLat, centerLng),
        zoom: zoom,
      ),
      MapAnimationOptions(duration: 1000),
    );
  }

  void _onContinue() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RiderSelectionScreen(),
      ),
    );
  }

  double _calculateRouteDistance() {
    if (_routeCoordinates == null || _routeCoordinates!.isEmpty) {
      return 0.0;
    }

    // In a real app, this would come from the directions API
    // For now, we'll use a simple estimation based on the number of points
    return _routeCoordinates!.length * 0.1; // Rough estimate
  }

  int _calculateRouteDuration() {
    if (_routeCoordinates == null || _routeCoordinates!.isEmpty) {
      return 0;
    }

    // In a real app, this would come from the directions API
    // For now, we'll use a simple calculation based on distance
    final distance = _calculateRouteDistance();

    // Assume average speed of 30 km/h
    final durationInHours = distance / 30;
    final durationInMinutes = (durationInHours * 60).round();

    return durationInMinutes;
  }

  @override
  Widget build(BuildContext context) {
    final pickupLocation = ref.watch(pickupLocationProvider);
    // We need to watch dropoffLocation to rebuild when it changes
    ref.watch(dropoffLocationProvider);
    final estimatedFareAsync = ref.watch(estimatedFareProvider);
    final primaryColor = AppTheme.primaryColor;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 80,
        automaticallyImplyLeading: false,
        flexibleSpace: Padding(
          padding: const EdgeInsets.only(top: 40, left: 16, right: 16),
          child: GlassmorphicContainer(
            width: double.infinity,
            height: 50,
            borderRadius: BorderRadius.circular(16),
            blur: 10,
            opacity: 0.2,
            child: Row(
              children: [
                Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: 20,
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      'Fare Estimate',
                      style: GoogleFonts.nunitoSans(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 40), // Balance the layout
              ],
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          // Map
          Positioned.fill(
            child: pickupLocation != null
                ? MapboxServiceNew.createMapView(
                    initialCameraPosition: MapboxServiceNew.latLngToPoint(
                        pickupLocation.latitude, pickupLocation.longitude),
                    initialZoom: 15.0,
                    myLocationEnabled: true,
                    compassEnabled: true,
                    logoEnabled: false,
                    attributionEnabled: false,
                    styleUri: MapboxServiceNew.customMapStyle,
                    onMapCreated: _onMapCreated,
                  )
                : const Center(
                    child: Text('Pickup location not available'),
                  ),
          ),

          // Route info card
          Positioned(
            top: 80,
            left: 16,
            right: 16,
            child: GlassmorphicContainer(
              width: double.infinity,
              height: 80,
              borderRadius: BorderRadius.circular(16),
              blur: 10,
              opacity: 0.2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Column(
                      children: [
                        Icon(Icons.circle, color: primaryColor, size: 12),
                        Container(
                          width: 1,
                          height: 20,
                          color: Colors.grey[400],
                        ),
                        const Icon(Icons.place, color: Colors.red, size: 12),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Pickup: Current Location',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Dropoff: Destination',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Fare breakdown panel
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Handle
                      Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      Text(
                        'Fare Estimate',
                        style: GoogleFonts.nunitoSans(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Fare breakdown
                      estimatedFareAsync.when(
                        data: (fare) {
                          if (fare == null) {
                            return const Center(
                              child: Text('Unable to calculate fare'),
                            );
                          }

                          return FareBreakdownCard(
                            estimatedFare: fare,
                            estimatedDistance: _calculateRouteDistance(),
                            estimatedDuration: _calculateRouteDuration(),
                          );
                        },
                        loading: () => Center(
                          child: CircularProgressIndicator(color: primaryColor),
                        ),
                        error: (error, _) => Center(
                          child: Text('Error: $error'),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Continue button
                      SizedBox(
                        width: double.infinity,
                        child: AnimatedScaleButton(
                          onTap: _onContinue,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                            ),
                            decoration: BoxDecoration(
                              color: primaryColor,
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Find Riders',
                                  style: GoogleFonts.nunitoSans(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.arrow_forward,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
