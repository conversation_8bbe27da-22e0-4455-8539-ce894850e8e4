import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Size;
import '../../app/theme.dart';
import '../../models/lat_lng.dart' as custom;
import '../../models/rider.dart';
import '../../providers/location_provider.dart';
import '../../providers/rider_provider.dart';
import '../../services/mapbox_service_new.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'widgets/rider_list_horizontal.dart';
import 'widgets/rider_profile_bottom_sheet.dart';
import 'ride_tracking_screen.dart';

/// Screen for selecting a rider
class RiderSelectionScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [RiderSelectionScreen]
  const RiderSelectionScreen({super.key});

  @override
  ConsumerState<RiderSelectionScreen> createState() =>
      _RiderSelectionScreenState();
}

class _RiderSelectionScreenState extends ConsumerState<RiderSelectionScreen> {
  MapboxMap? _mapController;
  bool _isMapInitialized = false;
  PointAnnotationManager? _annotationManager;
  List<custom.LatLng>? _routeCoordinates;
  Rider? _selectedRider;

  @override
  void initState() {
    super.initState();

    // Fetch route when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchRoute();
    });
  }

  void _onMapCreated(MapboxMap controller) {
    _mapController = controller;
    setState(() {
      _isMapInitialized = true;
    });

    _initializeAnnotationManagers();
  }

  Future<void> _initializeAnnotationManagers() async {
    if (_mapController != null) {
      _annotationManager =
          await _mapController!.annotations.createPointAnnotationManager();

      // Update markers
      _updateMapMarkers();

      // Draw route if available
      if (_routeCoordinates != null) {
        _drawRoute();
      }
    }
  }

  Future<void> _fetchRoute() async {
    final pickupLocation = ref.read(pickupLocationProvider);
    final dropoffLocation = ref.read(dropoffLocationProvider);

    if (pickupLocation == null || dropoffLocation == null) return;

    try {
      final point1 = MapboxServiceNew.latLngToPoint(
          pickupLocation.latitude, pickupLocation.longitude);
      final point2 = MapboxServiceNew.latLngToPoint(
          dropoffLocation.latitude, dropoffLocation.longitude);

      final directions = await MapboxServiceNew.getDirections(point1, point2);

      if (directions['routes'] == null ||
          (directions['routes'] as List).isEmpty) {
        return;
      }

      // Extract the route coordinates
      final route = directions['routes'][0];
      final geometry = route['geometry'] as Map<String, dynamic>;
      final coordinates = geometry['coordinates'] as List;

      // Convert to LatLng list
      setState(() {
        _routeCoordinates = coordinates.map((coord) {
          final lng = (coord[0] as num).toDouble();
          final lat = (coord[1] as num).toDouble();
          return custom.LatLng(lat, lng);
        }).toList();
      });

      // Draw route on map
      if (_isMapInitialized && _annotationManager != null) {
        _drawRoute();
      }

      // Fit map to show the entire route
      _fitMapToRoute();
    } catch (e) {
      debugPrint('Error fetching route: $e');
    }
  }

  void _drawRoute() {
    if (_routeCoordinates == null ||
        _routeCoordinates!.isEmpty ||
        _annotationManager == null) {
      return;
    }

    // Clear existing annotations
    _annotationManager!.deleteAll();

    // Add pickup and dropoff markers
    final pickupLocation = ref.read(pickupLocationProvider);
    final dropoffLocation = ref.read(dropoffLocationProvider);

    if (pickupLocation != null) {
      _annotationManager!.create(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            pickupLocation.latitude,
            pickupLocation.longitude,
          ),
          iconImage: 'assets/icons/pickup_marker.png',
          iconSize: 1.5,
        ),
      );
    }

    if (dropoffLocation != null) {
      _annotationManager!.create(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            dropoffLocation.latitude,
            dropoffLocation.longitude,
          ),
          iconImage: 'assets/icons/dropoff_marker.png',
          iconSize: 1.5,
        ),
      );
    }
  }

  void _fitMapToRoute() {
    if (_routeCoordinates == null ||
        _routeCoordinates!.isEmpty ||
        _mapController == null) {
      return;
    }

    // Find the bounds of the route
    double minLat = _routeCoordinates![0].latitude;
    double maxLat = _routeCoordinates![0].latitude;
    double minLng = _routeCoordinates![0].longitude;
    double maxLng = _routeCoordinates![0].longitude;

    for (final coord in _routeCoordinates!) {
      if (coord.latitude < minLat) minLat = coord.latitude;
      if (coord.latitude > maxLat) maxLat = coord.latitude;
      if (coord.longitude < minLng) minLng = coord.longitude;
      if (coord.longitude > maxLng) maxLng = coord.longitude;
    }

    // Add some padding
    minLat -= 0.01;
    maxLat += 0.01;
    minLng -= 0.01;
    maxLng += 0.01;

    // Create a camera options to fit the bounds
    final centerLat = (minLat + maxLat) / 2;
    final centerLng = (minLng + maxLng) / 2;
    final zoom = 14.0;

    final cameraOptions = CameraOptions(
      center: MapboxServiceNew.latLngToPoint(centerLat, centerLng),
      zoom: zoom,
    );

    // Animate to the new camera position
    _mapController!.flyTo(
      cameraOptions,
      MapAnimationOptions(duration: 1000),
    );
  }

  void _updateMapMarkers() {
    if (!_isMapInitialized ||
        _mapController == null ||
        _annotationManager == null) return;

    // Clear existing markers
    _annotationManager!.deleteAll();

    final options = <PointAnnotationOptions>[];

    // Add pickup marker
    final pickupLocation = ref.read(pickupLocationProvider);
    if (pickupLocation != null) {
      options.add(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            pickupLocation.latitude,
            pickupLocation.longitude,
          ),
          iconImage: 'assets/icons/pickup_marker.png',
          iconSize: 1.5,
        ),
      );
    }

    // Add dropoff marker
    final dropoffLocation = ref.read(dropoffLocationProvider);
    if (dropoffLocation != null) {
      options.add(
        PointAnnotationOptions(
          geometry: MapboxServiceNew.latLngToPoint(
            dropoffLocation.latitude,
            dropoffLocation.longitude,
          ),
          iconImage: 'assets/icons/dropoff_marker.png',
          iconSize: 1.5,
        ),
      );
    }

    // Add rider markers
    final nearbyRiders = ref.read(nearbyRidersProvider);
    nearbyRiders.whenData((riders) {
      for (final rider in riders) {
        if (rider.status == RiderStatus.offline) continue;

        options.add(
          PointAnnotationOptions(
            geometry: MapboxServiceNew.latLngToPoint(
              rider.location.latitude,
              rider.location.longitude,
            ),
            iconImage: 'assets/icons/rider_marker.png',
            iconSize: 1.2,
          ),
        );
      }

      // Add all markers at once
      if (options.isNotEmpty) {
        _annotationManager!.createMulti(options);
      }
    });
  }

  void _onRiderSelected(Rider rider) {
    setState(() {
      _selectedRider = rider;
    });

    // Show rider profile bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RiderProfileBottomSheet(
        rider: rider,
        onBookNow: () {
          Navigator.of(context).pop();
          _onBookRide(rider);
        },
      ),
    );
  }

  void _onBookRide(Rider rider) {
    // In a real app, this would create a ride request
    // For now, we'll just navigate to the ride tracking screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RideTrackingScreen(rider: rider),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userLocationAsync = ref.watch(userLocationProvider);
    final nearbyRidersAsync = ref.watch(nearbyRidersProvider);
    final primaryColor = AppTheme.primaryColor;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 80,
        automaticallyImplyLeading: false,
        flexibleSpace: Padding(
          padding: const EdgeInsets.only(top: 40, left: 16, right: 16),
          child: GlassmorphicContainer(
            width: double.infinity,
            height: 50,
            borderRadius: BorderRadius.circular(16),
            blur: 10,
            opacity: 0.2,
            child: Row(
              children: [
                Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: 20,
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      'Select Rider',
                      style: GoogleFonts.nunitoSans(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 40), // Balance the layout
              ],
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          // Map
          Positioned.fill(
            child: userLocationAsync.when(
              data: (location) {
                if (location == null) {
                  return const Center(
                    child: Text('Location not available'),
                  );
                }

                return MapboxServiceNew.createMapView(
                  initialCameraPosition: MapboxServiceNew.latLngToPoint(
                      location.latitude, location.longitude),
                  initialZoom: 15.0,
                  myLocationEnabled: true,
                  compassEnabled: true,
                  logoEnabled: false,
                  attributionEnabled: false,
                  styleUri: MapboxServiceNew.customMapStyle,
                  onMapCreated: _onMapCreated,
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, _) => Center(
                child: Text('Error: $error'),
              ),
            ),
          ),

          // Route info card
          Positioned(
            top: 80,
            left: 16,
            right: 16,
            child: GlassmorphicContainer(
              width: double.infinity,
              height: 80,
              borderRadius: BorderRadius.circular(16),
              blur: 10,
              opacity: 0.2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Column(
                      children: [
                        Icon(Icons.circle, color: primaryColor, size: 12),
                        Container(
                          width: 1,
                          height: 20,
                          color: Colors.grey[400],
                        ),
                        const Icon(Icons.place, color: Colors.red, size: 12),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Pickup: Current Location',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Dropoff: Destination',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Rider list
          Positioned(
            bottom: 24,
            left: 0,
            right: 0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: nearbyRidersAsync.when(
                data: (riders) {
                  final availableRiders = riders
                      .where((r) => r.status == RiderStatus.available)
                      .toList();

                  if (availableRiders.isEmpty) {
                    return Center(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          'No riders available nearby',
                          style: GoogleFonts.nunitoSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  }

                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 16, top: 16, bottom: 8),
                          child: Text(
                            'Available Riders (${availableRiders.length})',
                            style: GoogleFonts.nunitoSans(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        RiderListHorizontal(
                          riders: availableRiders,
                          selectedRider: _selectedRider,
                          onRiderSelected: _onRiderSelected,
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  );
                },
                loading: () => Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(color: primaryColor),
                  ),
                ),
                error: (error, _) => Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text('Error: $error'),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
