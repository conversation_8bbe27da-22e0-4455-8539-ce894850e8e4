import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../app/theme.dart';
import '../../models/ride.dart';
import '../../models/rider.dart';
import '../../models/lat_lng.dart' as custom;
import '../../models/websocket_message.dart';
import '../../providers/websocket_provider.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'widgets/ride_chat_widget.dart';
import 'widgets/emergency_button.dart';

/// Real-time ride tracking screen with live driver location
class RealTimeTrackingScreen extends ConsumerStatefulWidget {
  final Ride ride;
  final Rider rider;

  const RealTimeTrackingScreen({
    super.key,
    required this.ride,
    required this.rider,
  });

  @override
  ConsumerState<RealTimeTrackingScreen> createState() =>
      _RealTimeTrackingScreenState();
}

class _RealTimeTrackingScreenState
    extends ConsumerState<RealTimeTrackingScreen> {
  MapboxMap? _mapboxMap;
  Timer? _etaUpdateTimer;
  custom.LatLng? _currentDriverLocation;
  String _estimatedArrival = 'Calculating...';
  bool _showChat = false;

  @override
  void initState() {
    super.initState();
    _initializeTracking();
  }

  @override
  void dispose() {
    _etaUpdateTimer?.cancel();
    super.dispose();
  }

  void _initializeTracking() {
    // Connect to WebSocket for real-time updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(webSocketProvider.notifier).connect();
    });

    // Start ETA update timer
    _etaUpdateTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _updateETA();
    });
  }

  void _onMapCreated(MapboxMap mapboxMap) {
    _mapboxMap = mapboxMap;
    _setupMap();
  }

  void _setupMap() async {
    if (_mapboxMap == null) return;

    // Set initial camera to show both pickup and current rider location
    await _setCameraToShowRoute();

    // Add markers
    await _addPickupMarker();
    await _addDestinationMarker();
    await _addDriverMarker();
  }

  Future<void> _setCameraToShowRoute() async {
    // Calculate bounds to show pickup, destination, and driver location
    final bounds = _calculateBounds();

    await _mapboxMap!.setCamera(
      CameraOptions(
        center: Point(
            coordinates:
                Position(bounds.center.longitude, bounds.center.latitude)),
        zoom: 13.0,
      ),
    );
  }

  MapBounds _calculateBounds() {
    final locations = [
      widget.ride.pickup.location,
      widget.ride.dropoff.location,
      if (_currentDriverLocation != null) _currentDriverLocation!,
    ];

    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = minLat < location.latitude ? minLat : location.latitude;
      maxLat = maxLat > location.latitude ? maxLat : location.latitude;
      minLng = minLng < location.longitude ? minLng : location.longitude;
      maxLng = maxLng > location.longitude ? maxLng : location.longitude;
    }

    return MapBounds(
      southwest: custom.LatLng(minLat, minLng),
      northeast: custom.LatLng(maxLat, maxLng),
      center: custom.LatLng((minLat + maxLat) / 2, (minLng + maxLng) / 2),
    );
  }

  Future<void> _addPickupMarker() async {
    // Add pickup location marker
  }

  Future<void> _addDestinationMarker() async {
    // Add destination marker
  }

  Future<void> _addDriverMarker() async {
    // Add driver marker that will be updated in real-time
  }

  Future<void> _updateDriverLocation(custom.LatLng newLocation) async {
    setState(() {
      _currentDriverLocation = newLocation;
    });

    // Update driver marker position with smooth animation
    await _updateDriverMarkerPosition(newLocation);

    // Update ETA with real-time calculation
    _updateETA();

    // Update camera to follow driver if needed
    await _updateCameraToShowDriverAndDestination();
  }

  Future<void> _updateDriverMarkerPosition(custom.LatLng newLocation) async {
    if (_mapboxMap == null) return;

    try {
      // Get the point annotation manager
      final pointAnnotationManager =
          await _mapboxMap!.annotations.createPointAnnotationManager();

      // Clear existing driver markers
      await pointAnnotationManager.deleteAll();

      // Create new driver marker at updated location
      final driverMarker = PointAnnotationOptions(
        geometry: Point(
          coordinates: Position(newLocation.longitude, newLocation.latitude),
        ),
        iconImage: 'driver-marker', // Custom driver icon
        iconSize: 1.2,
        iconAnchor: IconAnchor.BOTTOM,
      );

      await pointAnnotationManager.create(driverMarker);

      // Add smooth animation to the marker update
      await _mapboxMap!.flyTo(
        CameraOptions(
          center: Point(
            coordinates: Position(newLocation.longitude, newLocation.latitude),
          ),
          zoom: 16.0,
        ),
        MapAnimationOptions(
          duration: 1000, // 1 second smooth animation
          startDelay: 0,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating driver marker: $e');
      }
    }
  }

  Future<void> _updateCameraToShowDriverAndDestination() async {
    if (_mapboxMap == null || _currentDriverLocation == null) return;

    try {
      // Use the existing _calculateBounds method which includes current driver location
      final bounds = _calculateBounds();

      // Update camera to show all locations with smooth animation
      await _mapboxMap!.flyTo(
        CameraOptions(
          center: Point(
            coordinates:
                Position(bounds.center.longitude, bounds.center.latitude),
          ),
          zoom: 14.0, // Use a fixed zoom level for consistency
        ),
        MapAnimationOptions(
          duration: 1500,
          startDelay: 0,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating camera bounds: $e');
      }
    }
  }

  void _updateETA() {
    if (_currentDriverLocation == null) return;

    // Calculate ETA based on current driver location and ride status
    final targetLocation = widget.ride.status == RideStatus.enRouteToPickup
        ? widget.ride.pickup.location
        : widget.ride.dropoff.location;

    // This would use a routing service to calculate actual ETA
    // For now, using a simple distance-based calculation
    final distance =
        _calculateDistance(_currentDriverLocation!, targetLocation);
    final estimatedMinutes =
        (distance * 2).round(); // Rough estimate: 2 minutes per km

    setState(() {
      _estimatedArrival = widget.ride.status == RideStatus.enRouteToPickup
          ? 'Arriving in $estimatedMinutes min'
          : 'Destination in $estimatedMinutes min';
    });
  }

  double _calculateDistance(custom.LatLng from, custom.LatLng to) {
    // Simple distance calculation (would use proper geospatial calculation in production)
    final latDiff = from.latitude - to.latitude;
    final lngDiff = from.longitude - to.longitude;
    return (latDiff * latDiff + lngDiff * lngDiff) * 111; // Rough km conversion
  }

  /// Handle real-time ride status updates
  void _handleRideStatusUpdate(WebSocketMessage message) {
    try {
      final statusString = message.data['status'] as String?;
      if (statusString == null) return;

      // Show status-specific UI updates and notifications
      switch (statusString) {
        case 'accepted':
          _showStatusNotification(
            'Ride Accepted!',
            'Your driver is on the way to pick you up.',
            Icons.check_circle,
            Colors.green,
          );
          break;
        case 'en_route_to_pickup':
          _showStatusNotification(
            'Driver En Route',
            'Your driver is heading to your pickup location.',
            Icons.directions_car,
            Colors.blue,
          );
          break;
        case 'arrived_at_pickup':
          _showStatusNotification(
            'Driver Arrived',
            'Your driver has arrived at the pickup location.',
            Icons.location_on,
            Colors.orange,
          );
          // Optionally trigger a phone vibration or sound
          break;
        case 'in_progress':
          _showStatusNotification(
            'Ride Started',
            'Your ride is now in progress. Enjoy your trip!',
            Icons.play_arrow,
            Colors.green,
          );
          break;
        case 'completed':
          _showStatusNotification(
            'Ride Completed',
            'You have arrived at your destination.',
            Icons.flag,
            Colors.purple,
          );
          // Navigate to completion screen after a short delay
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/ride-completion');
            }
          });
          break;
        case 'cancelled':
          final reason = message.data['cancellation_reason'] as String?;
          _showStatusNotification(
            'Ride Cancelled',
            reason ?? 'Your ride has been cancelled.',
            Icons.cancel,
            Colors.red,
          );
          // Navigate back after a short delay
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              Navigator.pop(context);
            }
          });
          break;
      }

      // Update the ETA and UI
      _updateETA();
    } catch (e) {
      if (kDebugMode) {
        print('Error handling ride status update: $e');
      }
    }
  }

  /// Show status notification with custom styling
  void _showStatusNotification(
    String title,
    String message,
    IconData icon,
    Color color,
  ) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    message,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.black87,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _callRider() async {
    final phoneNumber = widget.rider.phoneNumber;
    final uri = Uri.parse('tel:$phoneNumber');

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _toggleChat() {
    setState(() {
      _showChat = !_showChat;
    });
  }

  void _triggerEmergency() {
    // Show emergency dialog and trigger emergency protocol
    showDialog(
      context: context,
      builder: (context) => EmergencyDialog(rideId: widget.ride.id),
    );
  }

  String _getRideStatusText() {
    switch (widget.ride.status) {
      case RideStatus.accepted:
        return 'Rider assigned';
      case RideStatus.enRouteToPickup:
        return 'Rider is on the way';
      case RideStatus.arrivedAtPickup:
        return 'Rider has arrived';
      case RideStatus.inProgress:
        return 'Trip in progress';
      case RideStatus.completed:
        return 'Trip completed';
      case RideStatus.cancelled:
        return 'Trip cancelled';
      default:
        return 'Ride in progress';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to WebSocket messages for real-time updates
    ref.listen(driverLocationProvider, (previous, next) {
      next.when(
        data: (message) {
          if (message.rideId == widget.ride.id) {
            final lat = message.data['latitude'] as double;
            final lng = message.data['longitude'] as double;
            _updateDriverLocation(custom.LatLng(lat, lng));
          }
        },
        loading: () {},
        error: (error, stack) {},
      );
    });

    ref.listen(rideStatusUpdateProvider, (previous, next) {
      next.when(
        data: (message) {
          if (message.rideId == widget.ride.id) {
            // Handle real-time ride status updates
            _handleRideStatusUpdate(message);
          }
        },
        loading: () {},
        error: (error, stack) {
          if (kDebugMode) {
            print('Error receiving ride status update: $error');
          }
        },
      );
    });

    return Scaffold(
      body: Stack(
        children: [
          // Map
          MapWidget(
            key: const ValueKey('tracking_map'),
            onMapCreated: _onMapCreated,
          ),

          // Top status bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: _buildStatusBar(),
          ),

          // Rider info card
          Positioned(
            top: MediaQuery.of(context).padding.top + 80,
            left: 16,
            right: 16,
            child: _buildRiderInfoCard(),
          ),

          // Bottom action panel
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildActionPanel(),
          ),

          // Chat overlay
          if (_showChat)
            Positioned.fill(
              child: RideChatWidget(
                rideId: widget.ride.id,
                riderId: widget.rider.id,
                onClose: _toggleChat,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusBar() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 80,
      borderRadius: BorderRadius.circular(12),
      blur: 10,
      opacity: 0.2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getStatusColor(),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getRideStatusText(),
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    _estimatedArrival,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            EmergencyButton(
              rideId: widget.ride.id,
              onPressed: _triggerEmergency,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRiderInfoCard() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 100,
      borderRadius: BorderRadius.circular(12),
      blur: 10,
      opacity: 0.2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage: NetworkImage(widget.rider.imageUrl),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.rider.name,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.rider.rating} (${widget.rider.reviewCount})',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    '${widget.rider.motorcycleModel} • ${widget.rider.licensePlate}',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            AnimatedScaleButton(
              onTap: _callRider,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.phone, color: Colors.white, size: 20),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionPanel() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 120,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      blur: 10,
      opacity: 0.2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Expanded(
              child: AnimatedScaleButton(
                onTap: _toggleChat,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.chat_bubble_outline,
                          color: Colors.white),
                      const SizedBox(width: 8),
                      Text(
                        'Chat',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnimatedScaleButton(
                onTap: () {
                  // Share ride details
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.share, color: Colors.white),
                      const SizedBox(width: 8),
                      Text(
                        'Share',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.ride.status) {
      case RideStatus.accepted:
        return Colors.blue;
      case RideStatus.enRouteToPickup:
        return Colors.orange;
      case RideStatus.arrivedAtPickup:
        return Colors.green;
      case RideStatus.inProgress:
        return AppTheme.primaryColor;
      case RideStatus.completed:
        return Colors.green;
      case RideStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

class MapBounds {
  final custom.LatLng southwest;
  final custom.LatLng northeast;
  final custom.LatLng center;

  MapBounds({
    required this.southwest,
    required this.northeast,
    required this.center,
  });
}

class EmergencyDialog extends StatelessWidget {
  final String rideId;

  const EmergencyDialog({super.key, required this.rideId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Emergency'),
      content:
          const Text('Are you sure you want to trigger an emergency alert?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            // Trigger emergency
            Navigator.pop(context);
          },
          child: const Text('Emergency', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }
}
