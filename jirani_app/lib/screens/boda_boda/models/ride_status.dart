/// Enhanced status of a ride for the modern Boda Boda feature
enum RideStatus {
  /// Rider has been assigned to the ride
  riderAssigned,

  /// Rider is on the way to pick up the user
  riderEnRoute,

  /// Rider has arrived at the pickup location
  riderArrived,

  /// Trip has started
  tripStarted,

  /// Approaching the destination
  approachingDestination,

  /// Trip has been completed
  tripCompleted,

  /// Trip has been cancelled
  cancelled,
}
