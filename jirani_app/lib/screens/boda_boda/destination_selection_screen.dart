import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/theme.dart';
import '../../models/lat_lng.dart' as custom;
import '../../models/place_result.dart';
import '../../providers/location_provider.dart';
import '../../services/mapbox_service_new.dart';
import '../../widgets/animations/animated_scale_button.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'fare_estimation_screen.dart';

/// Step 3: Destination Selection Screen
/// Users can search for destinations or tap on the map to select
class DestinationSelectionScreen extends ConsumerStatefulWidget {
  final custom.LatLng pickupLocation;
  final String pickupAddress;

  const DestinationSelectionScreen({
    super.key,
    required this.pickupLocation,
    required this.pickupAddress,
  });

  @override
  ConsumerState<DestinationSelectionScreen> createState() =>
      _DestinationSelectionScreenState();
}

class _DestinationSelectionScreenState
    extends ConsumerState<DestinationSelectionScreen> {
  MapboxMap? _mapboxMap;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  custom.LatLng? _selectedDestination;
  String? _selectedDestinationAddress;
  bool _isSearching = false;
  List<PlaceResult> _searchResults = [];
  List<String> _recentDestinations = [];
  final List<String> _popularDestinations = [
    'KICC, Nairobi',
    'Westlands, Nairobi',
    'Karen, Nairobi',
    'Eastleigh, Nairobi',
    'Thika Road Mall',
    'Sarit Centre',
    'Junction Mall',
    'Two Rivers Mall',
  ];

  @override
  void initState() {
    super.initState();
    _loadRecentDestinations();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadRecentDestinations() {
    // Load from local storage
    _recentDestinations = [
      'Home - Kileleshwa',
      'Office - Westlands',
      'Gym - Kilimani',
    ];
  }

  void _onMapCreated(MapboxMap mapboxMap) {
    _mapboxMap = mapboxMap;
    _setupMap();
  }

  void _setupMap() async {
    if (_mapboxMap == null) return;

    // Set initial camera position to pickup location
    await _mapboxMap!.setCamera(
      CameraOptions(
        center: Point(
          coordinates: Position(
            widget.pickupLocation.longitude,
            widget.pickupLocation.latitude,
          ),
        ),
        zoom: 14.0,
      ),
    );

    // Add pickup location marker
    await _addPickupMarker();

    // Set up map click listener
    _mapboxMap!.setOnMapTapListener((context) {
      // For now, use a simple approach - in production you'd convert screen coordinates to lat/lng
      final latLng = custom.LatLng(
        widget.pickupLocation.latitude + 0.01, // Mock offset
        widget.pickupLocation.longitude + 0.01,
      );
      _onMapTapped(latLng);
    });
  }

  Future<void> _addPickupMarker() async {
    // Add pickup marker logic here
    // This would use Mapbox annotations API
  }

  void _onMapTapped(custom.LatLng latLng) async {
    setState(() {
      _selectedDestination = latLng;
      _selectedDestinationAddress = null;
    });

    // Reverse geocode to get address
    try {
      final address = await MapboxServiceNew.reverseGeocode(latLng);
      setState(() {
        _selectedDestinationAddress = address ?? 'Selected Location';
      });
    } catch (e) {
      setState(() {
        _selectedDestinationAddress = 'Selected Location';
      });
    }

    // Add destination marker
    await _addDestinationMarker(latLng);
  }

  Future<void> _addDestinationMarker(custom.LatLng location) async {
    // Add destination marker logic here
  }

  void _onSearchChanged(String query) async {
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults.clear();
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results = await MapboxServiceNew.searchPlaces(query);
      setState(() {
        _searchResults = results;
      });
    } catch (e) {
      setState(() {
        _searchResults.clear();
      });
    }
  }

  void _selectSearchResult(PlaceResult result) {
    setState(() {
      _selectedDestination = custom.LatLng(result.latitude, result.longitude);
      _selectedDestinationAddress = result.name;
      _searchController.text = result.name;
      _searchFocusNode.unfocus();
      _isSearching = false;
      _searchResults.clear();
    });

    // Move map to selected location
    _mapboxMap?.setCamera(
      CameraOptions(
        center: Point(
          coordinates: Position(
            result.longitude,
            result.latitude,
          ),
        ),
        zoom: 15.0,
      ),
    );

    // Add destination marker
    _addDestinationMarker(custom.LatLng(result.latitude, result.longitude));
  }

  void _selectDestination(String destination) {
    _searchController.text = destination;
    _onSearchChanged(destination);
  }

  void _proceedToFareEstimation() {
    if (_selectedDestination == null) return;

    // Set the dropoff location in the provider
    ref
        .read(dropoffLocationProvider.notifier)
        .setLocation(_selectedDestination!);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FareEstimationScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Map
          MapWidget(
            key: const ValueKey('destination_map'),
            onMapCreated: _onMapCreated,
          ),

          // Top search bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: _buildSearchBar(),
          ),

          // Search results or suggestions
          if (_isSearching || _searchController.text.isEmpty)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              right: 16,
              bottom: _selectedDestination != null ? 120 : 16,
              child: _buildSearchResults(),
            ),

          // Selected destination info and continue button
          if (_selectedDestination != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildDestinationInfo(),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 120,
      borderRadius: BorderRadius.circular(12),
      blur: 10,
      opacity: 0.2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Pickup location (read-only)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.pickupAddress,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Destination search field
            TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              onChanged: _onSearchChanged,
              style: GoogleFonts.inter(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Where to?',
                hintStyle: GoogleFonts.inter(color: Colors.white70),
                prefixIcon: Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.white70),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 400,
      borderRadius: BorderRadius.circular(12),
      blur: 10,
      opacity: 0.2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isSearching && _searchResults.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Search Results',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: _searchResults.length,
                itemBuilder: (context, index) {
                  final result = _searchResults[index];
                  return ListTile(
                    leading:
                        const Icon(Icons.location_on, color: Colors.white70),
                    title: Text(
                      result.name,
                      style: GoogleFonts.inter(color: Colors.white),
                    ),
                    subtitle: Text(
                      result.address,
                      style: GoogleFonts.inter(
                          color: Colors.white70, fontSize: 12),
                    ),
                    onTap: () => _selectSearchResult(result),
                  );
                },
              ),
            ),
          ] else if (!_isSearching) ...[
            // Recent destinations
            if (_recentDestinations.isNotEmpty) ...[
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Recent',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              ...(_recentDestinations.take(3).map((destination) => ListTile(
                    leading: const Icon(Icons.history, color: Colors.white70),
                    title: Text(
                      destination,
                      style: GoogleFonts.inter(color: Colors.white),
                    ),
                    onTap: () => _selectDestination(destination),
                  ))),
            ],

            // Popular destinations
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Popular Destinations',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: _popularDestinations.length,
                itemBuilder: (context, index) {
                  final destination = _popularDestinations[index];
                  return ListTile(
                    leading:
                        const Icon(Icons.trending_up, color: Colors.white70),
                    title: Text(
                      destination,
                      style: GoogleFonts.inter(color: Colors.white),
                    ),
                    onTap: () => _selectDestination(destination),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDestinationInfo() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 120,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      blur: 10,
      opacity: 0.2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedDestinationAddress ?? 'Selected Location',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            AnimatedScaleButton(
              onTap: _proceedToFareEstimation,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Continue',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Mock search result model
class MapboxSearchResult {
  final String name;
  final String address;
  final custom.LatLng location;

  MapboxSearchResult({
    required this.name,
    required this.address,
    required this.location,
  });
}
