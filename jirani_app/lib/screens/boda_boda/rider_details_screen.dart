import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/rider.dart';
import '../../providers/location_provider.dart';
import '../../providers/rider_provider.dart';
import '../../services/rider_service.dart';
import '../../widgets/animations/animated_scale_button.dart';
import 'location_selection_screen.dart';

/// Screen displaying detailed information about a rider
class RiderDetailsScreen extends ConsumerWidget {
  /// Creates a new instance of [RiderDetailsScreen]
  const RiderDetailsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedRider = ref.watch(selectedRiderProvider);
    final userLocationAsync = ref.watch(userLocationProvider);
    final etaAsync = ref.watch(riderEtaProvider);

    if (selectedRider == null) {
      return const SizedBox.shrink();
    }

    // Calculate distance from user
    double? distanceFromUser;
    if (userLocationAsync is AsyncData && userLocationAsync.value != null) {
      distanceFromUser = RiderService.calculateDistance(
        lat1: userLocationAsync.value!.latitude,
        lon1: userLocationAsync.value!.longitude,
        lat2: selectedRider.location.latitude,
        lon2: selectedRider.location.longitude,
      );
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.9,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.all(24),
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 24),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Rider header
              Row(
                children: [
                  CircleAvatar(
                    radius: 36,
                    backgroundImage: NetworkImage(selectedRider.imageUrl),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedRider.name,
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${selectedRider.rating.toStringAsFixed(1)} (${selectedRider.reviewCount} reviews)',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              selectedRider.status == RiderStatus.available
                                  ? Icons.circle
                                  : (selectedRider.status == RiderStatus.busy
                                      ? Icons.access_time_filled
                                      : Icons.cancel),
                              color: selectedRider.status ==
                                      RiderStatus.available
                                  ? Colors.green
                                  : (selectedRider.status == RiderStatus.busy
                                      ? Colors.orange
                                      : Colors.red),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              selectedRider.status == RiderStatus.available
                                  ? 'Available'
                                  : (selectedRider.status == RiderStatus.busy
                                      ? 'Busy'
                                      : 'Offline'),
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Distance and ETA
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primaryContainer
                      .withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          const Icon(
                            Icons.location_on,
                            color: Colors.red,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            distanceFromUser != null
                                ? '${distanceFromUser.toStringAsFixed(1)} km'
                                : 'Unknown',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 4),
                          const Text('Distance'),
                        ],
                      ),
                    ),
                    Container(
                      height: 40,
                      width: 1,
                      color: Colors.grey.withOpacity(0.3),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          const Icon(
                            Icons.access_time,
                            color: Colors.blue,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          etaAsync.when(
                            data: (eta) => Text(
                              '$eta min',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            loading: () => const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            error: (_, __) => Text(
                              'Unknown',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          const Text('ETA'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Motorcycle details
              _buildInfoSection(
                context,
                title: 'Motorcycle Details',
                icon: Icons.motorcycle,
                items: [
                  _InfoItem(
                    icon: Icons.directions_car,
                    label: 'Model',
                    value: selectedRider.motorcycleModel,
                  ),
                  _InfoItem(
                    icon: Icons.palette,
                    label: 'Color',
                    value: selectedRider.motorcycleColor,
                  ),
                  _InfoItem(
                    icon: Icons.confirmation_number,
                    label: 'License Plate',
                    value: selectedRider.licensePlate,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Rider stats
              _buildInfoSection(
                context,
                title: 'Rider Stats',
                icon: Icons.bar_chart,
                items: [
                  _InfoItem(
                    icon: Icons.motorcycle,
                    label: 'Rides Completed',
                    value: selectedRider.ridesCompleted.toString(),
                  ),
                  _InfoItem(
                    icon: Icons.calendar_today,
                    label: 'Joined',
                    value:
                        '${DateTime.now().difference(selectedRider.joinedDate).inDays} days ago',
                  ),
                  if (selectedRider.isVerified)
                    const _InfoItem(
                      icon: Icons.verified,
                      label: 'Verification',
                      value: 'Verified',
                    ),
                ],
              ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: AnimatedScaleButton(
                      onTap: () async {
                        final phoneUri = Uri(
                          scheme: 'tel',
                          path: selectedRider.phoneNumber,
                        );

                        if (await canLaunchUrl(phoneUri)) {
                          await launchUrl(phoneUri);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.phone,
                              color: Colors.white,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Call',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: AnimatedScaleButton(
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) =>
                                const LocationSelectionScreen(),
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.directions,
                              color: Colors.white,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Book Ride',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<_InfoItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${item.label}:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey,
                        ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item.value,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }
}

class _InfoItem {
  final IconData icon;
  final String label;
  final String value;

  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
  });
}
