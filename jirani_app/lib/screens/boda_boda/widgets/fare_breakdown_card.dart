import 'package:flutter/material.dart';
import '../../../app/theme.dart';

/// A card displaying the fare breakdown
class FareBreakdownCard extends StatelessWidget {
  /// The estimated fare
  final double estimatedFare;
  
  /// The estimated distance in kilometers
  final double estimatedDistance;
  
  /// The estimated duration in minutes
  final int estimatedDuration;
  
  /// Creates a new instance of [FareBreakdownCard]
  const FareBreakdownCard({
    super.key,
    required this.estimatedFare,
    required this.estimatedDistance,
    required this.estimatedDuration,
  });
  
  @override
  Widget build(BuildContext context) {
    // Calculate fare components
    const double baseFare = 100.0;
    final double distanceFare = estimatedDistance * 50.0;
    final double timeFare = estimatedDuration * 5.0;
    
    return Card(
      elevation: 0,
      color: Colors.grey[100],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Trip details
            Row(
              children: [
                const Icon(
                  Icons.motorcycle,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Boda Boda',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  'KSh ${estimatedFare.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Distance and time
            Row(
              children: [
                const Icon(
                  Icons.route,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${estimatedDistance.toStringAsFixed(1)} km',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(width: 16),
                const Icon(
                  Icons.access_time,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '$estimatedDuration min',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // Fare breakdown
            _FareBreakdownItem(
              title: 'Base Fare',
              amount: baseFare,
            ),
            const SizedBox(height: 8),
            _FareBreakdownItem(
              title: 'Distance (${estimatedDistance.toStringAsFixed(1)} km)',
              amount: distanceFare,
            ),
            const SizedBox(height: 8),
            _FareBreakdownItem(
              title: 'Time ($estimatedDuration min)',
              amount: timeFare,
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'KSh ${estimatedFare.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// A single item in the fare breakdown
class _FareBreakdownItem extends StatelessWidget {
  final String title;
  final double amount;
  
  const _FareBreakdownItem({
    required this.title,
    required this.amount,
  });
  
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          'KSh ${amount.toStringAsFixed(0)}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
}
