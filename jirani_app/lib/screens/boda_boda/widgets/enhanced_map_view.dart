import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Size;
import '../../../app/theme.dart';
import '../../../providers/enhanced_location_provider.dart';
import '../../../services/enhanced_location_service.dart';
import '../../../widgets/animations/animated_scale_button.dart';

/// Enhanced map view following the design specification
/// PRP-LOCATION-ENH-001 Phase 2 Implementation
class EnhancedMapView extends ConsumerStatefulWidget {
  final Function(double lat, double lng)? onMapTap;
  final VoidCallback? onLocationButtonTap;
  final VoidCallback? onStartNavigation;

  const EnhancedMapView({
    super.key,
    this.onMapTap,
    this.onLocationButtonTap,
    this.onStartNavigation,
  });

  @override
  ConsumerState<EnhancedMapView> createState() => _EnhancedMapViewState();
}

class _EnhancedMapViewState extends ConsumerState<EnhancedMapView>
    with TickerProviderStateMixin {
  MapboxMap? _mapController;
  PointAnnotationManager? _annotationManager;
  PolylineAnnotationManager? _polylineManager;
  bool _isMapInitialized = false;
  
  late AnimationController _locationButtonController;
  late Animation<double> _locationButtonAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize location button animation
    _locationButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _locationButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _locationButtonController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _locationButtonController.dispose();
    super.dispose();
  }

  void _onMapCreated(MapboxMap mapboxMap) {
    _mapController = mapboxMap;
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    if (_mapController == null) return;

    try {
      // Create annotation managers
      _annotationManager = await _mapController!.annotations.createPointAnnotationManager();
      _polylineManager = await _mapController!.annotations.createPolylineAnnotationManager();
      
      // Set map style
      await _mapController!.style.setStyleURI(MapboxStyles.STANDARD);
      
      // Configure map settings
      await _mapController!.gestures.updateSettings(
        GesturesSettings(
          rotateEnabled: true,
          scrollEnabled: true,
          tiltEnabled: false,
          zoomEnabled: true,
        ),
      );
      
      // Set initial camera position (Nairobi)
      await _mapController!.setCamera(
        CameraOptions(
          center: Point(coordinates: Position(36.8219, -1.2921)),
          zoom: 12.0,
        ),
      );
      
      setState(() {
        _isMapInitialized = true;
      });
      
      // Update markers with current locations
      _updateMapMarkers();
      
    } catch (e) {
      debugPrint('Error initializing map: $e');
    }
  }

  void _updateMapMarkers() {
    if (!_isMapInitialized || _annotationManager == null) return;

    final pickup = ref.read(pickupLocationProvider);
    final dropoff = ref.read(dropoffLocationProvider);
    
    _annotationManager!.deleteAll();
    
    // Add pickup marker
    if (pickup != null) {
      _addMarker(
        pickup.latitude,
        pickup.longitude,
        'pickup',
        AppTheme.primaryColor,
      );
    }
    
    // Add dropoff marker
    if (dropoff != null) {
      _addMarker(
        dropoff.latitude,
        dropoff.longitude,
        'dropoff',
        AppTheme.accentColor,
      );
    }
    
    // Draw route if both locations are set
    if (pickup != null && dropoff != null) {
      _drawRoute(pickup, dropoff);
    }
  }

  void _addMarker(double lat, double lng, String type, Color color) {
    if (_annotationManager == null) return;

    final pointAnnotationOptions = PointAnnotationOptions(
      geometry: Point(coordinates: Position(lng, lat)),
      iconImage: type == 'pickup' ? 'pickup-marker' : 'dropoff-marker',
      iconSize: 1.0,
      iconColor: color.value,
    );

    _annotationManager!.create(pointAnnotationOptions);
  }

  void _drawRoute(SavedLocation pickup, SavedLocation dropoff) {
    if (_polylineManager == null) return;

    // Clear existing routes
    _polylineManager!.deleteAll();

    // Create a simple straight line route (in production, use routing API)
    final coordinates = [
      Position(pickup.longitude, pickup.latitude),
      Position(dropoff.longitude, dropoff.latitude),
    ];

    final polylineAnnotationOptions = PolylineAnnotationOptions(
      geometry: LineString(coordinates: coordinates),
      lineColor: AppTheme.primaryColor.value,
      lineWidth: 3.0,
    );

    _polylineManager!.create(polylineAnnotationOptions);
    
    // Fit camera to show both points
    _fitCameraToPoints([pickup, dropoff]);
  }

  void _fitCameraToPoints(List<SavedLocation> locations) {
    if (_mapController == null || locations.isEmpty) return;

    if (locations.length == 1) {
      // Single location - center on it
      _mapController!.setCamera(
        CameraOptions(
          center: Point(coordinates: Position(
            locations.first.longitude,
            locations.first.latitude,
          )),
          zoom: 15.0,
        ),
      );
      return;
    }

    // Multiple locations - fit bounds
    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = minLat < location.latitude ? minLat : location.latitude;
      maxLat = maxLat > location.latitude ? maxLat : location.latitude;
      minLng = minLng < location.longitude ? minLng : location.longitude;
      maxLng = maxLng > location.longitude ? maxLng : location.longitude;
    }

    // Add padding
    const padding = 0.01;
    minLat -= padding;
    maxLat += padding;
    minLng -= padding;
    maxLng += padding;

    _mapController!.setCamera(
      CameraOptions(
        center: Point(coordinates: Position(
          (minLng + maxLng) / 2,
          (minLat + maxLat) / 2,
        )),
        zoom: 12.0,
      ),
    );
  }

  void _animateToUserLocation() async {
    try {
      final locationService = ref.read(enhancedLocationServiceProvider);
      final currentLocation = await locationService.getCurrentLocation();
      
      if (currentLocation.latitude != null && currentLocation.longitude != null) {
        await _mapController?.setCamera(
          CameraOptions(
            center: Point(coordinates: Position(
              currentLocation.longitude!,
              currentLocation.latitude!,
            )),
            zoom: 15.0,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to location changes
    ref.listen(pickupLocationProvider, (previous, next) {
      _updateMapMarkers();
    });
    
    ref.listen(dropoffLocationProvider, (previous, next) {
      _updateMapMarkers();
    });

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          // Map Container
          Positioned.fill(
            child: MapWidget(
              key: const ValueKey('mapbox'),
              onMapCreated: _onMapCreated,
              onTapListener: (coordinate) {
                widget.onMapTap?.call(
                  coordinate.coordinates.lat,
                  coordinate.coordinates.lng,
                );
              },
            ),
          ),
          
          // Floating Location Button
          Positioned(
            right: 12,
            bottom: 140,
            child: AnimatedBuilder(
              animation: _locationButtonAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _locationButtonAnimation.value,
                  child: AnimatedScaleButton(
                    onTap: () {
                      _locationButtonController.forward().then((_) {
                        _locationButtonController.reverse();
                      });
                      _animateToUserLocation();
                      widget.onLocationButtonTap?.call();
                    },
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(22),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            offset: const Offset(0, 2),
                            blurRadius: 4,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.my_location,
                        size: 20,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Bottom Sheet
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: _buildBottomSheet(context),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSheet(BuildContext context) {
    final pickup = ref.watch(pickupLocationProvider);
    final dropoff = ref.watch(dropoffLocationProvider);
    final canStartNavigation = pickup != null && dropoff != null;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Start Now Button
            AnimatedScaleButton(
              onTap: canStartNavigation ? () {
                widget.onStartNavigation?.call();
              } : null,
              child: Container(
                width: double.infinity,
                height: 44,
                decoration: BoxDecoration(
                  color: canStartNavigation 
                      ? AppTheme.primaryColor 
                      : AppTheme.primaryColor.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(22),
                ),
                child: Center(
                  child: Text(
                    'Start Now',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
