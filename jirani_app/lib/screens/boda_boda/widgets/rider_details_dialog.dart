import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../models/rider.dart';
import '../../../providers/rider_provider.dart';
import '../../../widgets/animations/animated_scale_button.dart';
import '../../../widgets/common/error_widget.dart';
import '../../../widgets/common/loading_widget.dart';

/// Rider details dialog
class RiderDetailsDialog extends ConsumerWidget {
  /// Rider
  final Rider rider;

  /// Callback when the rider is called
  final VoidCallback? onCall;

  /// Creates a new instance of [RiderDetailsDialog]
  const RiderDetailsDialog({
    super.key,
    required this.rider,
    this.onCall,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final riderAsync = ref.watch(riderProvider(rider.id));

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 400,
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: AnimatedScaleButton(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ),
            ),

            // Rider details
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: riderAsync.when(
                  data: (riderData) {
                    final rider = riderData ?? this.rider;
                    return _buildRiderDetails(context, rider);
                  },
                  loading: () => const AppLoadingWidget(
                      message: 'Loading rider details...'),
                  error: (error, stackTrace) => AppErrorWidget(
                    message: 'Failed to load rider details',
                    details: error.toString(),
                    onRetry: () => ref.refresh(riderProvider(rider.id)),
                  ),
                ),
              ),
            ),

            // Call button
            Padding(
              padding: const EdgeInsets.all(16),
              child: AnimatedScaleButton(
                onTap: onCall,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.call,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Call Rider',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build rider details
  Widget _buildRiderDetails(BuildContext context, Rider rider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Rider image
        _buildRiderImage(context, rider),

        const SizedBox(height: 16),

        // Rider name
        Text(
          rider.name,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
          textAlign: TextAlign.center,
        ).animate().fadeIn(
              duration: 300.ms,
              delay: 100.ms,
              curve: Curves.easeOut,
            ),

        const SizedBox(height: 8),

        // Rider rating
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star,
              size: 20,
              color: Colors.amber,
            ),
            const SizedBox(width: 4),
            Text(
              '${rider.rating} (${rider.ratingCount} ratings)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ).animate().fadeIn(
              duration: 300.ms,
              delay: 200.ms,
              curve: Curves.easeOut,
            ),

        const SizedBox(height: 24),

        // Rider info
        _buildInfoSection(context, rider),

        const SizedBox(height: 24),

        // Rider reviews
        _buildReviewsSection(context, rider),
      ],
    );
  }

  /// Build rider image
  Widget _buildRiderImage(BuildContext context, Rider rider) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(60),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(60),
        child: CachedNetworkImage(
          imageUrl: rider.imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
          errorWidget: (context, url, error) => const Icon(
            Icons.person,
            size: 60,
          ),
        ),
      ),
    ).animate().scale(
          duration: 600.ms,
          curve: Curves.elasticOut,
          begin: const Offset(0.5, 0.5),
          end: const Offset(1.0, 1.0),
        );
  }

  /// Build info section
  Widget _buildInfoSection(BuildContext context, Rider rider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Rider Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ).animate().fadeIn(
              duration: 300.ms,
              delay: 300.ms,
              curve: Curves.easeOut,
            ),

        const SizedBox(height: 16),

        // Info items
        _buildInfoItem(
          context,
          icon: Icons.motorcycle,
          label: 'Vehicle',
          value: rider.vehicleModel,
          delay: 400,
        ),

        _buildInfoItem(
          context,
          icon: Icons.confirmation_number,
          label: 'License Plate',
          value: rider.licensePlate,
          delay: 500,
        ),

        _buildInfoItem(
          context,
          icon: Icons.phone,
          label: 'Phone',
          value: rider.phone,
          delay: 600,
        ),

        _buildInfoItem(
          context,
          icon: Icons.access_time,
          label: 'Experience',
          value: rider.experience,
          delay: 700,
        ),
      ],
    );
  }

  /// Build info item
  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required int delay,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerLow,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),

          const SizedBox(width: 12),

          // Label and value
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Label
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),

                // Value
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(
          duration: 300.ms,
          delay: delay.ms,
          curve: Curves.easeOut,
        )
        .slideX(
          begin: 0.2,
          end: 0,
          duration: 300.ms,
          delay: delay.ms,
          curve: Curves.easeOut,
        );
  }

  /// Build reviews section
  Widget _buildReviewsSection(BuildContext context, Rider rider) {
    // In a real implementation, this would fetch reviews from the API
    // For now, we'll simulate it with mock data
    final reviews = List.generate(
      3,
      (index) => {
        'userName': 'User ${index + 1}',
        'rating': 3 + (index % 3),
        'review': index % 2 == 0
            ? 'Great rider, very professional!'
            : 'Good service, arrived on time.',
        'date': DateTime.now().subtract(Duration(days: index * 2)),
      },
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Reviews',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ).animate().fadeIn(
              duration: 300.ms,
              delay: 800.ms,
              curve: Curves.easeOut,
            ),

        const SizedBox(height: 16),

        // Reviews
        ...reviews.asMap().entries.map((entry) {
          final index = entry.key;
          final review = entry.value;

          return _buildReviewItem(
            context,
            userName: review['userName'] as String,
            rating: review['rating'] as int,
            review: review['review'] as String,
            date: review['date'] as DateTime,
            delay: 900 + (index * 100),
          );
        }),
      ],
    );
  }

  /// Build review item
  Widget _buildReviewItem(
    BuildContext context, {
    required String userName,
    required int rating,
    required String review,
    required DateTime date,
    required int delay,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerLowest,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User name and rating
            Row(
              children: [
                // User name
                Expanded(
                  child: Text(
                    userName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),

                // Rating
                Row(
                  children: List.generate(
                    5,
                    (index) => Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      size: 16,
                      color: Colors.amber,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Review
            Text(
              review,
              style: Theme.of(context).textTheme.bodyMedium,
            ),

            const SizedBox(height: 4),

            // Date
            Text(
              '${date.day}/${date.month}/${date.year}',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
      ),
    )
        .animate()
        .fadeIn(
          duration: 300.ms,
          delay: delay.ms,
          curve: Curves.easeOut,
        )
        .slideY(
          begin: 0.2,
          end: 0,
          duration: 300.ms,
          delay: delay.ms,
          curve: Curves.easeOut,
        );
  }
}
