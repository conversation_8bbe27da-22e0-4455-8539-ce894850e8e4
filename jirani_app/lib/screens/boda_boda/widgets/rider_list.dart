import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/rider.dart';
import '../../../providers/rider_provider.dart';
import '../../../widgets/common/error_widget.dart';
import '../../../widgets/common/loading_widget.dart';
import 'rider_card.dart';

/// Rider list widget
class RiderList extends ConsumerWidget {
  /// Selected rider
  final Rider? selectedRider;
  
  /// Callback when a rider is selected
  final Function(Rider) onRiderSelected;
  
  /// Callback when a rider is called
  final Function(Rider) onRiderCall;
  
  /// Callback when a rider's info is viewed
  final Function(Rider) onRiderInfo;
  
  /// Creates a new instance of [RiderList]
  const RiderList({
    super.key,
    this.selectedRider,
    required this.onRiderSelected,
    required this.onRiderCall,
    required this.onRiderInfo,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nearbyRidersAsync = ref.watch(nearbyRidersProvider);
    
    return Container(
      width: 320,
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(context),
          
          // Rider list
          Expanded(
            child: nearbyRidersAsync.when(
              data: (riders) => _buildRiderList(context, riders),
              loading: () => const AppLoadingWidget(message: 'Finding riders...'),
              error: (error, stackTrace) => AppErrorWidget(
                message: 'Failed to load riders',
                details: error.toString(),
                onRetry: () => ref.read(nearbyRidersProvider.notifier).refresh(),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build header
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.motorcycle,
            color: Theme.of(context).colorScheme.primary,
          ),
          
          const SizedBox(width: 8),
          
          Text(
            'Available Riders',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build rider list
  Widget _buildRiderList(BuildContext context, List<Rider> riders) {
    if (riders.isEmpty) {
      return EmptyStateWidget(
        message: 'No riders available',
        details: 'Try again in a few minutes',
        icon: Icons.motorcycle_outlined,
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: riders.length,
      itemBuilder: (context, index) {
        final rider = riders[index];
        final isSelected = selectedRider?.id == rider.id;
        
        return RiderCard(
          rider: rider,
          isSelected: isSelected,
          onSelect: () => onRiderSelected(rider),
          onCall: () => onRiderCall(rider),
          onViewInfo: () => onRiderInfo(rider),
        )
        .animate()
        .fadeIn(
          duration: 300.ms,
          delay: (index * 50).ms,
          curve: Curves.easeOut,
        )
        .slideY(
          begin: 0.2,
          end: 0,
          duration: 300.ms,
          delay: (index * 50).ms,
          curve: Curves.easeOut,
        );
      },
    );
  }
}
