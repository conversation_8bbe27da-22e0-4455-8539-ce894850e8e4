import 'package:flutter/material.dart';
import '../../../app/theme.dart';
import '../../../widgets/animations/animated_scale_button.dart';

/// A widget for sending quick messages to the rider
class RideChatButton extends StatelessWidget {
  /// Creates a new instance of [RideChatButton]
  const RideChatButton({super.key});
  
  @override
  Widget build(BuildContext context) {
    // Predefined messages
    final predefinedMessages = [
      'I\'m waiting at the pickup location',
      'I\'ll be there in a minute',
      'Please call me when you arrive',
      'I\'m wearing a blue shirt',
      'I\'m at the main entrance',
      'Please drive carefully',
    ];
    
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Title
            const Text(
              'Quick Messages',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Predefined messages
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: predefinedMessages.map((message) {
                return AnimatedScaleButton(
                  onTap: () {
                    // In a real app, this would send the message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Message sent: $message'),
                        backgroundColor: AppTheme.primaryColor,
                      ),
                    );
                    
                    // Close the bottom sheet
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppTheme.primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Custom message input
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Type a custom message...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                AnimatedScaleButton(
                  onTap: () {
                    // In a real app, this would send the custom message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Custom message sent'),
                        backgroundColor: AppTheme.primaryColor,
                      ),
                    );
                    
                    // Close the bottom sheet
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.send,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
