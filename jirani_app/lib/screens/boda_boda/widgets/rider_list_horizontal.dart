import 'package:flutter/material.dart';
import '../../../models/rider.dart';
import '../../../widgets/animations/animated_scale_button.dart';
import 'rider_card_horizontal.dart';

/// A horizontal list of riders
class RiderListHorizontal extends StatelessWidget {
  /// The list of riders to display
  final List<Rider> riders;

  /// The currently selected rider
  final Rider? selectedRider;

  /// Callback when a rider is selected
  final Function(Rider) onRiderSelected;

  /// Creates a new instance of [RiderListHorizontal]
  const RiderListHorizontal({
    super.key,
    required this.riders,
    this.selectedRider,
    required this.onRiderSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 150,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: riders.length,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemBuilder: (context, index) {
          final rider = riders[index];
          final isSelected = selectedRider?.id == rider.id;

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: AnimatedScaleButton(
              onTap: () => onRiderSelected(rider),
              child: RiderCardHorizontal(
                rider: rider,
                isSelected: isSelected,
              ),
            ),
          );
        },
      ),
    );
  }
}
