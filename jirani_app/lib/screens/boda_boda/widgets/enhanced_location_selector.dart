import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../app/theme.dart';
import '../../../providers/enhanced_location_provider.dart';
import '../../../services/enhanced_location_service.dart';
import '../../../widgets/animations/animated_scale_button.dart';
import '../../../widgets/common/glassmorphic_container.dart';

/// Enhanced location selector widget following the design specification
/// PRP-LOCATION-ENH-001 Phase 2 Implementation
class EnhancedLocationSelector extends ConsumerStatefulWidget {
  final VoidCallback? onLocationSelected;
  final bool isPickup;
  final String? initialAddress;

  const EnhancedLocationSelector({
    super.key,
    this.onLocationSelected,
    this.isPickup = true,
    this.initialAddress,
  });

  @override
  ConsumerState<EnhancedLocationSelector> createState() =>
      _EnhancedLocationSelectorState();
}

class _EnhancedLocationSelectorState
    extends ConsumerState<EnhancedLocationSelector>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isExpanded = false;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Set initial address if provided
    if (widget.initialAddress != null) {
      _searchController.text = widget.initialAddress!;
    }

    // Listen to search focus changes
    _searchFocusNode.addListener(_onSearchFocusChange);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchFocusChange() {
    if (_searchFocusNode.hasFocus && !_isExpanded) {
      _expandSelector();
    }
  }

  void _expandSelector() {
    setState(() {
      _isExpanded = true;
    });
    _animationController.forward();
  }

  void _collapseSelector() {
    setState(() {
      _isExpanded = false;
    });
    _animationController.reverse();
    _searchFocusNode.unfocus();
  }

  void _onLocationSelected(SavedLocation location) {
    _searchController.text = location.address;
    
    // Update the appropriate provider
    if (widget.isPickup) {
      ref.read(pickupLocationProvider.notifier).state = location;
    } else {
      ref.read(dropoffLocationProvider.notifier).state = location;
    }
    
    _collapseSelector();
    widget.onLocationSelected?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final locationHistory = ref.watch(locationHistoryProvider);
    final recentLocations = ref.watch(recentLocationsProvider);
    final favoriteLocations = ref.watch(favoriteLocationsProvider);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: _isExpanded ? MediaQuery.of(context).size.height * 0.7 : 80,
      child: GlassmorphicContainer(
        borderRadius: BorderRadius.circular(_isExpanded ? 16 : 22),
        child: Column(
          children: [
            // Search Input Field
            _buildSearchField(theme),
            
            // Expanded Content
            if (_isExpanded) ...[
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(_slideAnimation),
                    child: _buildExpandedContent(
                      locationHistory,
                      recentLocations,
                      favoriteLocations,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSearchField(ThemeData theme) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Location Icon
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: widget.isPickup ? AppTheme.primaryColor : AppTheme.accentColor,
              borderRadius: BorderRadius.circular(22),
            ),
            child: Icon(
              widget.isPickup ? Icons.location_on : Icons.flag,
              color: Colors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Search TextField
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
              decoration: InputDecoration(
                hintText: widget.isPickup ? 'Pickup location' : 'Destination',
                hintStyle: GoogleFonts.inter(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) {
                ref.read(locationSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          
          // Close Button (when expanded)
          if (_isExpanded)
            AnimatedScaleButton(
              onTap: _collapseSelector,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.close,
                  size: 18,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExpandedContent(
    AsyncValue<List<SavedLocation>> locationHistory,
    List<SavedLocation> recentLocations,
    AsyncValue<List<SavedLocation>> favoriteLocations,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Location Button
          _buildCurrentLocationButton(),
          
          const SizedBox(height: 16),
          
          // Recent Locations
          if (recentLocations.isNotEmpty) ...[
            _buildSectionHeader('Recent'),
            const SizedBox(height: 8),
            ...recentLocations.take(3).map((location) => 
              _buildLocationItem(location, Icons.history)),
            const SizedBox(height: 16),
          ],
          
          // Favorite Locations
          favoriteLocations.when(
            data: (favorites) {
              if (favorites.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader('Favorites'),
                    const SizedBox(height: 8),
                    ...favorites.take(3).map((location) => 
                      _buildLocationItem(location, Icons.favorite)),
                    const SizedBox(height: 16),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),
          
          // Search Results or All History
          Expanded(
            child: locationHistory.when(
              data: (locations) {
                final filteredLocations = ref.watch(filteredLocationHistoryProvider);
                
                if (filteredLocations.isEmpty) {
                  return _buildEmptyState();
                }
                
                return ListView.builder(
                  itemCount: filteredLocations.length,
                  itemBuilder: (context, index) {
                    final location = filteredLocations[index];
                    return _buildLocationItem(
                      location,
                      _getLocationTypeIcon(location.locationType),
                    );
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, _) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentLocationButton() {
    return AnimatedScaleButton(
      onTap: () async {
        // Get current location and use it
        try {
          final locationService = ref.read(enhancedLocationServiceProvider);
          final currentLocation = await locationService.getCurrentLocation();
          
          if (currentLocation.latitude != null && currentLocation.longitude != null) {
            final address = await locationService.getAddressFromCoordinates(
              currentLocation.latitude!,
              currentLocation.longitude!,
            );
            
            final savedLocation = SavedLocation(
              id: 'current',
              latitude: currentLocation.latitude!,
              longitude: currentLocation.longitude!,
              address: address,
              locationType: widget.isPickup ? 'pickup' : 'destination',
              usageCount: 1,
              lastUsedAt: DateTime.now(),
              createdAt: DateTime.now(),
            );
            
            _onLocationSelected(savedLocation);
          }
        } catch (e) {
          // Handle error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to get current location: $e')),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.my_location,
              color: AppTheme.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              'Use current location',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      ),
    );
  }

  Widget _buildLocationItem(SavedLocation location, IconData icon) {
    return AnimatedScaleButton(
      onTap: () => _onLocationSelected(location),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                location.address,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (location.usageCount > 1)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${location.usageCount}',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No saved locations',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your frequently used locations will appear here',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load locations',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getLocationTypeIcon(String locationType) {
    switch (locationType) {
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'favorite':
        return Icons.favorite;
      case 'pickup':
        return Icons.location_on;
      case 'destination':
        return Icons.flag;
      default:
        return Icons.place;
    }
  }
}
