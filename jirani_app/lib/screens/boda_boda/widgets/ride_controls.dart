import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/lat_lng.dart' as custom;
import '../../../models/rider.dart';
import '../../../providers/ride_provider.dart';
import '../../../providers/rider_provider.dart';
import '../../../utils/responsive_utils.dart';
import '../../../widgets/animations/animated_scale_button.dart';

/// Ride controls widget
class RideControls extends ConsumerStatefulWidget {
  /// Selected rider
  final Rider? selectedRider;
  
  /// Pickup location
  final custom.LatLng? pickupLocation;
  
  /// Dropoff location
  final custom.LatLng? dropoffLocation;
  
  /// Callback when the ride is requested
  final Function(Rider) onRideRequested;
  
  /// Callback when the pickup location is changed
  final Function(custom.LatLng) onPickupLocationChanged;
  
  /// Callback when the dropoff location is changed
  final Function(custom.LatLng) onDropoffLocationChanged;
  
  /// Creates a new instance of [RideControls]
  const RideControls({
    super.key,
    this.selectedRider,
    this.pickupLocation,
    this.dropoffLocation,
    required this.onRideRequested,
    required this.onPickupLocationChanged,
    required this.onDropoffLocationChanged,
  });
  
  @override
  ConsumerState<RideControls> createState() => _RideControlsState();
}

class _RideControlsState extends ConsumerState<RideControls> {
  bool _isExpanded = false;
  
  @override
  Widget build(BuildContext context) {
    final estimatedFareAsync = ref.watch(estimatedFareProvider);
    final riderEtaAsync = ref.watch(riderEtaProvider);
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final padding = ResponsiveUtils.getPadding(
          width,
          const EdgeInsets.symmetric(horizontal: 16),
        );
        
        return Padding(
          padding: padding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Ride details card
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Ride details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Pickup and dropoff locations
                          _buildLocationFields(),
                          
                          // Expand/collapse button
                          _buildExpandButton(),
                          
                          // Expanded details
                          if (_isExpanded) ...[
                            const SizedBox(height: 16),
                            
                            // Divider
                            Divider(
                              color: Theme.of(context).colorScheme.outlineVariant,
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Fare and ETA
                            _buildFareAndEta(estimatedFareAsync, riderEtaAsync),
                          ],
                        ],
                      ),
                    ),
                    
                    // Request ride button
                    _buildRequestRideButton(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// Build location fields
  Widget _buildLocationFields() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Pickup location
        _buildLocationField(
          icon: Icons.my_location,
          label: 'Pickup',
          value: widget.pickupLocation != null ? 'Current Location' : 'Select pickup location',
          iconColor: Colors.green,
          onTap: () {
            // TODO: Implement pickup location selection
          },
        ),
        
        // Location connector
        Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Row(
            children: [
              Container(
                width: 2,
                height: 24,
                color: Theme.of(context).colorScheme.outlineVariant,
              ),
            ],
          ),
        ),
        
        // Dropoff location
        _buildLocationField(
          icon: Icons.location_on,
          label: 'Dropoff',
          value: widget.dropoffLocation != null ? 'Selected Location' : 'Select dropoff location',
          iconColor: Colors.red,
          onTap: () {
            // TODO: Implement dropoff location selection
          },
        ),
      ],
    );
  }
  
  /// Build location field
  Widget _buildLocationField({
    required IconData icon,
    required String label,
    required String value,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return AnimatedScaleButton(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerLow,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                size: 16,
                color: iconColor,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Label and value
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Label
                  Text(
                    label,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  
                  // Value
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // Edit icon
            Icon(
              Icons.edit,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build expand button
  Widget _buildExpandButton() {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: AnimatedScaleButton(
        onTap: () {
          setState(() {
            _isExpanded = !_isExpanded;
          });
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isExpanded ? 'Less details' : 'More details',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(width: 4),
            
            Icon(
              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build fare and ETA
  Widget _buildFareAndEta(
    AsyncValue<double?> estimatedFareAsync,
    AsyncValue<int> riderEtaAsync,
  ) {
    return Row(
      children: [
        // Estimated fare
        Expanded(
          child: _buildInfoCard(
            icon: Icons.attach_money,
            label: 'Estimated Fare',
            value: estimatedFareAsync.when(
              data: (fare) => fare != null ? 'KES ${fare.toStringAsFixed(0)}' : 'N/A',
              loading: () => 'Calculating...',
              error: (_, __) => 'N/A',
            ),
            iconColor: Colors.green,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // ETA
        Expanded(
          child: _buildInfoCard(
            icon: Icons.access_time,
            label: 'Estimated Time',
            value: riderEtaAsync.when(
              data: (eta) => '$eta min',
              loading: () => 'Calculating...',
              error: (_, __) => 'N/A',
            ),
            iconColor: Colors.orange,
          ),
        ),
      ],
    )
    .animate()
    .fadeIn(
      duration: 300.ms,
      curve: Curves.easeOut,
    )
    .slideY(
      begin: 0.2,
      end: 0,
      duration: 300.ms,
      curve: Curves.easeOut,
    );
  }
  
  /// Build info card
  Widget _buildInfoCard({
    required IconData icon,
    required String label,
    required String value,
    required Color iconColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              icon,
              size: 16,
              color: iconColor,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Label and value
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Label
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                
                // Value
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build request ride button
  Widget _buildRequestRideButton() {
    final canRequestRide = widget.selectedRider != null &&
        widget.pickupLocation != null &&
        widget.dropoffLocation != null;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: AnimatedScaleButton(
        onTap: canRequestRide
            ? () {
                if (widget.selectedRider != null) {
                  widget.onRideRequested(widget.selectedRider!);
                }
              }
            : null,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 16,
          ),
          decoration: BoxDecoration(
            color: canRequestRide
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: canRequestRide
                  ? Colors.transparent
                  : Theme.of(context).colorScheme.outlineVariant,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.motorcycle,
                color: canRequestRide
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              
              const SizedBox(width: 8),
              
              Text(
                canRequestRide
                    ? 'Request Ride'
                    : widget.selectedRider == null
                        ? 'Select a Rider'
                        : 'Set Pickup & Dropoff',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: canRequestRide
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
