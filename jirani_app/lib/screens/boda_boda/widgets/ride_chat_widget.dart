import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../app/theme.dart';
import '../../../models/websocket_message.dart';
import '../../../providers/websocket_provider.dart';
import '../../../widgets/animations/animated_scale_button.dart';
import '../../../widgets/common/glassmorphic_container.dart';
import '../../../services/logging_service.dart';

/// Chat widget for communication between rider and passenger
class RideChatWidget extends ConsumerStatefulWidget {
  final String rideId;
  final String riderId;
  final VoidCallback onClose;

  const RideChatWidget({
    super.key,
    required this.rideId,
    required this.riderId,
    required this.onClose,
  });

  @override
  ConsumerState<RideChatWidget> createState() => _RideChatWidgetState();
}

class _RideChatWidgetState extends ConsumerState<RideChatWidget> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];

  // Predefined quick messages
  final List<String> _quickMessages = [
    'I\'m waiting at the pickup location',
    'I\'ll be there in a minute',
    'Please call me when you arrive',
    'I\'m wearing a blue shirt',
    'I\'m at the main entrance',
    'Please drive carefully',
    'Thank you!',
  ];

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadChatHistory() {
    // Load chat history from local storage or API
    // For now, adding some sample messages
    _messages.addAll([
      ChatMessage(
        id: '1',
        message: 'Hi! I\'m on my way to pick you up.',
        senderId: widget.riderId,
        senderRole: 'driver',
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        isMe: false,
      ),
      ChatMessage(
        id: '2',
        message: 'Great! I\'m waiting at the main entrance.',
        senderId: 'current_user',
        senderRole: 'user',
        timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
        isMe: true,
      ),
    ]);
  }

  void _sendMessage(String message) {
    if (message.trim().isEmpty) return;

    final chatMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      message: message.trim(),
      senderId: 'current_user', // Would get from auth service
      senderRole: 'user',
      timestamp: DateTime.now(),
      isMe: true,
    );

    setState(() {
      _messages.add(chatMessage);
    });

    // Send via WebSocket
    ref.read(webSocketProvider.notifier).sendChatMessage(
      widget.rideId,
      message.trim(),
      widget.riderId,
    );

    _messageController.clear();
    _scrollToBottom();
  }

  void _sendQuickMessage(String message) {
    _sendMessage(message);
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// Initiate a call to the driver
  Future<void> _callDriver() async {
    try {
      // In a real app, this would get the driver's phone number from the ride data
      // For now, we'll use a placeholder
      const driverPhone = '+254700000000'; // This would come from ride data

      final uri = Uri.parse('tel:$driverPhone');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);

        // Send a WebSocket message to notify the driver of the call attempt
        final callMessage = WebSocketMessage(
          type: WebSocketMessageType.call,
          rideId: widget.rideId,
          data: {
            'caller_id': 'current_user', // Would get from auth service
            'call_type': 'voice',
            'action': 'initiated',
          },
          timestamp: DateTime.now().millisecondsSinceEpoch,
        );
        ref.read(webSocketProvider.notifier).sendMessage(callMessage);

        LoggingService.i('Call initiated to driver');
      } else {
        _showErrorSnackBar('Unable to make phone call');
      }
    } catch (e) {
      LoggingService.e('Error initiating call', error: e);
      _showErrorSnackBar('Failed to initiate call');
    }
  }

  /// Show error message to user
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    // Listen for incoming chat messages
    ref.listen(chatMessageProvider, (previous, next) {
      next.when(
        data: (message) {
          if (message.rideId == widget.rideId) {
            final chatMessage = ChatMessage(
              id: message.timestamp.toString(),
              message: message.data['message'] as String,
              senderId: message.data['from'] as String,
              senderRole: message.data['from_role'] as String? ?? 'driver',
              timestamp: DateTime.fromMillisecondsSinceEpoch(message.timestamp),
              isMe: false,
            );

            setState(() {
              _messages.add(chatMessage);
            });
            _scrollToBottom();
          }
        },
        loading: () {},
        error: (error, stack) {},
      );
    });

    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              bottom: 16,
            ),
            child: Row(
              children: [
                AnimatedScaleButton(
                  onTap: widget.onClose,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.close, color: Colors.white),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Chat with Rider',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                // Call button
                AnimatedScaleButton(
                  onTap: _callDriver,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.phone,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Chat messages
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: GlassmorphicContainer(
                width: double.infinity,
                height: double.infinity,
                borderRadius: BorderRadius.circular(16),
                blur: 10,
                opacity: 0.2,
                child: Column(
                  children: [
                  // Messages list
                  Expanded(
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        return _buildMessageBubble(message);
                      },
                    ),
                  ),

                  // Quick messages
                  Container(
                    height: 60,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _quickMessages.length,
                      itemBuilder: (context, index) {
                        final quickMessage = _quickMessages[index];
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: AnimatedScaleButton(
                            onTap: () => _sendQuickMessage(quickMessage),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                ),
                              ),
                              child: Text(
                                quickMessage,
                                style: GoogleFonts.inter(
                                  fontSize: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Message input
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            style: GoogleFonts.inter(color: Colors.white),
                            decoration: InputDecoration(
                              hintText: 'Type a message...',
                              hintStyle: GoogleFonts.inter(color: Colors.white70),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.1),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                            ),
                            onSubmitted: _sendMessage,
                          ),
                        ),
                        const SizedBox(width: 12),
                        AnimatedScaleButton(
                          onTap: () => _sendMessage(_messageController.text),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: AppTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: const Icon(
                              Icons.send,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: message.isMe
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          if (!message.isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.white.withOpacity(0.2),
              child: Icon(
                message.senderRole == 'driver' ? Icons.motorcycle : Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isMe
                    ? AppTheme.primaryColor.withOpacity(0.8)
                    : Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20).copyWith(
                  bottomLeft: message.isMe ? const Radius.circular(20) : const Radius.circular(4),
                  bottomRight: message.isMe ? const Radius.circular(4) : const Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.message,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.white.withOpacity(0.2),
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}

/// Chat message model
class ChatMessage {
  final String id;
  final String message;
  final String senderId;
  final String senderRole;
  final DateTime timestamp;
  final bool isMe;

  ChatMessage({
    required this.id,
    required this.message,
    required this.senderId,
    required this.senderRole,
    required this.timestamp,
    required this.isMe,
  });
}
