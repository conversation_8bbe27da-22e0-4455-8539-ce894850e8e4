import 'package:flutter/material.dart';
import '../../utils/responsive_utils.dart';

/// Responsive layout for the boda boda screen
class ResponsiveBodyLayout extends StatelessWidget {
  /// Map widget
  final Widget mapWidget;
  
  /// Controls widget
  final Widget controlsWidget;
  
  /// Rider list widget
  final Widget riderListWidget;
  
  /// Whether the rider list is expanded
  final bool isRiderListExpanded;
  
  /// Callback when the rider list is toggled
  final VoidCallback onRiderListToggle;
  
  /// Creates a new instance of [ResponsiveBodyLayout]
  const ResponsiveBodyLayout({
    super.key,
    required this.mapWidget,
    required this.controlsWidget,
    required this.riderListWidget,
    required this.isRiderListExpanded,
    required this.onRiderListToggle,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final deviceType = ResponsiveUtils.getDeviceType(width);
        
        switch (deviceType) {
          case DeviceType.mobile:
            return _buildMobileLayout(context);
          case DeviceType.tablet:
            return _buildTabletLayout(context);
          case DeviceType.desktop:
            return _buildDesktopLayout(context);
        }
      },
    );
  }
  
  /// Build mobile layout
  Widget _buildMobileLayout(BuildContext context) {
    return Stack(
      children: [
        // Map
        mapWidget,
        
        // Rider list toggle button
        Positioned(
          top: 16,
          right: 16,
          child: _buildRiderListToggleButton(context),
        ),
        
        // Rider list
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          top: 16,
          right: isRiderListExpanded ? 16 : -300,
          child: riderListWidget,
        ),
        
        // Controls
        Positioned(
          bottom: 24,
          left: 0,
          right: 0,
          child: controlsWidget,
        ),
      ],
    );
  }
  
  /// Build tablet layout
  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        // Map and controls
        Expanded(
          flex: 3,
          child: Stack(
            children: [
              // Map
              mapWidget,
              
              // Controls
              Positioned(
                bottom: 24,
                left: 0,
                right: 0,
                child: controlsWidget,
              ),
            ],
          ),
        ),
        
        // Rider list
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          width: isRiderListExpanded ? 320 : 0,
          child: isRiderListExpanded ? riderListWidget : const SizedBox(),
        ),
        
        // Rider list toggle button
        Container(
          width: 48,
          alignment: Alignment.center,
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: _buildRiderListToggleButton(context, isVertical: true),
        ),
      ],
    );
  }
  
  /// Build desktop layout
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Map and controls
        Expanded(
          flex: 4,
          child: Stack(
            children: [
              // Map
              mapWidget,
              
              // Controls
              Positioned(
                bottom: 24,
                left: 0,
                right: 0,
                child: controlsWidget,
              ),
            ],
          ),
        ),
        
        // Rider list
        SizedBox(
          width: 350,
          child: riderListWidget,
        ),
      ],
    );
  }
  
  /// Build rider list toggle button
  Widget _buildRiderListToggleButton(BuildContext context, {bool isVertical = false}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onRiderListToggle,
        borderRadius: BorderRadius.circular(24),
        child: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.9),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            isRiderListExpanded 
                ? (isVertical ? Icons.chevron_right : Icons.close)
                : (isVertical ? Icons.chevron_left : Icons.list),
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ),
    );
  }
}
