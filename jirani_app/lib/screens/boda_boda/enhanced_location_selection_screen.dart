import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/theme.dart';
import '../../providers/enhanced_location_provider.dart';
import '../../services/enhanced_location_service.dart';
import '../../widgets/common/glassmorphic_container.dart';
import 'widgets/enhanced_location_selector.dart';
import 'widgets/enhanced_map_view.dart';
import 'fare_estimation_screen.dart';

/// Enhanced location selection screen following the design specification
/// PRP-LOCATION-ENH-001 Phase 2 Implementation
class EnhancedLocationSelectionScreen extends ConsumerStatefulWidget {
  const EnhancedLocationSelectionScreen({super.key});

  @override
  ConsumerState<EnhancedLocationSelectionScreen> createState() =>
      _EnhancedLocationSelectionScreenState();
}

class _EnhancedLocationSelectionScreenState
    extends ConsumerState<EnhancedLocationSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  late Animation<double> _headerSlideAnimation;
  late Animation<double> _headerFadeAnimation;

  bool _isPickupSelectorVisible = false;
  bool _isDropoffSelectorVisible = false;

  @override
  void initState() {
    super.initState();

    // Initialize header animations
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _headerSlideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    ));

    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    ));

    // Start header animation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _headerAnimationController.forward();
      _initializeLocationService();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializeLocationService() async {
    try {
      final locationService = ref.read(enhancedLocationServiceProvider);
      await locationService.initialize();

      // Start location tracking with high accuracy
      await locationService.startLocationTracking(
        accuracyMode: LocationAccuracyMode.high,
      );

      // Load location history
      final locationHistoryNotifier =
          ref.read(locationHistoryStateProvider.notifier);
      await locationHistoryNotifier.refresh();
    } catch (e) {
      debugPrint('Failed to initialize location service: $e');
      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location service initialization failed: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showPickupSelector() {
    setState(() {
      _isPickupSelectorVisible = true;
      _isDropoffSelectorVisible = false;
    });
  }

  void _showDropoffSelector() {
    setState(() {
      _isDropoffSelectorVisible = true;
      _isPickupSelectorVisible = false;
    });
  }

  void _hideSelectors() {
    setState(() {
      _isPickupSelectorVisible = false;
      _isDropoffSelectorVisible = false;
    });
  }

  void _onLocationSelected() {
    _hideSelectors();
  }

  void _onStartNavigation() {
    final pickup = ref.read(pickupLocationProvider);
    final dropoff = ref.read(dropoffLocationProvider);

    if (pickup != null && dropoff != null) {
      // Navigate to fare estimation screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const FareEstimationScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final pickup = ref.watch(pickupLocationProvider);
    final dropoff = ref.watch(dropoffLocationProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Map View
          Positioned.fill(
            child: EnhancedMapView(
              onMapTap: (lat, lng) {
                // Handle map tap if needed
                _hideSelectors();
              },
              onLocationButtonTap: () {
                // Handle location button tap
              },
              onStartNavigation: _onStartNavigation,
            ),
          ),

          // Transparent Header with Status Bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: AnimatedBuilder(
              animation: _headerAnimationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _headerFadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -1),
                      end: Offset.zero,
                    ).animate(_headerSlideAnimation),
                    child: _buildHeader(context),
                  ),
                );
              },
            ),
          ),

          // Location Selectors
          if (_isPickupSelectorVisible)
            Positioned(
              top: MediaQuery.of(context).padding.top + 60,
              left: 16,
              right: 16,
              child: EnhancedLocationSelector(
                isPickup: true,
                initialAddress: pickup?.address,
                onLocationSelected: _onLocationSelected,
              ),
            ),

          if (_isDropoffSelectorVisible)
            Positioned(
              top: MediaQuery.of(context).padding.top + 60,
              left: 16,
              right: 16,
              child: EnhancedLocationSelector(
                isPickup: false,
                initialAddress: dropoff?.address,
                onLocationSelected: _onLocationSelected,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final pickup = ref.watch(pickupLocationProvider);
    final dropoff = ref.watch(dropoffLocationProvider);

    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        left: 16,
        right: 16,
        bottom: 12,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
            Colors.white.withOpacity(0.0),
          ],
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          children: [
            // Back Button and Title
            Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Select Locations',
                    style: GoogleFonts.inter(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Compact Location Fields
            GlassmorphicContainer(
              width: double.infinity,
              height: 120,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  children: [
                    // Pickup Field
                    GestureDetector(
                      onTap: _showPickupSelector,
                      child: _buildCompactLocationField(
                        icon: Icons.location_on,
                        iconColor: AppTheme.primaryColor,
                        label: 'From',
                        address: pickup?.address ?? 'Select pickup location',
                        isSelected: pickup != null,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Divider with swap button
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 1,
                            color: Colors.grey.withOpacity(0.3),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.grey.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.swap_vert,
                            size: 16,
                            color: Colors.grey.withOpacity(0.6),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            height: 1,
                            color: Colors.grey.withOpacity(0.3),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Dropoff Field
                    GestureDetector(
                      onTap: _showDropoffSelector,
                      child: _buildCompactLocationField(
                        icon: Icons.flag,
                        iconColor: AppTheme.accentColor,
                        label: 'To',
                        address: dropoff?.address ?? 'Select destination',
                        isSelected: dropoff != null,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactLocationField({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String address,
    required bool isSelected,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? iconColor.withOpacity(0.1)
            : Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected
              ? iconColor.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: iconColor,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey.withOpacity(0.7),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              address,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.black : Colors.grey.withOpacity(0.6),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
