import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart' as app_theme;
import '../services/auth_service.dart';
import '../widgets/animations/animated_scale_button.dart';
import 'edit_profile_screen.dart';

/// Profile screen
class ProfileScreen extends ConsumerStatefulWidget {
  /// Creates a new instance of [ProfileScreen]
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _userData;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Use ref.read to get the AuthService instance
      final authService = ref.read(authServiceProvider);
      final userData = await authService.getUserProfile();

      setState(() {
        _userData = userData['user'];
      });
    } catch (e) {
      // Handle error - Message is now shown by BaseApiService via MessageProvider
      // if (mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(
      //       content: Text('Failed to load profile: ${e.toString()}'),
      //       backgroundColor: Colors.red,
      //     ),
      //   );
      // }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _logout() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Use ref.read to get the AuthService instance
      final authService = ref.read(authServiceProvider);
      await authService.logout();

      if (mounted) {
        context.go('/login');
      }
    } catch (e) {
      // Handle error - Message is now shown by BaseApiService via MessageProvider
      // if (mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(
      //       content: Text('Failed to logout: ${e.toString()}'),
      //       backgroundColor: Colors.red,
      //     ),
      //   );
      // }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final favoriteIds = ref.watch(favoriteServiceIdsProvider);
    final themeMode = ref.watch(app_theme.themeModeProvider);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primaryContainer,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: GridView.builder(
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 5,
                          ),
                          itemBuilder: (context, index) {
                            return Container(
                              margin: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.white,
                                  width: 0.5,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    // Profile info
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            // Profile image
                            _isLoading
                                ? const CircularProgressIndicator(color: Colors.white)
                                : _userData != null && _userData!['profile_image'] != null
                                    ? CircleAvatar(
                                        radius: 40,
                                        backgroundColor: Colors.white,
                                        backgroundImage: CachedNetworkImageProvider(
                                          _userData!['profile_image'],
                                        ),
                                      )
                                    : CircleAvatar(
                                        radius: 40,
                                        backgroundColor: Colors.white,
                                        child: Text(
                                          _userData != null && _userData!['full_name'] != null
                                              ? _userData!['full_name'][0].toUpperCase()
                                              : '?',
                                          style: TextStyle(
                                            fontSize: 32,
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context).colorScheme.primary,
                                          ),
                                        ),
                                      ),

                            const SizedBox(width: 16),

                            // Profile details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _userData != null && _userData!['full_name'] != null
                                        ? _userData!['full_name']
                                        : 'Loading...',
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _userData != null && _userData!['email'] != null
                                        ? _userData!['email']
                                        : 'Loading...',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                  ),
                                  // Placeholder for Provider Role
                                  // TODO: Fetch this from a user state provider that includes role/provider type
                                  if (_userData != null && _userData!['provider_type'] != null) // Assuming 'provider_type' will be added
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4.0),
                                      child: Text(
                                        'Role: ${_userData!['provider_type']}',
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                              color: Colors.white.withOpacity(0.7),
                                              fontStyle: FontStyle.italic,
                                            ),
                                      ),
                                    ),
                                  // Fallback for local state if backend doesn't provide it yet
                                  // else if (ref.watch(userProvider).isBodaBodaDriver) // Example of a local state check
                                  //   Padding(
                                  //     padding: const EdgeInsets.only(top: 4.0),
                                  //     child: Text(
                                  //       'Role: Boda Boda Driver (Pending Verification)',
                                  //       style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  //             color: Colors.white.withOpacity(0.7),
                                  //             fontStyle: FontStyle.italic,
                                  //           ),
                                  //     ),
                                  //   ),
                                ],
                              ),
                            ),


                            // Edit button
                            AnimatedScaleButton(
                              onTap: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const EditProfileScreen(),
                                  ),
                                ).then((_) => _loadUserData());
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.edit,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Stats
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      icon: Icons.favorite,
                      title: 'Favorites',
                      value: favoriteIds.length.toString(),
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      icon: Icons.history,
                      title: 'History',
                      value: '12',
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      icon: Icons.star,
                      title: 'Reviews',
                      value: '8',
                      color: Colors.amber,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Settings
          SliverList(
            delegate: SliverChildListDelegate([
              const SizedBox(height: 16),

              // Settings header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Theme mode
              _buildSettingsTile(
                context,
                icon: Icons.dark_mode,
                title: 'Dark Mode',
                trailing: Switch(
                  value: themeMode == app_theme.ThemeMode.dark,
                  onChanged: (value) {
                    ref.read(app_theme.themeModeProvider.notifier).setThemeMode(
                      value ? app_theme.ThemeMode.dark : app_theme.ThemeMode.light,
                    );
                    HapticFeedback.lightImpact();
                  },
                ),
              ),

              // Notifications
              _buildSettingsTile(
                context,
                icon: Icons.notifications,
                title: 'Notifications',
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Implement notifications settings
                },
              ),

              // Privacy
              _buildSettingsTile(
                context,
                icon: Icons.lock,
                title: 'Privacy',
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Implement privacy settings
                },
              ),

              // Help & Support
              _buildSettingsTile(
                context,
                icon: Icons.help,
                title: 'Help & Support',
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Implement help & support
                },
              ),

              // About
              _buildSettingsTile(
                context,
                icon: Icons.info,
                title: 'About',
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Implement about
                },
              ),

              const SizedBox(height: 16),

              // Logout
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: AnimatedScaleButton(
                  onTap: () {
                    HapticFeedback.mediumImpact();
                    _logout();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : Text(
                              'Logout',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 32),
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return AnimatedScaleButton(
      onTap: () {
        // TODO: Implement stat details
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    )
    .animate()
    .fadeIn(
      duration: 600.ms,
      curve: Curves.easeOut,
    )
    .slideY(
      begin: 0.2,
      end: 0,
      duration: 600.ms,
      curve: Curves.easeOut,
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return AnimatedScaleButton(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              trailing,
            ],
          ),
        ),
      ),
    )
    .animate()
    .fadeIn(
      duration: 600.ms,
      curve: Curves.easeOut,
    )
    .slideX(
      begin: 0.2,
      end: 0,
      duration: 600.ms,
      curve: Curves.easeOut,
    );
  }
}
