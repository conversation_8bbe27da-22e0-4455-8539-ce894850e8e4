/// WebSocket message types
enum WebSocketMessageType {
  // Ride-related messages
  rideRequest('ride_request'),
  rideAccepted('ride_accepted'),
  rideStatusUpdate('ride_status_update'),
  locationUpdate('location_update'),
  driverLocation('driver_location'),

  // Communication messages
  chat('chat'),
  call('call'),
  notification('notification'),

  // Emergency messages
  emergency('emergency'),

  // System messages
  error('error'),
  heartbeat('heartbeat');

  const WebSocketMessageType(this.value);
  final String value;

  static WebSocketMessageType fromString(String value) {
    return WebSocketMessageType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => WebSocketMessageType.error,
    );
  }
}

/// WebSocket message model
class WebSocketMessage {
  final WebSocketMessageType type;
  final String? userId;
  final String? rideId;
  final Map<String, dynamic> data;
  final int timestamp;

  const WebSocketMessage({
    required this.type,
    this.userId,
    this.rideId,
    required this.data,
    required this.timestamp,
  });

  /// Create from JSON
  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: WebSocketMessageType.fromString(json['type'] ?? ''),
      userId: json['user_id'],
      rideId: json['ride_id'],
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      timestamp: json['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.value,
      if (userId != null) 'user_id': userId,
      if (rideId != null) 'ride_id': rideId,
      'data': data,
      'timestamp': timestamp,
    };
  }

  /// Create a ride status update message
  factory WebSocketMessage.rideStatusUpdate({
    required String rideId,
    required String status,
    Map<String, dynamic>? additionalData,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.rideStatusUpdate,
      rideId: rideId,
      data: {
        'status': status,
        ...?additionalData,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Create a location update message
  factory WebSocketMessage.locationUpdate({
    required double latitude,
    required double longitude,
    String? rideId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.locationUpdate,
      rideId: rideId,
      data: {
        'latitude': latitude,
        'longitude': longitude,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Create a driver location message
  factory WebSocketMessage.driverLocation({
    required double latitude,
    required double longitude,
    required String driverId,
    String? rideId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.driverLocation,
      rideId: rideId,
      data: {
        'latitude': latitude,
        'longitude': longitude,
        'driver_id': driverId,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Create a chat message
  factory WebSocketMessage.chat({
    required String rideId,
    required String message,
    required String fromUserId,
    required String toUserId,
    String? fromRole,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.chat,
      rideId: rideId,
      data: {
        'message': message,
        'from': fromUserId,
        'to': toUserId,
        if (fromRole != null) 'from_role': fromRole,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Create a notification message
  factory WebSocketMessage.notification({
    required String title,
    required String body,
    Map<String, dynamic>? additionalData,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.notification,
      data: {
        'title': title,
        'body': body,
        ...?additionalData,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Create a heartbeat message
  factory WebSocketMessage.heartbeat() {
    return WebSocketMessage(
      type: WebSocketMessageType.heartbeat,
      data: {'status': 'alive'},
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  @override
  String toString() {
    return 'WebSocketMessage(type: $type, userId: $userId, rideId: $rideId, data: $data, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WebSocketMessage &&
        other.type == type &&
        other.userId == userId &&
        other.rideId == rideId &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return type.hashCode ^
        userId.hashCode ^
        rideId.hashCode ^
        timestamp.hashCode;
  }
}
