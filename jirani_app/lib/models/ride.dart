import 'lat_lng.dart';

/// Status of a ride
enum RideStatus {
  /// Ride has been requested but not yet accepted by a rider
  requested,

  /// Ride has been accepted by a rider
  accepted,

  /// Rider is on the way to pick up the user
  enRouteToPickup,

  /// Rider has arrived at the pickup location
  arrivedAtPickup,

  /// Ride is in progress
  inProgress,

  /// Ride has been completed
  completed,

  /// Ride has been cancelled
  cancelled,
}

/// Payment method for a ride
enum PaymentMethod {
  /// Cash payment
  cash,

  /// Mobile money payment
  mobileMoney,

  /// Card payment
  card,
}

/// A model representing a Boda Boda ride
class Ride {
  /// Unique identifier for the ride
  final String id;

  /// User who requested the ride
  final String userId;

  /// Rider who is assigned to the ride
  final String? riderId;

  /// Pickup location of the ride
  final RideLocation pickup;

  /// Dropoff location of the ride
  final RideLocation dropoff;

  /// Current status of the ride
  final RideStatus status;

  /// Estimated fare for the ride
  final double estimatedFare;

  /// Actual fare for the ride (after completion)
  final double? actualFare;

  /// Estimated distance of the ride in kilometers
  final double estimatedDistance;

  /// Estimated duration of the ride in minutes
  final int estimatedDuration;

  /// Actual distance of the ride in kilometers (after completion)
  final double? distance;

  /// Actual duration of the ride in minutes (after completion)
  final int? duration;

  /// Surge pricing factor
  final double surgeFactor;

  /// Payment method for the ride
  final PaymentMethod paymentMethod;

  /// Whether the ride has been paid
  final bool isPaid;

  /// Rating given by the user to the rider (1-5)
  final int? rating;

  /// Review given by the user to the rider
  final String? review;

  /// Timestamp of when the ride was requested
  final DateTime requestedAt;

  /// Timestamp of when the ride was accepted
  final DateTime? acceptedAt;

  /// Timestamp of when the ride was started
  final DateTime? startedAt;

  /// Timestamp of when the ride was completed
  final DateTime? completedAt;

  /// Creates a new instance of [Ride]
  const Ride({
    required this.id,
    required this.userId,
    this.riderId,
    required this.pickup,
    required this.dropoff,
    required this.status,
    required this.estimatedFare,
    this.actualFare,
    required this.estimatedDistance,
    required this.estimatedDuration,
    this.distance,
    this.duration,
    this.surgeFactor = 1.0,
    required this.paymentMethod,
    this.isPaid = false,
    this.rating,
    this.review,
    required this.requestedAt,
    this.acceptedAt,
    this.startedAt,
    this.completedAt,
  });

  /// Creates a new instance of [Ride] from a JSON object
  factory Ride.fromJson(Map<String, dynamic> json) {
    return Ride(
      id: json['id'] as String,
      userId: json['userId'] as String,
      riderId: json['riderId'] as String?,
      pickup: RideLocation.fromJson(json['pickup'] as Map<String, dynamic>),
      dropoff: RideLocation.fromJson(json['dropoff'] as Map<String, dynamic>),
      status: RideStatus.values.firstWhere(
        (e) => e.toString() == 'RideStatus.${json['status']}',
        orElse: () => RideStatus.requested,
      ),
      estimatedFare: (json['estimatedFare'] as num).toDouble(),
      actualFare: (json['actualFare'] as num?)?.toDouble(),
      estimatedDistance: (json['estimatedDistance'] as num).toDouble(),
      estimatedDuration: json['estimatedDuration'] as int,
      distance: (json['distance'] as num?)?.toDouble(),
      duration: json['duration'] as int?,
      surgeFactor: (json['surgeFactor'] as num?)?.toDouble() ?? 1.0,
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString() == 'PaymentMethod.${json['paymentMethod']}',
        orElse: () => PaymentMethod.cash,
      ),
      isPaid: json['isPaid'] as bool? ?? false,
      rating: json['rating'] as int?,
      review: json['review'] as String?,
      requestedAt: DateTime.parse(json['requestedAt'] as String),
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'] as String)
          : null,
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
    );
  }

  /// Converts this [Ride] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'riderId': riderId,
      'pickup': pickup.toJson(),
      'dropoff': dropoff.toJson(),
      'status': status.toString().split('.').last,
      'estimatedFare': estimatedFare,
      'actualFare': actualFare,
      'estimatedDistance': estimatedDistance,
      'estimatedDuration': estimatedDuration,
      'distance': distance,
      'duration': duration,
      'surgeFactor': surgeFactor,
      'paymentMethod': paymentMethod.toString().split('.').last,
      'isPaid': isPaid,
      'rating': rating,
      'review': review,
      'requestedAt': requestedAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  /// Returns a copy of this [Ride] with the given fields replaced with the new values
  Ride copyWith({
    String? id,
    String? userId,
    String? riderId,
    RideLocation? pickup,
    RideLocation? dropoff,
    RideStatus? status,
    double? estimatedFare,
    double? actualFare,
    double? estimatedDistance,
    int? estimatedDuration,
    double? distance,
    int? duration,
    double? surgeFactor,
    PaymentMethod? paymentMethod,
    bool? isPaid,
    int? rating,
    String? review,
    DateTime? requestedAt,
    DateTime? acceptedAt,
    DateTime? startedAt,
    DateTime? completedAt,
  }) {
    return Ride(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      riderId: riderId ?? this.riderId,
      pickup: pickup ?? this.pickup,
      dropoff: dropoff ?? this.dropoff,
      status: status ?? this.status,
      estimatedFare: estimatedFare ?? this.estimatedFare,
      actualFare: actualFare ?? this.actualFare,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      distance: distance ?? this.distance,
      duration: duration ?? this.duration,
      surgeFactor: surgeFactor ?? this.surgeFactor,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      requestedAt: requestedAt ?? this.requestedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}

/// A model representing a location for a ride
class RideLocation {
  /// Latitude of the location
  final double latitude;

  /// Longitude of the location
  final double longitude;

  /// Address of the location
  final String address;

  /// Name of the location (e.g., "Home", "Work", "Nairobi CBD")
  final String? name;

  /// LatLng location for compatibility with new components
  LatLng get location => LatLng(latitude, longitude);

  /// Creates a new instance of [RideLocation]
  const RideLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.name,
  });

  /// Creates a new instance of [RideLocation] from a JSON object
  factory RideLocation.fromJson(Map<String, dynamic> json) {
    return RideLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      name: json['name'] as String?,
    );
  }

  /// Converts this [RideLocation] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'name': name,
    };
  }
}
