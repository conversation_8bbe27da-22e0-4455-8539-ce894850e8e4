import 'package:flutter/foundation.dart';

/// Model class for place search results
class PlaceResult {
  /// Unique identifier for the place
  final String id;

  /// Name of the place
  final String name;

  /// Full address of the place
  final String address;

  /// Latitude coordinate
  final double latitude;

  /// Longitude coordinate
  final double longitude;

  /// Creates a new instance of [PlaceResult]
  PlaceResult({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  /// Creates a [PlaceResult] from a Mapbox feature
  factory PlaceResult.fromMapboxFeature(Map<String, dynamic> feature) {
    try {
      final center = feature['center'] as List;
      return PlaceResult(
        id: feature['id'] as String? ?? '',
        name: feature['text'] as String? ?? '',
        address: feature['place_name'] as String? ?? '',
        longitude: (center[0] as num).toDouble(),
        latitude: (center[1] as num).toDouble(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error creating PlaceResult from Mapbox feature: $e');
        print('Feature data: $feature');
      }
      // Return a default place result with empty values
      return PlaceResult(
        id: '',
        name: 'Unknown location',
        address: '',
        latitude: 0,
        longitude: 0,
      );
    }
  }

  @override
  String toString() {
    return 'PlaceResult(id: $id, name: $name, address: $address, latitude: $latitude, longitude: $longitude)';
  }
}
