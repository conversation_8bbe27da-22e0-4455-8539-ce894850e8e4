/// Rider review model
class RiderReview {
  /// Review ID
  final String id;
  
  /// Rider ID
  final String riderId;
  
  /// User ID
  final String userId;
  
  /// User name
  final String userName;
  
  /// User image URL
  final String? userImageUrl;
  
  /// Rating
  final int rating;
  
  /// Review text
  final String? review;
  
  /// Review date
  final DateTime date;
  
  /// Creates a new instance of [RiderReview]
  const RiderReview({
    required this.id,
    required this.riderId,
    required this.userId,
    required this.userName,
    this.userImageUrl,
    required this.rating,
    this.review,
    required this.date,
  });
  
  /// Creates a new instance of [RiderReview] from a JSON object
  factory RiderReview.fromJson(Map<String, dynamic> json) {
    return RiderReview(
      id: json['id'] as String,
      riderId: json['riderId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userImageUrl: json['userImageUrl'] as String?,
      rating: json['rating'] as int,
      review: json['review'] as String?,
      date: DateTime.parse(json['date'] as String),
    );
  }
  
  /// Converts this [RiderReview] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'riderId': riderId,
      'userId': userId,
      'userName': userName,
      'userImageUrl': userImageUrl,
      'rating': rating,
      'review': review,
      'date': date.toIso8601String(),
    };
  }
}
