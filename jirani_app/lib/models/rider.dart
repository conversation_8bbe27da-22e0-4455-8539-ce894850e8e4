/// Status of a rider
enum RiderStatus {
  /// Rider is online and available
  available,

  /// Rider is online but busy with a ride
  busy,

  /// Rider is offline
  offline,
}

/// A model representing a Boda Boda rider
class Rider {
  /// Unique identifier for the rider
  final String id;

  /// Name of the rider
  final String name;

  /// Phone number of the rider
  final String phoneNumber;

  /// Email of the rider (optional)
  final String? email;

  /// Profile image URL of the rider
  final String imageUrl;

  /// Current location of the rider
  final RiderLocation location;

  /// Current status of the rider
  final RiderStatus status;

  /// Rating of the rider (out of 5)
  final double rating;

  /// Number of rides completed by the rider
  final int ridesCompleted;

  /// Number of reviews received by the rider
  final int reviewCount;

  /// Alias for reviewCount to maintain compatibility with new components
  int get ratingCount => reviewCount;

  /// License plate number of the rider's motorcycle
  final String licensePlate;

  /// Motorcycle model of the rider
  final String motorcycleModel;

  /// Alias for motorcycleModel to maintain compatibility with new components
  String get vehicleModel => motorcycleModel;

  /// Motorcycle color of the rider
  final String motorcycleColor;

  /// Distance from user in kilometers (calculated field)
  final double distance;

  /// Estimated time of arrival in minutes (calculated field)
  final int eta;

  /// Phone number alias for compatibility
  String get phone => phoneNumber;

  /// Experience in years (calculated field)
  String get experience =>
      '${DateTime.now().difference(joinedDate).inDays ~/ 365 + 1} years';

  /// Whether the rider is verified
  final bool isVerified;

  /// Date when the rider joined the platform
  final DateTime joinedDate;

  /// Creates a new instance of [Rider]
  const Rider({
    required this.id,
    required this.name,
    required this.phoneNumber,
    this.email,
    required this.imageUrl,
    required this.location,
    required this.status,
    required this.rating,
    required this.ridesCompleted,
    required this.reviewCount,
    required this.licensePlate,
    required this.motorcycleModel,
    required this.motorcycleColor,
    this.isVerified = false,
    required this.joinedDate,
    this.distance = 0.0,
    this.eta = 0,
  });

  /// Creates a new instance of [Rider] from a JSON object
  factory Rider.fromJson(Map<String, dynamic> json) {
    return Rider(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String?,
      imageUrl: json['imageUrl'] as String,
      location:
          RiderLocation.fromJson(json['location'] as Map<String, dynamic>),
      status: RiderStatus.values.firstWhere(
        (e) => e.toString() == 'RiderStatus.${json['status']}',
        orElse: () => RiderStatus.offline,
      ),
      rating: (json['rating'] as num).toDouble(),
      ridesCompleted: json['ridesCompleted'] as int,
      reviewCount: json['reviewCount'] as int,
      licensePlate: json['licensePlate'] as String,
      motorcycleModel: json['motorcycleModel'] as String,
      motorcycleColor: json['motorcycleColor'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
      joinedDate: DateTime.parse(json['joinedDate'] as String),
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      eta: (json['eta'] as num?)?.toInt() ?? 0,
    );
  }

  /// Converts this [Rider] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'imageUrl': imageUrl,
      'location': location.toJson(),
      'status': status.toString().split('.').last,
      'rating': rating,
      'ridesCompleted': ridesCompleted,
      'reviewCount': reviewCount,
      'licensePlate': licensePlate,
      'motorcycleModel': motorcycleModel,
      'motorcycleColor': motorcycleColor,
      'isVerified': isVerified,
      'joinedDate': joinedDate.toIso8601String(),
      'distance': distance,
      'eta': eta,
    };
  }

  /// Returns a copy of this [Rider] with the given fields replaced with the new values
  Rider copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    String? imageUrl,
    RiderLocation? location,
    RiderStatus? status,
    double? rating,
    int? ridesCompleted,
    int? reviewCount,
    String? licensePlate,
    String? motorcycleModel,
    String? motorcycleColor,
    bool? isVerified,
    DateTime? joinedDate,
    double? distance,
    int? eta,
  }) {
    return Rider(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      imageUrl: imageUrl ?? this.imageUrl,
      location: location ?? this.location,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      ridesCompleted: ridesCompleted ?? this.ridesCompleted,
      reviewCount: reviewCount ?? this.reviewCount,
      licensePlate: licensePlate ?? this.licensePlate,
      motorcycleModel: motorcycleModel ?? this.motorcycleModel,
      motorcycleColor: motorcycleColor ?? this.motorcycleColor,
      isVerified: isVerified ?? this.isVerified,
      joinedDate: joinedDate ?? this.joinedDate,
      distance: distance ?? this.distance,
      eta: eta ?? this.eta,
    );
  }
}

/// A model representing a rider's location
class RiderLocation {
  /// Latitude of the rider's location
  final double latitude;

  /// Longitude of the rider's location
  final double longitude;

  /// Heading direction of the rider in degrees (0-360, where 0 is North)
  final double heading;

  /// Speed of the rider in meters per second
  final double speed;

  /// Timestamp of when the location was recorded
  final DateTime timestamp;

  /// Creates a new instance of [RiderLocation]
  const RiderLocation({
    required this.latitude,
    required this.longitude,
    this.heading = 0,
    this.speed = 0,
    required this.timestamp,
  });

  /// Creates a new instance of [RiderLocation] from a JSON object
  factory RiderLocation.fromJson(Map<String, dynamic> json) {
    return RiderLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      heading: (json['heading'] as num?)?.toDouble() ?? 0,
      speed: (json['speed'] as num?)?.toDouble() ?? 0,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Converts this [RiderLocation] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'heading': heading,
      'speed': speed,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
