/// A service model
class Service {
  /// The unique identifier of the service
  final String id;
  
  /// The name of the service
  final String name;
  
  /// The description of the service
  final String description;
  /// The category of the service 
  final String category;
  /// The price of the service
  final double price;
  
  /// The rating of the service
  final double rating;
  
  /// The number of reviews
  final int reviewCount;
  
  /// The image URL of the service
  final String imageUrl;
  
  /// The provider of the service
  final ServiceProvider provider;
  
  /// Whether the service is featured
  final bool isFeatured;
  
  /// Whether the service is popular
  final bool isPopular;

  /// Creates a new instance of [Service]
  const Service({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.price,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.provider,
    this.isFeatured = false,
    this.isPopular = false,
  });

  /// Creates a new instance of [Service] from a JSON object
  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      price: (json['price'] as num).toDouble(),
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      imageUrl: json['imageUrl'] as String,
      provider: ServiceProvider.fromJson(json['provider'] as Map<String, dynamic>),
      isFeatured: json['isFeatured'] as bool? ?? false,
      isPopular: json['isPopular'] as bool? ?? false,
    );
  }

  /// Converts this [Service] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'price': price,
      'rating': rating,
      'reviewCount': reviewCount,
      'imageUrl': imageUrl,
      'provider': provider.toJson(),
      'isFeatured': isFeatured,
      'isPopular': isPopular,
    };
  }
}

/// A service provider model
class ServiceProvider {
  /// The unique identifier of the provider
  final String id;
  
  /// The name of the provider
  final String name;
  
  /// The description of the provider
  final String description;
  
  /// The address of the provider
  final String address;
  
  /// The phone number of the provider
  final String phone;
  
  /// The email of the provider
  final String email;
  
  /// The website of the provider
  final String? website;
  
  /// The image URL of the provider
  final String imageUrl;
  
  /// The location of the provider
  final Location location;

  /// Creates a new instance of [ServiceProvider]
  const ServiceProvider({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.phone,
    required this.email,
    this.website,
    required this.imageUrl,
    required this.location,
  });

  /// Creates a new instance of [ServiceProvider] from a JSON object
  factory ServiceProvider.fromJson(Map<String, dynamic> json) {
    return ServiceProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String,
      website: json['website'] as String?,
      imageUrl: json['imageUrl'] as String,
      location: Location.fromJson(json['location'] as Map<String, dynamic>),
    );
  }

  /// Converts this [ServiceProvider] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'phone': phone,
      'email': email,
      'website': website,
      'imageUrl': imageUrl,
      'location': location.toJson(),
    };
  }
}

/// A location model
class Location {
  /// The latitude of the location
  final double latitude;
  
  /// The longitude of the location
  final double longitude;

  /// Creates a new instance of [Location]
  const Location({
    required this.latitude,
    required this.longitude,
  });

  /// Creates a new instance of [Location] from a JSON object
  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );
  }

  /// Converts this [Location] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
