import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'driver.g.dart';

@JsonSerializable()
class Driver extends Equatable {
  final String id;
  final String userId;
  final String licenseNumber;
  final String idNumber;
  final int rating;
  final bool isAvailable;
  final bool isVerified;
  final double currentLatitude;
  final double currentLongitude;
  @Json<PERSON><PERSON>(name: 'LastLocationUpdate')
  final DateTime lastLocationUpdate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'CreatedAt')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'UpdatedAt')
  final DateTime updatedAt;
  @J<PERSON><PERSON><PERSON>(name: 'DeletedAt')
  final DateTime? deletedAt;
  final User user;
  final List<Vehicle> vehicles;

  const Driver({
    required this.id,
    required this.userId,
    required this.licenseNumber,
    required this.idNumber,
    required this.rating,
    required this.isAvailable,
    required this.isVerified,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.lastLocationUpdate,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.user,
    required this.vehicles,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => _$Driver<PERSON>rom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$DriverToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        licenseNumber,
        idNumber,
        rating,
        isAvailable,
        isVerified,
        currentLatitude,
        currentLongitude,
        lastLocationUpdate,
        createdAt,
        updatedAt,
        deletedAt,
        user,
        vehicles,
      ];
}

@JsonSerializable()
class User extends Equatable {
  final String id;
  final String email;
  @JsonKey(name: 'PasswordHash')
  final String passwordHash;
  @JsonKey(name: 'FullName')
  final String fullName;
  @JsonKey(name: 'PhoneNumber')
  final String phoneNumber;
  @JsonKey(name: 'ProfileImage')
  final String profileImage;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const User({
    required this.id,
    required this.email,
    required this.passwordHash,
    required this.fullName,
    required this.phoneNumber,
    required this.profileImage,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [
        id,
        email,
        passwordHash,
        fullName,
        phoneNumber,
        profileImage,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

@JsonSerializable()
class Vehicle extends Equatable {
  final int id;
  @JsonKey(name: 'DriverID')
  final String driverId;
  final String type;
  final String make;
  final String model;
  final int year;
  final String color;
  @JsonKey(name: 'LicensePlate')
  final String licensePlate;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const Vehicle({
    required this.id,
    required this.driverId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) => _$VehicleFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleToJson(this);

  @override
  List<Object?> get props => [
        id,
        driverId,
        type,
        make,
        model,
        year,
        color,
        licensePlate,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}
import 'package:equatable/equatable.dart';

part 'driver.g.dart';

@JsonSerializable()
class Driver extends Equatable {
  final String id;
  final String userId;
  final String licenseNumber;
  final String idNumber;
  final int rating;
  final bool isAvailable;
  final bool isVerified;
  final double currentLatitude;
  final double currentLongitude;
  @JsonKey(name: 'LastLocationUpdate')
  final DateTime lastLocationUpdate;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;
  final User user;
  final List<Vehicle> vehicles;

  const Driver({
    required this.id,
    required this.userId,
    required this.licenseNumber,
    required this.idNumber,
    required this.rating,
    required this.isAvailable,
    required this.isVerified,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.lastLocationUpdate,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.user,
    required this.vehicles,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => _$DriverFromJson(json);
  Map<String, dynamic> toJson() => _$DriverToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        licenseNumber,
        idNumber,
        rating,
        isAvailable,
        isVerified,
        currentLatitude,
        currentLongitude,
        lastLocationUpdate,
        createdAt,
        updatedAt,
        deletedAt,
        user,
        vehicles,
      ];
}

@JsonSerializable()
class User extends Equatable {
  final String id;
  final String email;
  @JsonKey(name: 'PasswordHash')
  final String passwordHash;
  @JsonKey(name: 'FullName')
  final String fullName;
  @JsonKey(name: 'PhoneNumber')
  final String phoneNumber;
  @JsonKey(name: 'ProfileImage')
  final String profileImage;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const User({
    required this.id,
    required this.email,
    required this.passwordHash,
    required this.fullName,
    required this.phoneNumber,
    required this.profileImage,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [
        id,
        email,
        passwordHash,
        fullName,
        phoneNumber,
        profileImage,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

@JsonSerializable()
class Vehicle extends Equatable {
  final int id;
  @JsonKey(name: 'DriverID')
  final String driverId;
  final String type;
  final String make;
  final String model;
  final int year;
  final String color;
  @JsonKey(name: 'LicensePlate')
  final String licensePlate;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const Vehicle({
    required this.id,
    required this.driverId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) => _$VehicleFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleToJson(this);

  @override
  List<Object?> get props => [
        id,
        driverId,
        type,
        make,
        model,
        year,
        color,
        licensePlate,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}
import 'package:equatable/equatable.dart';

part 'driver.g.dart';

@JsonSerializable()
class Driver extends Equatable {
  final String id;
  final String userId;
  final String licenseNumber;
  final String idNumber;
  final int rating;
  final bool isAvailable;
  final bool isVerified;
  final double currentLatitude;
  final double currentLongitude;
  @JsonKey(name: 'LastLocationUpdate')
  final DateTime lastLocationUpdate;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;
  final User user;
  final List<Vehicle> vehicles;

  const Driver({
    required this.id,
    required this.userId,
    required this.licenseNumber,
    required this.idNumber,
    required this.rating,
    required this.isAvailable,
    required this.isVerified,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.lastLocationUpdate,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.user,
    required this.vehicles,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => _$DriverFromJson(json);
  Map<String, dynamic> toJson() => _$DriverToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        licenseNumber,
        idNumber,
        rating,
        isAvailable,
        isVerified,
        currentLatitude,
        currentLongitude,
        lastLocationUpdate,
        createdAt,
        updatedAt,
        deletedAt,
        user,
        vehicles,
      ];
}

@JsonSerializable()
class User extends Equatable {
  final String id;
  final String email;
  @JsonKey(name: 'PasswordHash')
  final String passwordHash;
  @JsonKey(name: 'FullName')
  final String fullName;
  @JsonKey(name: 'PhoneNumber')
  final String phoneNumber;
  @JsonKey(name: 'ProfileImage')
  final String profileImage;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const User({
    required this.id,
    required this.email,
    required this.passwordHash,
    required this.fullName,
    required this.phoneNumber,
    required this.profileImage,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [
        id,
        email,
        passwordHash,
        fullName,
        phoneNumber,
        profileImage,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

@JsonSerializable()
class Vehicle extends Equatable {
  final int id;
  @JsonKey(name: 'DriverID')
  final String driverId;
  final String type;
  final String make;
  final String model;
  final int year;
  final String color;
  @JsonKey(name: 'LicensePlate')
  final String licensePlate;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const Vehicle({
    required this.id,
    required this.driverId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) => _$VehicleFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleToJson(this);

  @override
  List<Object?> get props => [
        id,
        driverId,
        type,
        make,
        model,
        year,
        color,
        licensePlate,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}
import 'package:equatable/equatable.dart';

part 'driver.g.dart';

@JsonSerializable()
class Driver extends Equatable {
  final String id;
  final String userId;
  final String licenseNumber;
  final String idNumber;
  final int rating;
  final bool isAvailable;
  final bool isVerified;
  final double currentLatitude;
  final double currentLongitude;
  @JsonKey(name: 'LastLocationUpdate')
  final DateTime lastLocationUpdate;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;
  final User user;
  final List<Vehicle> vehicles;

  const Driver({
    required this.id,
    required this.userId,
    required this.licenseNumber,
    required this.idNumber,
    required this.rating,
    required this.isAvailable,
    required this.isVerified,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.lastLocationUpdate,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
    required this.user,
    required this.vehicles,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => _$DriverFromJson(json);
  Map<String, dynamic> toJson() => _$DriverToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        licenseNumber,
        idNumber,
        rating,
        isAvailable,
        isVerified,
        currentLatitude,
        currentLongitude,
        lastLocationUpdate,
        createdAt,
        updatedAt,
        deletedAt,
        user,
        vehicles,
      ];
}

@JsonSerializable()
class User extends Equatable {
  final String id;
  final String email;
  @JsonKey(name: 'PasswordHash')
  final String passwordHash;
  @JsonKey(name: 'FullName')
  final String fullName;
  @JsonKey(name: 'PhoneNumber')
  final String phoneNumber;
  @JsonKey(name: 'ProfileImage')
  final String profileImage;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const User({
    required this.id,
    required this.email,
    required this.passwordHash,
    required this.fullName,
    required this.phoneNumber,
    required this.profileImage,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [
        id,
        email,
        passwordHash,
        fullName,
        phoneNumber,
        profileImage,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

@JsonSerializable()
class Vehicle extends Equatable {
  final int id;
  @JsonKey(name: 'DriverID')
  final String driverId;
  final String type;
  final String make;
  final String model;
  final int year;
  final String color;
  @JsonKey(name: 'LicensePlate')
  final String licensePlate;
  @JsonKey(name: 'IsActive')
  final bool isActive;
  @JsonKey(name: 'CreatedAt')
  final DateTime createdAt;
  @JsonKey(name: 'UpdatedAt')
  final DateTime updatedAt;
  @JsonKey(name: 'DeletedAt')
  final DateTime? deletedAt;

  const Vehicle({
    required this.id,
    required this.driverId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.deletedAt,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) => _$VehicleFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleToJson(this);

  @override
  List<Object?> get props => [
        id,
        driverId,
        type,
        make,
        model,
        year,
        color,
        licensePlate,
        isActive,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

