/// A class representing a latitude and longitude coordinate
class LatLng {
  /// The latitude in degrees
  final double latitude;
  
  /// The longitude in degrees
  final double longitude;
  
  /// Creates a new [LatLng] object
  const LatLng(this.latitude, this.longitude);
  
  @override
  String toString() => 'LatLng(latitude: $latitude, longitude: $longitude)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LatLng && 
           other.latitude == latitude && 
           other.longitude == longitude;
  }
  
  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode;
}
