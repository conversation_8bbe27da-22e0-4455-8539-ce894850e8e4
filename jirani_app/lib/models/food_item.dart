import 'package:flutter/material.dart';

/// A model class representing a food item
class FoodItem {
  /// Unique identifier for the food item
  final String id;

  /// Name of the food item
  final String name;

  /// Description of the food item
  final String description;

  /// Price of the food item
  final double price;

  /// Image URL of the food item
  final String imageUrl;

  /// Rating of the food item (out of 5)
  final double rating;

  /// Number of reviews for the food item
  final int reviewCount;

  /// Preparation time in minutes
  final int prepTimeMinutes;

  /// Delivery time in minutes
  final int deliveryTimeMinutes;

  /// Category of the food item
  final String category;

  /// Whether the food item is a featured item
  final bool isFeatured;

  /// Whether the food item is popular
  final bool isPopular;

  /// Whether the food item is a bestseller
  final bool isBestseller;

  /// Creates a new instance of [FoodItem]
  const FoodItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.prepTimeMinutes,
    required this.deliveryTimeMinutes,
    required this.category,
    this.isFeatured = false,
    this.isPopular = false,
    this.isBestseller = false,
  });
}

/// A model class representing a food category
class FoodCategory {
  /// Unique identifier for the category
  final String id;

  /// Name of the category
  final String name;

  /// Icon for the category
  final IconData icon;

  /// Image URL for the category
  final String? imageUrl;

  /// Creates a new instance of [FoodCategory]
  const FoodCategory({
    required this.id,
    required this.name,
    required this.icon,
    this.imageUrl,
  });
}

/// A model class representing a restaurant
class Restaurant {
  /// Unique identifier for the restaurant
  final String id;

  /// Name of the restaurant
  final String name;

  /// Logo URL of the restaurant
  final String logoUrl;

  /// Cover image URL of the restaurant
  final String coverImageUrl;

  /// Rating of the restaurant (out of 5)
  final double rating;

  /// Number of reviews for the restaurant
  final int reviewCount;

  /// Address of the restaurant
  final String address;

  /// Delivery time in minutes
  final int deliveryTimeMinutes;

  /// Delivery fee
  final double deliveryFee;

  /// Minimum order amount
  final double minimumOrderAmount;

  /// Categories of food offered by the restaurant
  final List<String> categories;

  /// Whether the restaurant is featured
  final bool isFeatured;

  /// Creates a new instance of [Restaurant]
  const Restaurant({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.coverImageUrl,
    required this.rating,
    required this.reviewCount,
    required this.address,
    required this.deliveryTimeMinutes,
    required this.deliveryFee,
    required this.minimumOrderAmount,
    required this.categories,
    this.isFeatured = false,
  });
}
