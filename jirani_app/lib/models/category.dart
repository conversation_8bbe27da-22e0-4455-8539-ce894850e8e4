import 'package:flutter/material.dart';

/// A category model
class Category {
  /// The unique identifier of the category
  final String id;

  /// The name of the category
  final String name;

  /// The icon of the category
  final IconData icon;

  /// The color of the category
  final Color color;

  /// The image URL of the category
  final String? imageUrl;

  /// Creates a new instance of [Category]
  const Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.imageUrl,
  });

  /// Creates a new instance of [Category] from a JSON object
  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'].toString(),
      name: json['name'] as String,
      icon: IconData(
        json['iconCodePoint'] as int,
        fontFamily: json['iconFontFamily'] as String? ?? 'MaterialIcons',
        fontPackage: json['iconFontPackage'] as String?,
      ),
      color: Color(json['colorValue'] as int),
      imageUrl: json['imageUrl'] as String?,
    );
  }

  /// Converts this [Category] to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily ?? 'MaterialIcons',
      'iconFontPackage': icon.fontPackage,
      'colorValue': color.value,
      'imageUrl': imageUrl,
    };
  }
}
