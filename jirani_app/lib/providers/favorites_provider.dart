import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/service.dart';
import 'service_provider.dart';

/// Provider for favorite service IDs
final favoriteServiceIdsProvider = StateNotifierProvider<FavoriteServiceIdsNotifier, List<String>>((ref) {
  return FavoriteServiceIdsNotifier();
});

/// Notifier for favorite service IDs
class FavoriteServiceIdsNotifier extends StateNotifier<List<String>> {
  FavoriteServiceIdsNotifier() : super([]) {
    _loadFavorites();
  }

  /// Loads favorites from shared preferences
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = prefs.getStringList('favorites') ?? [];
      state = favorites;
    } catch (e) {
      // Handle error
    }
  }

  /// Saves favorites to shared preferences
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('favorites', state);
    } catch (e) {
      // Handle error
    }
  }

  /// Toggles a service as favorite
  void toggleFavorite(String serviceId) {
    if (state.contains(serviceId)) {
      state = state.where((id) => id != serviceId).toList();
    } else {
      state = [...state, serviceId];
    }
    _saveFavorites();
  }

  /// Checks if a service is a favorite
  bool isFavorite(String serviceId) {
    return state.contains(serviceId);
  }

  /// Adds a service to favorites
  void addFavorite(String serviceId) {
    if (!state.contains(serviceId)) {
      state = [...state, serviceId];
      _saveFavorites();
    }
  }

  /// Removes a service from favorites
  void removeFavorite(String serviceId) {
    if (state.contains(serviceId)) {
      state = state.where((id) => id != serviceId).toList();
      _saveFavorites();
    }
  }

  /// Clears all favorites
  void clearFavorites() {
    state = [];
    _saveFavorites();
  }
}

/// Provider for favorite services
final favoriteServicesProvider = Provider<List<Service>>((ref) {
  final favoriteIds = ref.watch(favoriteServiceIdsProvider);
  final servicesAsyncValue = ref.watch(servicesProvider);
  
  return servicesAsyncValue.when(
    data: (services) {
      return services.where((service) => favoriteIds.contains(service.id)).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
