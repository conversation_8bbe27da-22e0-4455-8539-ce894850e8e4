import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/repositories/ride_repository.dart';
import '../data/repositories/rider_repository.dart';

/// Provider for the rider repository
final riderRepositoryProvider = Provider<RiderRepository>((ref) {
  return RiderRepository();
});

/// Provider for the ride repository
final rideRepositoryProvider = Provider<RideRepository>((ref) {
  return RideRepository();
});
