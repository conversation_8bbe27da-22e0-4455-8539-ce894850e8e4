import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category.dart';
import '../services/mock_data_service.dart';

/// Provider for all categories
final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getCategories();
});

/// Provider for selected category
final selectedCategoryProvider = StateProvider<String?>((ref) => null);

/// Provider for filtered categories
final filteredCategoriesProvider = Provider<List<Category>>((ref) {
  final categoriesAsyncValue = ref.watch(categoriesProvider);
  final selectedCategory = ref.watch(selectedCategoryProvider);
  
  return categoriesAsyncValue.when(
    data: (categories) {
      if (selectedCategory == null) {
        return categories;
      }
      
      return categories.where((category) => category.id == selectedCategory).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
