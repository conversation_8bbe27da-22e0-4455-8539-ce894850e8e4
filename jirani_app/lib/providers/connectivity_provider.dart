import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum ConnectivityStatus {
  connected,
  disconnected,
  unknown, // Initial state
}

final connectivityStatusProvider = StateNotifierProvider<ConnectivityStatusNotifier, ConnectivityStatus>((ref) {
  return ConnectivityStatusNotifier();
});

class ConnectivityStatusNotifier extends StateNotifier<ConnectivityStatus> {
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  final Connectivity _connectivity = Connectivity();

  ConnectivityStatusNotifier() : super(ConnectivityStatus.unknown) {
    _init();
  }

  Future<void> _init() async {
    await _updateConnectionStatus(await _connectivity.checkConnectivity());
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    // If the list contains ConnectivityResult.none, it means disconnected.
    // Otherwise, if it contains mobile, wifi, ethernet, etc., it's connected.
    // The list can contain multiple results if multiple connections are active (e.g. wifi and mobile data).
    if (result.contains(ConnectivityResult.none)) {
      state = ConnectivityStatus.disconnected;
    } else if (result.any((r) => r == ConnectivityResult.mobile || r == ConnectivityResult.wifi || r == ConnectivityResult.ethernet || r == ConnectivityResult.vpn)) {
      state = ConnectivityStatus.connected;
    } else {
      // This case should ideally not be hit if 'none' is properly handled,
      // but as a fallback, consider it disconnected or unknown.
      state = ConnectivityStatus.disconnected; 
    }
  }

  // Method to manually check connectivity if needed
  Future<void> checkConnectivity() async {
     await _updateConnectionStatus(await _connectivity.checkConnectivity());
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
