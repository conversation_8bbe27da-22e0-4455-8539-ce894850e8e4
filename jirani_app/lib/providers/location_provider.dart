import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/lat_lng.dart';
import '../services/location_service.dart';

/// Provider for the location service
final locationServiceProvider = Provider<LocationService>((ref) {
  final service = LocationService();
  ref.onDispose(() {
    service.dispose();
  });
  return service;
});

/// Provider for the user's current location
final userLocationProvider = StateNotifierProvider<UserLocationNotifier, AsyncValue<LatLng?>>((ref) {
  return UserLocationNotifier(ref);
});

/// Notifier for the user's current location
class UserLocationNotifier extends StateNotifier<AsyncValue<LatLng?>> {
  final Ref _ref;
  LocationService? _locationService;

  /// Creates a new instance of [UserLocationNotifier]
  UserLocationNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initLocationService();
  }

  /// Initializes the location service and gets the user's current location
  Future<void> _initLocationService() async {
    try {
      _locationService = _ref.read(locationServiceProvider);

      // Initialize the location service
      await _locationService!.initialize().catchError((e) {
        if (kDebugMode) {
          print('Error initializing location service: $e');
        }
        // Fallback to default location
        state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
        return;
      });

      try {
        // Get the current position
        final position = await _locationService!.getCurrentPosition();

        if (position.latitude != null && position.longitude != null) {
          state = AsyncValue.data(LatLng(
            position.latitude!,
            position.longitude!,
          ));
        }

        // Start listening for location updates
        _locationService!.startLocationTracking(
          onLocationUpdate: (position) {
            if (position.latitude != null && position.longitude != null) {
              state = AsyncValue.data(LatLng(
                position.latitude!,
                position.longitude!,
              ));
            }
          },
        );
      } catch (e) {
        if (kDebugMode) {
          print('Error getting current position: $e');
        }
        // Fallback to default location
        state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in _initLocationService: $e');
      }
      // Provide a fallback location even in case of errors
      state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
    }
  }

  /// Refreshes the user's current location
  Future<void> refreshLocation() async {
    try {
      state = const AsyncValue.loading();

      if (_locationService != null) {
        try {
          final position = await _locationService!.getCurrentPosition();
          if (position.latitude != null && position.longitude != null) {
            state = AsyncValue.data(LatLng(
              position.latitude!,
              position.longitude!,
            ));
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error getting current position: $e');
          }
          // Fallback to default location
          state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
        }
      } else {
        // Fallback to default location
        state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing location: $e');
      }
      // Provide a fallback location even in case of errors
      state = const AsyncValue.data(LatLng(-1.2921, 36.8219)); // Nairobi, Kenya
    }
  }

  @override
  void dispose() {
    _locationService?.stopLocationTracking();
    super.dispose();
  }
}

/// Provider for the selected location on the map
final selectedLocationProvider = StateNotifierProvider<SelectedLocationNotifier, LatLng?>((ref) {
  return SelectedLocationNotifier();
});

/// Notifier for the selected location on the map
class SelectedLocationNotifier extends StateNotifier<LatLng?> {
  /// Creates a new instance of [SelectedLocationNotifier]
  SelectedLocationNotifier() : super(null);

  /// Sets the selected location
  void setLocation(LatLng location) {
    state = location;
  }

  /// Clears the selected location
  void clearLocation() {
    state = null;
  }
}

/// Provider for the pickup location
final pickupLocationProvider = StateNotifierProvider<PickupLocationNotifier, LatLng?>((ref) {
  return PickupLocationNotifier(ref);
});

/// Notifier for the pickup location
class PickupLocationNotifier extends StateNotifier<LatLng?> {
  final Ref _ref;

  /// Creates a new instance of [PickupLocationNotifier]
  PickupLocationNotifier(this._ref) : super(null) {
    // Initialize with the user's current location when available
    _ref.listen(userLocationProvider, (previous, next) {
      if (next is AsyncData && next.value != null && state == null) {
        state = next.value;
      }
    });
  }

  /// Sets the pickup location
  void setLocation(LatLng location) {
    state = location;
  }

  /// Clears the pickup location
  void clearLocation() {
    state = null;
  }

  /// Uses the user's current location as the pickup location
  void useCurrentLocation() {
    final userLocation = _ref.read(userLocationProvider);
    if (userLocation is AsyncData && userLocation.value != null) {
      state = userLocation.value;
    }
  }
}

/// Provider for the dropoff location
final dropoffLocationProvider = StateNotifierProvider<DropoffLocationNotifier, LatLng?>((ref) {
  return DropoffLocationNotifier();
});

/// Notifier for the dropoff location
class DropoffLocationNotifier extends StateNotifier<LatLng?> {
  /// Creates a new instance of [DropoffLocationNotifier]
  DropoffLocationNotifier() : super(null);

  /// Sets the dropoff location
  void setLocation(LatLng location) {
    state = location;
  }

  /// Clears the dropoff location
  void clearLocation() {
    state = null;
  }
}
