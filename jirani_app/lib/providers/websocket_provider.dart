import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/websocket_message.dart';
import '../services/websocket_service.dart';
import '../services/logging_service.dart';

/// WebSocket connection state
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket state model
class WebSocketState {
  final WebSocketConnectionState connectionState;
  final String? error;
  final int connectedUsers;

  const WebSocketState({
    required this.connectionState,
    this.error,
    this.connectedUsers = 0,
  });

  WebSocketState copyWith({
    WebSocketConnectionState? connectionState,
    String? error,
    int? connectedUsers,
  }) {
    return WebSocketState(
      connectionState: connectionState ?? this.connectionState,
      error: error ?? this.error,
      connectedUsers: connectedUsers ?? this.connectedUsers,
    );
  }
}

/// WebSocket provider
class WebSocketNotifier extends StateNotifier<WebSocketState> {
  WebSocketNotifier()
      : super(const WebSocketState(
            connectionState: WebSocketConnectionState.disconnected)) {
    _initialize();
  }

  final WebSocketService _webSocketService = WebSocketService();
  StreamSubscription<WebSocketMessage>? _messageSubscription;

  /// Initialize WebSocket connection
  void _initialize() {
    _messageSubscription = _webSocketService.messageStream.listen(
      _handleMessage,
      onError: (error) {
        LoggingService.e('WebSocket Provider: Error', error: error);
        state = state.copyWith(
          connectionState: WebSocketConnectionState.error,
          error: error.toString(),
        );
      },
    );
  }

  /// Connect to WebSocket
  Future<void> connect() async {
    if (state.connectionState == WebSocketConnectionState.connected) return;

    state =
        state.copyWith(connectionState: WebSocketConnectionState.connecting);

    try {
      await _webSocketService.connect();
      state = state.copyWith(
        connectionState: WebSocketConnectionState.connected,
        error: null,
      );
    } catch (e) {
      LoggingService.e('WebSocket Provider: Connection failed', error: e);
      state = state.copyWith(
        connectionState: WebSocketConnectionState.error,
        error: e.toString(),
      );
    }
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    await _webSocketService.disconnect();
    state =
        state.copyWith(connectionState: WebSocketConnectionState.disconnected);
  }

  /// Send a message
  void sendMessage(WebSocketMessage message) {
    _webSocketService.sendMessage(message);
  }

  /// Send location update
  void sendLocationUpdate(double latitude, double longitude, {String? rideId}) {
    _webSocketService.sendLocationUpdate(latitude, longitude, rideId: rideId);
  }

  /// Send chat message
  void sendChatMessage(String rideId, String message, String toUserId) {
    _webSocketService.sendChatMessage(rideId, message, toUserId);
  }

  /// Handle incoming messages
  void _handleMessage(WebSocketMessage message) {
    LoggingService.i('WebSocket Provider: Received message: ${message.type}');

    switch (message.type) {
      case WebSocketMessageType.heartbeat:
        // Handle heartbeat response
        break;
      case WebSocketMessageType.error:
        state = state.copyWith(
          connectionState: WebSocketConnectionState.error,
          error: message.data['message'] ?? 'Unknown error',
        );
        break;
      default:
        // Other message types are handled by specific providers
        break;
    }
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    _webSocketService.dispose();
    super.dispose();
  }
}

/// WebSocket provider
final webSocketProvider =
    StateNotifierProvider<WebSocketNotifier, WebSocketState>((ref) {
  return WebSocketNotifier();
});

/// WebSocket message stream provider
final webSocketMessageProvider = StreamProvider<WebSocketMessage>((ref) {
  final webSocketService = WebSocketService();
  return webSocketService.messageStream;
});

/// Connection status provider
final webSocketConnectionProvider = Provider<bool>((ref) {
  final state = ref.watch(webSocketProvider);
  return state.connectionState == WebSocketConnectionState.connected;
});

/// Specific message type stream providers
final rideStatusUpdateProvider = StreamProvider<WebSocketMessage>((ref) {
  final webSocketService = WebSocketService();
  return webSocketService.messageStream.where(
    (message) => message.type == WebSocketMessageType.rideStatusUpdate,
  );
});

final driverLocationProvider = StreamProvider<WebSocketMessage>((ref) {
  final webSocketService = WebSocketService();
  return webSocketService.messageStream.where(
    (message) => message.type == WebSocketMessageType.driverLocation,
  );
});

final chatMessageProvider = StreamProvider<WebSocketMessage>((ref) {
  final webSocketService = WebSocketService();
  return webSocketService.messageStream.where(
    (message) => message.type == WebSocketMessageType.chat,
  );
});

final notificationProvider = StreamProvider<WebSocketMessage>((ref) {
  final webSocketService = WebSocketService();
  return webSocketService.messageStream.where(
    (message) => message.type == WebSocketMessageType.notification,
  );
});

/// Auto-connect WebSocket when user is authenticated
final autoConnectWebSocketProvider = Provider<void>((ref) {
  // This provider will automatically connect WebSocket when needed
  // It can be watched by screens that need real-time functionality
  ref.listen(webSocketProvider, (previous, next) {
    if (next.connectionState == WebSocketConnectionState.disconnected) {
      // Auto-reconnect logic can be added here
    }
  });
});
