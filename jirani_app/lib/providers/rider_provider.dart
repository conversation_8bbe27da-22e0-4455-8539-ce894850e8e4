import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/rider.dart';
import '../services/logging_service.dart';
import '../services/network_bound_resource.dart';
import 'location_provider.dart';
import 'repository_providers.dart';

/// Provider for nearby riders
final nearbyRidersProvider =
    StateNotifierProvider<NearbyRidersNotifier, AsyncValue<List<Rider>>>((ref) {
  return NearbyRidersNotifier(ref);
});

/// Notifier for nearby riders
class NearbyRidersNotifier extends StateNotifier<AsyncValue<List<Rider>>> {
  /// Reference to the provider
  final Ref _ref;

  /// Creates a new instance of [NearbyRidersNotifier]
  NearbyRidersNotifier(this._ref) : super(const AsyncValue.loading()) {
    _fetchNearbyRiders();
  }

  /// Fetch nearby riders
  Future<void> _fetchNearbyRiders() async {
    final userLocationAsync = _ref.read(userLocationProvider);

    // Return empty list if user location is not available
    if (userLocationAsync is! AsyncData || userLocationAsync.value == null) {
      state = const AsyncValue.data([]);
      return;
    }

    final userLocation = userLocationAsync.value!;
    final repository = _ref.read(riderRepositoryProvider);

    try {
      // Listen to the stream of nearby riders
      repository
          .getNearbyRiders(
        userLocation: userLocation,
        radiusInKm: 5.0,
      )
          .listen(
        (resource) {
          switch (resource.status) {
            case ResourceStatus.loadingFromCache:
              // Keep current state if we have data, otherwise show loading
              if (state is! AsyncData ||
                  (state as AsyncData<List<Rider>>).value.isEmpty) {
                state = const AsyncValue.loading();
              }
              break;
            case ResourceStatus.loadingFromNetwork:
              // If we have cached data, use it while loading from network
              if (resource.data != null) {
                state = AsyncValue.data(resource.data!);
              } else if (state is! AsyncData) {
                state = const AsyncValue.loading();
              }
              break;
            case ResourceStatus.success:
              state = AsyncValue.data(resource.data!);
              break;
            case ResourceStatus.error:
              // If we have data (from cache), keep it and show error
              if (resource.data != null) {
                state = AsyncValue.data(resource.data!);
                LoggingService.e(
                  'Error fetching nearby riders',
                  error: resource.error,
                  stackTrace: resource.stackTrace,
                  tag: 'NearbyRidersNotifier',
                );
              } else {
                state = AsyncValue.error(
                  resource.error!,
                  resource.stackTrace!,
                );
              }
              break;
          }
        },
        onError: (error, stackTrace) {
          state = AsyncValue.error(error, stackTrace);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh nearby riders
  Future<void> refresh() async {
    final userLocationAsync = _ref.read(userLocationProvider);

    // Return if user location is not available
    if (userLocationAsync is! AsyncData || userLocationAsync.value == null) {
      return;
    }

    await _fetchNearbyRiders();
  }
}

/// Provider for a specific rider
final riderProvider =
    FutureProvider.family<Rider?, String>((ref, riderId) async {
  final repository = ref.read(riderRepositoryProvider);
  return repository.getRiderById(riderId);
});

/// Provider for the selected rider
final selectedRiderProvider =
    StateNotifierProvider<SelectedRiderNotifier, Rider?>((ref) {
  return SelectedRiderNotifier();
});

/// Notifier for the selected rider
class SelectedRiderNotifier extends StateNotifier<Rider?> {
  /// Creates a new instance of [SelectedRiderNotifier]
  SelectedRiderNotifier() : super(null);

  /// Sets the selected rider
  void selectRider(Rider rider) {
    state = rider;
  }

  /// Clears the selected rider
  void clearSelection() {
    state = null;
  }
}

/// Provider for the ETA of the selected rider to the pickup location
final riderEtaProvider = FutureProvider.autoDispose<int>((ref) async {
  final selectedRider = ref.watch(selectedRiderProvider);
  final pickupLocation = ref.watch(pickupLocationProvider);
  final repository = ref.read(riderRepositoryProvider);

  // Return 0 if either the rider or pickup location is not available
  if (selectedRider == null || pickupLocation == null) {
    return 0;
  }

  try {
    // Calculate ETA using the repository
    final eta = await repository.calculateEta(
      riderLocation: selectedRider.location,
      destination: pickupLocation,
    );

    return eta ?? 0;
  } catch (e) {
    LoggingService.e(
      'Error calculating ETA',
      error: e,
      tag: 'riderEtaProvider',
    );
    return 0;
  }
});

/// Provider for available riders (online and not busy)
final availableRidersProvider = Provider.autoDispose<List<Rider>>((ref) {
  final nearbyRidersAsync = ref.watch(nearbyRidersProvider);

  return nearbyRidersAsync.when(
    data: (riders) =>
        riders.where((rider) => rider.status == RiderStatus.available).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for busy riders (online but busy)
final busyRidersProvider = Provider.autoDispose<List<Rider>>((ref) {
  final nearbyRidersAsync = ref.watch(nearbyRidersProvider);

  return nearbyRidersAsync.when(
    data: (riders) =>
        riders.where((rider) => rider.status == RiderStatus.busy).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for offline riders
final offlineRidersProvider = Provider.autoDispose<List<Rider>>((ref) {
  final nearbyRidersAsync = ref.watch(nearbyRidersProvider);

  return nearbyRidersAsync.when(
    data: (riders) =>
        riders.where((rider) => rider.status == RiderStatus.offline).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});
