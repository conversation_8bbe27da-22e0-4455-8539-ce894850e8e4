import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user.dart';

class UserProvider with ChangeNotifier {
  User? _user;
  List<String> _favoriteProviderIds = [];

  User? get user => _user;
  List<String> get favoriteProviderIds => _favoriteProviderIds;

  bool get isLoggedIn => _user != null;

  UserProvider() {
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user');
      final favoritesData = prefs.getStringList('favorites') ?? [];

      if (userData != null) {
        _user = User.fromJson(json.decode(userData));
        _favoriteProviderIds = _user!.favoriteProviderIds;
      } else {
        _favoriteProviderIds = favoritesData;
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading user data: $e');
      }
    }
  }

  Future<void> _saveUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_user != null) {
        await prefs.setString('user', json.encode(_user!.toJson()));
      } else {
        await prefs.remove('user');
      }
      await prefs.setStringList('favorites', _favoriteProviderIds);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving user data: $e');
      }
    }
  }

  Future<void> login(User user) async {
    _user = user;
    if (_user!.favoriteProviderIds.isNotEmpty) {
      _favoriteProviderIds = _user!.favoriteProviderIds;
    }
    await _saveUserData();
    notifyListeners();
  }

  Future<void> logout() async {
    _user = null;
    await _saveUserData();
    notifyListeners();
  }

  Future<void> updateUser(User updatedUser) async {
    _user = updatedUser;
    await _saveUserData();
    notifyListeners();
  }

  Future<void> toggleFavorite(String providerId) async {
    if (_favoriteProviderIds.contains(providerId)) {
      _favoriteProviderIds.remove(providerId);
    } else {
      _favoriteProviderIds.add(providerId);
    }

    if (_user != null) {
      _user = _user!.copyWith(favoriteProviderIds: _favoriteProviderIds);
    }

    await _saveUserData();
    notifyListeners();
  }

  bool isFavorite(String providerId) {
    return _favoriteProviderIds.contains(providerId);
  }
}
