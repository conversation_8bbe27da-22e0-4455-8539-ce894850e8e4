import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';

/// Provider for the current user ID
final currentUserIdProvider = StateProvider<String>((ref) {
  // In a real app, this would come from authentication
  return 'user_1';
});

/// Provider for whether the user is authenticated
final isAuthenticatedProvider = StateProvider<bool>((ref) {
  // In a real app, this would check if the user is authenticated
  return true;
});

/// Provider for the current user
final currentUserProvider = Provider<User>((ref) {
  // In a real app, this would fetch the user from a repository
  return User(
    id: 'user_1',
    name: '<PERSON>',
    phoneNumber: '+254712345678',
    email: '<EMAIL>',
    profileImageUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
  );
});
