import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Theme mode enum
enum ThemeMode {
  /// Light theme
  light,
  
  /// Dark theme
  dark,
  
  /// System theme
  system,
}

/// Provider for theme mode
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

/// Notifier for theme mode
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  /// Loads theme mode from shared preferences
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeMode = prefs.getString('themeMode');
      
      if (themeMode != null) {
        state = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == themeMode,
          orElse: () => ThemeMode.system,
        );
      }
    } catch (e) {
      // Handle error
    }
  }

  /// Saves theme mode to shared preferences
  Future<void> _saveThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('themeMode', state.toString());
    } catch (e) {
      // Handle error
    }
  }

  /// Sets the theme mode
  void setThemeMode(ThemeMode mode) {
    state = mode;
    _saveThemeMode();
  }

  /// Toggles between light and dark theme
  void toggleTheme() {
    if (state == ThemeMode.light) {
      state = ThemeMode.dark;
    } else if (state == ThemeMode.dark) {
      state = ThemeMode.light;
    } else {
      // If system, check the current brightness
      final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      state = brightness == Brightness.light ? ThemeMode.dark : ThemeMode.light;
    }
    _saveThemeMode();
  }
}

/// Provider for current brightness
final brightnessProvider = Provider<Brightness>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  
  if (themeMode == ThemeMode.light) {
    return Brightness.light;
  } else if (themeMode == ThemeMode.dark) {
    return Brightness.dark;
  } else {
    return WidgetsBinding.instance.platformDispatcher.platformBrightness;
  }
});
