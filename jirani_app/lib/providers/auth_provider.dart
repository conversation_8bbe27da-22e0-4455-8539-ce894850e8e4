import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

/// Authentication state
enum AuthState {
  /// Initial state
  initial,
  
  /// Loading state
  loading,
  
  /// Authenticated state
  authenticated,
  
  /// Unauthenticated state
  unauthenticated,
  
  /// Error state
  error,
}

/// Authentication state notifier
class AuthNotifier extends StateNotifier<AuthState> {
  /// Auth service
  final AuthService _authService;
  
  /// User data
  Map<String, dynamic>? _userData;
  
  /// Error message
  String? _errorMessage;
  
  /// Creates a new auth notifier
  AuthNotifier(this._authService) : super(AuthState.initial) {
    _initialize();
  }
  
  /// Initialize the auth state
  Future<void> _initialize() async {
    state = AuthState.loading;
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        _userData = await _authService.getUserProfile();
        state = AuthState.authenticated;
      } else {
        state = AuthState.unauthenticated;
      }
    } catch (e) {
      _errorMessage = e.toString();
      state = AuthState.error;
    }
  }
  
  /// Register a new user
  Future<void> register({
    required String fullName,
    required String email,
    required String password,
    required String phoneNumber,
  }) async {
    state = AuthState.loading;
    try {
      final data = await _authService.register(
        fullName: fullName,
        email: email,
        password: password,
        phoneNumber: phoneNumber,
      );
      _userData = data['user'];
      state = AuthState.authenticated;
    } catch (e) {
      _errorMessage = e.toString();
      state = AuthState.error;
    }
  }
  
  /// Login a user
  Future<void> login({
    required String email,
    required String password,
  }) async {
    state = AuthState.loading;
    try {
      final data = await _authService.login(
        email: email,
        password: password,
      );
      _userData = data['user'];
      state = AuthState.authenticated;
    } catch (e) {
      _errorMessage = e.toString();
      state = AuthState.error;
    }
  }
  
  /// Forgot password
  Future<void> forgotPassword({required String email}) async {
    state = AuthState.loading;
    try {
      await _authService.forgotPassword(email: email);
      state = AuthState.unauthenticated;
    } catch (e) {
      _errorMessage = e.toString();
      state = AuthState.error;
    }
  }
  
  /// Logout the current user
  Future<void> logout() async {
    state = AuthState.loading;
    try {
      await _authService.logout();
      _userData = null;
      state = AuthState.unauthenticated;
    } catch (e) {
      _errorMessage = e.toString();
      state = AuthState.error;
    }
  }
  
  /// Get the current user data
  Map<String, dynamic>? get userData => _userData;
  
  /// Get the error message
  String? get errorMessage => _errorMessage;

  /// Register the current user as a driver
  Future<void> registerAsDriver({
    required String licenseNumber,
    required String idNumber,
  }) async {
    // No state change to loading here, as this is usually called from a specific screen
    // that handles its own loading state. If global loading is desired, set state = AuthState.loading.
    try {
      await _authService.registerAsDriver(
        licenseNumber: licenseNumber,
        idNumber: idNumber,
      );
      // After successful driver registration, ideally, we would refresh user data
      // to get updated roles/status. For now, this is a separate step.
      // Consider adding a flag or re-fetching user profile if backend supports it.
      // For example: await _initialize(); // This would re-fetch and update state.
    } catch (e) {
      _errorMessage = e.toString();
      // Potentially set state to AuthState.error or let the calling screen handle it.
      // For now, just rethrow to be handled by the UI.
      rethrow;
    }
  }
}

// Removed redundant authServiceProvider as it's defined in auth_service.dart

/// Auth state provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  // AuthNotifier now correctly uses the authServiceProvider from auth_service.dart
  return AuthNotifier(ref.watch(authServiceProvider)); // Uses the imported provider
});

/// User data provider
final userDataProvider = Provider<Map<String, dynamic>?>((ref) {
  final authNotifier = ref.watch(authProvider.notifier);
  return authNotifier.userData;
});

/// Error message provider
final authErrorProvider = Provider<String?>((ref) {
  final authNotifier = ref.watch(authProvider.notifier);
  return authNotifier.errorMessage;
});
