import 'dart:math' as math;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:location/location.dart' as loc;
import '../services/enhanced_location_service.dart';

/// Enhanced Location Service Provider
/// PRP-LOCATION-ENH-001 Phase 2 Implementation
final enhancedLocationServiceProvider =
    Provider<EnhancedLocationService>((ref) {
  return EnhancedLocationService();
});

/// Current location provider with enhanced accuracy
final currentLocationProvider = StreamProvider<loc.LocationData>((ref) {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return locationService.locationStream;
});

/// Location history provider
final locationHistoryProvider = StreamProvider<List<SavedLocation>>((ref) {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return locationService.locationHistoryStream;
});

/// Location history state provider for manual refresh
final locationHistoryStateProvider = StateNotifierProvider<
    LocationHistoryNotifier, AsyncValue<List<SavedLocation>>>((ref) {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return LocationHistoryNotifier(locationService);
});

/// Pickup location provider
final pickupLocationProvider = StateProvider<SavedLocation?>((ref) => null);

/// Dropoff location provider
final dropoffLocationProvider = StateProvider<SavedLocation?>((ref) => null);

/// Current user location provider (single location, not stream)
final userLocationProvider = FutureProvider<loc.LocationData>((ref) async {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return await locationService.getCurrentLocation();
});

/// Location accuracy mode provider
final locationAccuracyModeProvider = StateProvider<LocationAccuracyMode>((ref) {
  return LocationAccuracyMode.high; // Default to high accuracy
});

/// Location tracking state provider
final locationTrackingProvider =
    StateNotifierProvider<LocationTrackingNotifier, bool>((ref) {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  final accuracyMode = ref.watch(locationAccuracyModeProvider);
  return LocationTrackingNotifier(locationService, accuracyMode);
});

/// Location search query provider
final locationSearchQueryProvider = StateProvider<String>((ref) => '');

/// Filtered location history provider based on search query
final filteredLocationHistoryProvider = Provider<List<SavedLocation>>((ref) {
  final locationHistory = ref.watch(locationHistoryProvider);
  final searchQuery = ref.watch(locationSearchQueryProvider);

  return locationHistory.when(
    data: (locations) {
      if (searchQuery.isEmpty) return locations;

      final locationService = ref.watch(enhancedLocationServiceProvider);
      return locationService.searchLocationHistory(searchQuery);
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Location history by type provider
final locationHistoryByTypeProvider =
    FutureProvider.family<List<SavedLocation>, LocationType>((ref, type) async {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return await locationService.getLocationHistoryByType(type);
});

/// Favorite locations provider
final favoriteLocationsProvider =
    Provider<AsyncValue<List<SavedLocation>>>((ref) {
  return ref.watch(locationHistoryByTypeProvider(LocationType.favorite));
});

/// Recent locations provider (last 5 used)
final recentLocationsProvider = Provider<List<SavedLocation>>((ref) {
  final locationHistory = ref.watch(locationHistoryProvider);

  return locationHistory.when(
    data: (locations) {
      // Sort by last used and take first 5
      final sortedLocations = List<SavedLocation>.from(locations);
      sortedLocations.sort((a, b) => b.lastUsedAt.compareTo(a.lastUsedAt));
      return sortedLocations.take(5).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Location History State Notifier
class LocationHistoryNotifier
    extends StateNotifier<AsyncValue<List<SavedLocation>>> {
  final EnhancedLocationService _locationService;

  LocationHistoryNotifier(this._locationService)
      : super(const AsyncValue.loading()) {
    _loadLocationHistory();
  }

  /// Load location history
  Future<void> _loadLocationHistory() async {
    try {
      state = const AsyncValue.loading();
      final locations = await _locationService.getLocationHistory();
      state = AsyncValue.data(locations);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh location history
  Future<void> refresh() async {
    try {
      state = const AsyncValue.loading();
      final locations =
          await _locationService.getLocationHistory(forceRefresh: true);
      state = AsyncValue.data(locations);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Save location to history
  Future<void> saveLocation({
    required double latitude,
    required double longitude,
    required String address,
    required LocationType locationType,
  }) async {
    try {
      await _locationService.saveLocationToHistory(
        latitude: latitude,
        longitude: longitude,
        address: address,
        locationType: locationType,
      );

      // Refresh the list
      await _loadLocationHistory();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete location from history
  Future<void> deleteLocation(String locationId) async {
    try {
      await _locationService.deleteLocationFromHistory(locationId);

      // Refresh the list
      await _loadLocationHistory();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Location Tracking State Notifier
class LocationTrackingNotifier extends StateNotifier<bool> {
  final EnhancedLocationService _locationService;
  final LocationAccuracyMode _accuracyMode;

  LocationTrackingNotifier(this._locationService, this._accuracyMode)
      : super(false);

  /// Start location tracking
  Future<void> startTracking() async {
    try {
      await _locationService.startLocationTracking(
        accuracyMode: _accuracyMode,
        onLocationUpdate: (locationData) {
          // Handle location updates if needed
        },
      );
      state = true;
    } catch (error) {
      // Handle error
      state = false;
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    try {
      await _locationService.stopLocationTracking();
      state = false;
    } catch (error) {
      // Handle error
    }
  }
}

/// Location permission provider
final locationPermissionProvider = FutureProvider<bool>((ref) async {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  try {
    await locationService.initialize();
    return true;
  } catch (e) {
    return false;
  }
});

/// Address from coordinates provider
final addressFromCoordinatesProvider =
    FutureProvider.family<String, ({double lat, double lng})>(
        (ref, coords) async {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  return await locationService.getAddressFromCoordinates(
      coords.lat, coords.lng);
});

/// Distance calculation provider
final distanceCalculationProvider = Provider.family<double?,
    ({double lat1, double lng1, double lat2, double lng2})>((ref, coords) {
  // Simple distance calculation using Haversine formula
  const double earthRadius = 6371; // Earth's radius in kilometers

  final lat1Rad = coords.lat1 * (3.14159265359 / 180);
  final lat2Rad = coords.lat2 * (3.14159265359 / 180);
  final deltaLatRad = (coords.lat2 - coords.lat1) * (3.14159265359 / 180);
  final deltaLngRad = (coords.lng2 - coords.lng1) * (3.14159265359 / 180);

  final a = (deltaLatRad / 2).sin() * (deltaLatRad / 2).sin() +
      lat1Rad.cos() *
          lat2Rad.cos() *
          (deltaLngRad / 2).sin() *
          (deltaLngRad / 2).sin();
  final c = 2 * (a.sqrt()).atan2((1 - a).sqrt());

  return earthRadius * c;
});

/// Location initialization provider
final locationInitializationProvider = FutureProvider<bool>((ref) async {
  final locationService = ref.watch(enhancedLocationServiceProvider);
  try {
    await locationService.initialize();
    return true;
  } catch (e) {
    return false;
  }
});

/// Auto-save location provider - saves frequently used locations automatically
final autoSaveLocationProvider = Provider<void>((ref) {
  // This provider can be used to automatically save locations
  // when they are used frequently (e.g., pickup/dropoff selections)

  final locationHistoryNotifier =
      ref.watch(locationHistoryStateProvider.notifier);

  // Auto-save logic can be implemented here
  // For example, save pickup location as 'pickup' type when selected
  ref.listen(pickupLocationProvider, (previous, next) {
    if (next != null && previous != next) {
      // Auto-save pickup location
      locationHistoryNotifier.saveLocation(
        latitude: next.latitude,
        longitude: next.longitude,
        address: next.address,
        locationType: LocationType.pickup,
      );
    }
  });

  // Auto-save dropoff location
  ref.listen(dropoffLocationProvider, (previous, next) {
    if (next != null && previous != next) {
      // Auto-save dropoff location
      locationHistoryNotifier.saveLocation(
        latitude: next.latitude,
        longitude: next.longitude,
        address: next.address,
        locationType: LocationType.destination,
      );
    }
  });
});

/// Extension for math functions
extension MathExtension on double {
  double sin() => math.sin(this);
  double cos() => math.cos(this);
  double sqrt() => math.sqrt(this);
  double atan2(double other) => math.atan2(this, other);
}
