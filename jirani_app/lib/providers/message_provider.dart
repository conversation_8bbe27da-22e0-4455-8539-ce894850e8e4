import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum MessageType {
  success,
  error,
  info,
  warning,
}

class AppMessage {
  final String text;
  final MessageType type;
  final Duration duration;

  AppMessage({
    required this.text,
    required this.type,
    this.duration = const Duration(seconds: 4),
  });

  Color get backgroundColor {
    switch (type) {
      case MessageType.success:
        return Colors.green.shade600;
      case MessageType.error:
        return Colors.red.shade600;
      case MessageType.info:
        return Colors.blue.shade600;
      case MessageType.warning:
        return Colors.orange.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  IconData get icon {
     switch (type) {
      case MessageType.success:
        return Icons.check_circle_outline;
      case MessageType.error:
        return Icons.error_outline;
      case MessageType.info:
        return Icons.info_outline;
      case MessageType.warning:
        return Icons.warning_amber_outlined;
      default:
        return Icons.info_outline;
    }
  }
}

final messageProvider = StateNotifierProvider<MessageNotifier, AppMessage?>((ref) {
  return MessageNotifier();
});

class MessageNotifier extends StateNotifier<AppMessage?> {
  MessageNotifier() : super(null);

  void showMessage({
    required String text,
    required MessageType type,
    Duration? duration,
  }) {
    state = AppMessage(
      text: text,
      type: type,
      duration: duration ?? const Duration(seconds: 4),
    );
  }

  void clearMessage() {
    state = null;
  }
}
