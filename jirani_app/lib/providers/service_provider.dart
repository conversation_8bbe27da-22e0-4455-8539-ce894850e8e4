import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/service.dart';
import '../services/mock_data_service.dart';

/// Provider for all services
final servicesProvider = FutureProvider<List<Service>>((ref) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getServices();
});

/// Provider for featured services
final featuredServicesProvider = FutureProvider<List<Service>>((ref) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getFeaturedServices();
});

/// Provider for popular services
final popularServicesProvider = FutureProvider<List<Service>>((ref) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getPopularServices();
});

/// Provider for services by category
final servicesByCategoryProvider = FutureProvider.family<List<Service>, String>((ref, category) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getServicesByCategory(category);
});

/// Provider for a service by ID
final serviceByIdProvider = FutureProvider.family<Service?, String>((ref, id) async {
  // Simulate network delay
  await Future.delayed(const Duration(seconds: 1));
  return MockDataService.getServiceById(id);
});

/// Provider for service search
final serviceSearchProvider = StateProvider<String>((ref) => '');

/// Provider for filtered services
final filteredServicesProvider = Provider<List<Service>>((ref) {
  final searchQuery = ref.watch(serviceSearchProvider);
  final servicesAsyncValue = ref.watch(servicesProvider);
  
  return servicesAsyncValue.when(
    data: (services) {
      if (searchQuery.isEmpty) {
        return services;
      }
      
      final lowercaseQuery = searchQuery.toLowerCase();
      return services.where((service) {
        return service.name.toLowerCase().contains(lowercaseQuery) ||
            service.description.toLowerCase().contains(lowercaseQuery) ||
            service.category.toLowerCase().contains(lowercaseQuery) ||
            service.provider.name.toLowerCase().contains(lowercaseQuery);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
