import 'package:flutter/foundation.dart';
import '../models/service.dart';
import '../services/mock_data_service.dart';

class ProviderProvider with ChangeNotifier {
  List<ServiceProvider> _providers = [];
  bool _isLoading = false;
  String? _error;

  List<ServiceProvider> get providers => _providers;
  bool get isLoading => _isLoading;
  String? get error => _error;

  ProviderProvider() {
    fetchProviders();
  }

  Future<void> fetchProviders() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // In a real app, this would be an API call
      await Future.delayed(const Duration(seconds: 1));
      _providers = MockDataService.getProviders();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load providers: $e';
      if (kDebugMode) {
        print(_error);
      }
      notifyListeners();
    }
  }

  // Note: ServiceProvider in service.dart doesn't have a category field
  // This is a placeholder implementation
  List<ServiceProvider> getProvidersByCategory(String category) {
    return _providers;
  }

  ServiceProvider? getProviderById(String id) {
    try {
      return _providers.firstWhere((provider) => provider.id == id);
    } catch (e) {
      return null;
    }
  }

  List<ServiceProvider> searchProviders(String query) {
    if (query.isEmpty) {
      return _providers;
    }

    final lowercaseQuery = query.toLowerCase();
    return _providers.where((provider) {
      return provider.name.toLowerCase().contains(lowercaseQuery) ||
          provider.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}
