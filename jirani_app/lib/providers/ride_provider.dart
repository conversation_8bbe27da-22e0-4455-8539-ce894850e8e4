import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/lat_lng.dart' as custom;
import '../models/ride.dart';
import '../models/websocket_message.dart';
import '../services/logging_service.dart';
import '../services/mapbox_service_new.dart';
import '../services/network_bound_resource.dart';
import 'location_provider.dart';
import 'repository_providers.dart';
import 'websocket_provider.dart';

/// Provider for the current ride
final currentRideProvider =
    StateNotifierProvider<CurrentRideNotifier, AsyncValue<Ride?>>((ref) {
  return CurrentRideNotifier(ref);
});

/// Notifier for the current ride
class CurrentRideNotifier extends StateNotifier<AsyncValue<Ride?>> {
  /// Reference to the provider
  final Ref _ref;

  /// WebSocket subscription for ride updates
  StreamSubscription<WebSocketMessage>? _rideStatusSubscription;
  StreamSubscription<WebSocketMessage>? _driverLocationSubscription;

  /// Creates a new instance of [CurrentRideNotifier]
  CurrentRideNotifier(this._ref) : super(const AsyncValue.data(null)) {
    _initializeWebSocketListeners();
  }

  @override
  void dispose() {
    _rideStatusSubscription?.cancel();
    _driverLocationSubscription?.cancel();
    super.dispose();
  }

  /// Initialize WebSocket listeners for real-time updates
  void _initializeWebSocketListeners() {
    // Listen for ride status updates
    _ref.listen(rideStatusUpdateProvider, (previous, next) {
      next.when(
        data: (message) {
          final currentRide = state.value;
          if (currentRide != null && message.rideId == currentRide.id) {
            _handleRideStatusUpdate(message);
          }
        },
        loading: () {},
        error: (error, stack) {},
      );
    });

    // Listen for driver location updates
    _ref.listen(driverLocationProvider, (previous, next) {
      next.when(
        data: (message) {
          final currentRide = state.value;
          if (currentRide != null && message.rideId == currentRide.id) {
            _handleDriverLocationUpdate(message);
          }
        },
        loading: () {},
        error: (error, stack) {},
      );
    });
  }

  /// Handle ride status updates from WebSocket
  void _handleRideStatusUpdate(WebSocketMessage message) {
    try {
      final currentRide = state.value;
      if (currentRide == null) return;

      final statusString = message.data['status'] as String?;
      if (statusString == null) return;

      // Map backend status to frontend enum
      final newStatus = _mapBackendStatusToRideStatus(statusString);

      // Extract additional data from the message
      final driverId = message.data['driver_id'] as String?;
      final driverName = message.data['driver_name'] as String?;
      final driverPhone = message.data['driver_phone'] as String?;
      final estimatedArrival = message.data['estimated_arrival'] as String?;
      final actualFare = message.data['actual_fare'] as double?;
      final completedAt = message.data['completed_at'] as String?;
      final acceptedAt = message.data['accepted_at'] as String?;
      final startedAt = message.data['started_at'] as String?;

      // Update ride with comprehensive status data
      final updatedRide = currentRide.copyWith(
        status: newStatus,
        riderId: driverId ?? currentRide.riderId,
        actualFare: actualFare ?? currentRide.actualFare,
        acceptedAt: acceptedAt != null
            ? DateTime.parse(acceptedAt)
            : currentRide.acceptedAt,
        startedAt: startedAt != null
            ? DateTime.parse(startedAt)
            : currentRide.startedAt,
        completedAt: completedAt != null
            ? DateTime.parse(completedAt)
            : currentRide.completedAt,
      );

      state = AsyncValue.data(updatedRide);

      // Trigger status-specific actions and notifications
      _handleStatusSpecificActions(newStatus, message.data);

      LoggingService.i('Ride status updated to: $newStatus');
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error handling ride status update',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
    }
  }

  /// Map backend status string to RideStatus enum
  RideStatus _mapBackendStatusToRideStatus(String backendStatus) {
    switch (backendStatus) {
      case 'requested':
        return RideStatus.requested;
      case 'accepted':
        return RideStatus.accepted;
      case 'en_route_to_pickup':
        return RideStatus.enRouteToPickup;
      case 'arrived_at_pickup':
        return RideStatus.arrivedAtPickup;
      case 'in_progress':
        return RideStatus.inProgress;
      case 'completed':
        return RideStatus.completed;
      case 'cancelled':
        return RideStatus.cancelled;
      default:
        return RideStatus.requested;
    }
  }

  /// Handle status-specific actions and notifications
  void _handleStatusSpecificActions(
      RideStatus status, Map<String, dynamic> data) {
    switch (status) {
      case RideStatus.accepted:
        _showNotification(
          'Ride Accepted!',
          'Your driver is on the way to pick you up.',
          isSuccess: true,
        );
        break;
      case RideStatus.enRouteToPickup:
        _showNotification(
          'Driver En Route',
          'Your driver is heading to your pickup location.',
        );
        break;
      case RideStatus.arrivedAtPickup:
        _showNotification(
          'Driver Arrived',
          'Your driver has arrived at the pickup location.',
          isUrgent: true,
        );
        break;
      case RideStatus.inProgress:
        _showNotification(
          'Ride Started',
          'Your ride is now in progress. Enjoy your trip!',
          isSuccess: true,
        );
        break;
      case RideStatus.completed:
        _showNotification(
          'Ride Completed',
          'You have arrived at your destination. Please rate your ride.',
          isSuccess: true,
        );
        break;
      case RideStatus.cancelled:
        final reason = data['cancellation_reason'] as String?;
        _showNotification(
          'Ride Cancelled',
          reason ?? 'Your ride has been cancelled.',
          isError: true,
        );
        break;
      default:
        break;
    }
  }

  /// Show notification to user
  void _showNotification(
    String title,
    String message, {
    bool isSuccess = false,
    bool isError = false,
    bool isUrgent = false,
  }) {
    // This would integrate with a notification service
    // For now, just log the notification
    LoggingService.i('Notification: $title - $message');

    // In a full implementation, this would:
    // 1. Show in-app notification
    // 2. Send push notification if app is in background
    // 3. Play notification sound for urgent notifications
    // 4. Update notification badge
  }

  /// Handle driver location updates from WebSocket
  void _handleDriverLocationUpdate(WebSocketMessage message) {
    try {
      final currentRide = state.value;
      if (currentRide == null) return;

      final latitude = message.data['latitude'] as double?;
      final longitude = message.data['longitude'] as double?;

      if (latitude == null || longitude == null) return;

      // For now, just log the driver location update
      // In a full implementation, this would update the ride model with driver location
      LoggingService.i('Driver location updated: $latitude, $longitude');
    } catch (e) {
      LoggingService.e('Error handling driver location update', error: e);
    }
  }

  /// Creates a new ride request
  Future<void> createRideRequest({
    required String userId,
    required RideLocation pickup,
    required RideLocation dropoff,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      state = const AsyncValue.loading();

      final repository = _ref.read(rideRepositoryProvider);

      final ride = await repository.createRideRequest(
        userId: userId,
        pickup: pickup,
        dropoff: dropoff,
        paymentMethod: paymentMethod,
      );

      if (ride == null) {
        throw Exception('Failed to create ride request');
      }

      state = AsyncValue.data(ride);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error creating ride request',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Updates the current ride
  void updateRide(Ride ride) {
    state = AsyncValue.data(ride);
  }

  /// Updates the ride status
  Future<void> updateRideStatus(RideStatus status) async {
    try {
      final currentRide = state.value;
      final repository = _ref.read(rideRepositoryProvider);

      if (currentRide == null) {
        throw Exception('No current ride');
      }

      final updatedRide = await repository.updateRideStatus(
        rideId: currentRide.id,
        status: status,
      );

      if (updatedRide == null) {
        throw Exception('Failed to update ride status');
      }

      state = AsyncValue.data(updatedRide);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error updating ride status',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Cancels the current ride
  Future<void> cancelRide() async {
    try {
      final currentRide = state.value;
      final repository = _ref.read(rideRepositoryProvider);

      if (currentRide == null) {
        throw Exception('No current ride');
      }

      final updatedRide = await repository.cancelRide(currentRide.id);

      if (updatedRide == null) {
        throw Exception('Failed to cancel ride');
      }

      state = AsyncValue.data(updatedRide);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error cancelling ride',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Completes the current ride
  Future<void> completeRide() async {
    try {
      final currentRide = state.value;
      final repository = _ref.read(rideRepositoryProvider);

      if (currentRide == null) {
        throw Exception('No current ride');
      }

      final updatedRide = await repository.completeRide(currentRide.id);

      if (updatedRide == null) {
        throw Exception('Failed to complete ride');
      }

      state = AsyncValue.data(updatedRide);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error completing ride',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Rates the current ride
  Future<void> rateRide({
    required int rating,
    String? review,
  }) async {
    try {
      final currentRide = state.value;
      final repository = _ref.read(rideRepositoryProvider);

      if (currentRide == null) {
        throw Exception('No current ride');
      }

      final success = await repository.rateRide(
        rideId: currentRide.id,
        rating: rating,
        review: review,
      );

      if (!success) {
        throw Exception('Failed to rate ride');
      }

      // Clear the current ride after rating
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error rating ride',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refreshes the current ride data from the server
  Future<void> refreshRide(String rideId) async {
    try {
      // For now, just log that we would refresh the ride
      // In a full implementation, this would call the API to get updated ride data
      LoggingService.i('Refreshing ride data for ride: $rideId');
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error refreshing ride',
        error: e,
        stackTrace: stackTrace,
        tag: 'CurrentRideNotifier',
      );
    }
  }

  /// Clears the current ride
  void clearRide() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the user's ride history
final rideHistoryProvider = StateNotifierProvider.family<RideHistoryNotifier,
    AsyncValue<List<Ride>>, String>((ref, userId) {
  return RideHistoryNotifier(ref, userId);
});

/// Notifier for ride history
class RideHistoryNotifier extends StateNotifier<AsyncValue<List<Ride>>> {
  /// Reference to the provider
  final Ref _ref;

  /// User ID
  final String _userId;

  /// Creates a new instance of [RideHistoryNotifier]
  RideHistoryNotifier(this._ref, this._userId)
      : super(const AsyncValue.loading()) {
    _fetchRideHistory();
  }

  /// Fetch ride history
  Future<void> _fetchRideHistory() async {
    final repository = _ref.read(rideRepositoryProvider);

    try {
      // Listen to the stream of ride history
      repository.getUserRideHistory(_userId).listen(
        (resource) {
          switch (resource.status) {
            case ResourceStatus.loadingFromCache:
              // Keep current state if we have data, otherwise show loading
              if (state is! AsyncData ||
                  (state as AsyncData<List<Ride>>).value.isEmpty) {
                state = const AsyncValue.loading();
              }
              break;
            case ResourceStatus.loadingFromNetwork:
              // If we have cached data, use it while loading from network
              if (resource.data != null) {
                state = AsyncValue.data(resource.data!);
              } else if (state is! AsyncData) {
                state = const AsyncValue.loading();
              }
              break;
            case ResourceStatus.success:
              state = AsyncValue.data(resource.data!);
              break;
            case ResourceStatus.error:
              // If we have data (from cache), keep it and show error
              if (resource.data != null) {
                state = AsyncValue.data(resource.data!);
                LoggingService.e(
                  'Error fetching ride history',
                  error: resource.error,
                  stackTrace: resource.stackTrace,
                  tag: 'RideHistoryNotifier',
                );
              } else {
                state = AsyncValue.error(
                  resource.error!,
                  resource.stackTrace!,
                );
              }
              break;
          }
        },
        onError: (error, stackTrace) {
          state = AsyncValue.error(error, stackTrace);
        },
      );
    } catch (e, stackTrace) {
      LoggingService.e(
        'Error fetching ride history',
        error: e,
        stackTrace: stackTrace,
        tag: 'RideHistoryNotifier',
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh ride history
  Future<void> refresh() async {
    await _fetchRideHistory();
  }
}

/// Provider for the estimated fare
final estimatedFareProvider = FutureProvider.autoDispose<double?>((ref) async {
  final pickupLocation = ref.watch(pickupLocationProvider);
  final dropoffLocation = ref.watch(dropoffLocationProvider);
  final repository = ref.read(rideRepositoryProvider);

  // Return null if either pickup or dropoff location is not available
  if (pickupLocation == null || dropoffLocation == null) {
    return null;
  }

  try {
    // Get estimated fare from repository
    return await repository.getEstimatedFare(
      pickup: pickupLocation,
      dropoff: dropoffLocation,
    );
  } catch (e, stackTrace) {
    LoggingService.e(
      'Error getting estimated fare',
      error: e,
      stackTrace: stackTrace,
      tag: 'estimatedFareProvider',
    );
    return null;
  }
});

/// Provider for the route between pickup and dropoff locations
final routeProvider =
    FutureProvider.autoDispose<List<custom.LatLng>?>((ref) async {
  final pickupLocation = ref.watch(pickupLocationProvider);
  final dropoffLocation = ref.watch(dropoffLocationProvider);

  // Return null if either pickup or dropoff location is not available
  if (pickupLocation == null || dropoffLocation == null) {
    return null;
  }

  // Get directions using the new Mapbox service
  final point1 = MapboxServiceNew.latLngToPoint(
      pickupLocation.latitude, pickupLocation.longitude);
  final point2 = MapboxServiceNew.latLngToPoint(
      dropoffLocation.latitude, dropoffLocation.longitude);

  final directions = await MapboxServiceNew.getDirections(point1, point2);

  if (directions['routes'] == null || (directions['routes'] as List).isEmpty) {
    return null;
  }

  // Extract the route coordinates
  final route = directions['routes'][0];
  final geometry = route['geometry'] as Map<String, dynamic>;
  final coordinates = geometry['coordinates'] as List;

  // Convert to LatLng list
  return coordinates.map((coord) {
    final lng = (coord[0] as num).toDouble();
    final lat = (coord[1] as num).toDouble();
    return custom.LatLng(lat, lng);
  }).toList();
});

/// Provider for the selected payment method
final paymentMethodProvider = StateProvider<PaymentMethod>((ref) {
  return PaymentMethod.cash;
});
