import 'package:flutter/foundation.dart' show kIsWeb;

/// Helper class for handling image URLs
class ImageUrlHelper {
  /// Ensures that image URLs use the correct protocol (HTTP or HTTPS)
  /// 
  /// In production, all URLs should use HTTPS
  /// In development, URLs may use HTTP
  static String ensureSecureUrl(String url) {
    // If the URL is already using HTTPS, return it as is
    if (url.startsWith('https://')) {
      return url;
    }
    
    // If the URL is using HTTP and we're in production, convert it to HTTPS
    if (url.startsWith('http://')) {
      // Always use HTTPS for storage URLs in production
      if (url.contains('storage.jirani.tufiked.live')) {
        return url.replaceFirst('http://', 'https://');
      }
      
      // For other URLs, use HTTPS in web production
      if (kIsWeb) {
        return url.replaceFirst('http://', 'https://');
      }
    }
    
    // Return the original URL if no changes are needed
    return url;
  }
  
  /// Gets the appropriate image URL for the current platform and environment
  static String getImageUrl(String url) {
    return ensureSecureUrl(url);
  }
}
