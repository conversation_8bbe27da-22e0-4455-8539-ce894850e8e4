import 'package:flutter/material.dart';

/// Device type
enum DeviceType {
  /// Mobile device
  mobile,
  
  /// Tablet device
  tablet,
  
  /// Desktop device
  desktop,
}

/// Screen size
enum ScreenSize {
  /// Extra small screen
  xs,
  
  /// Small screen
  sm,
  
  /// Medium screen
  md,
  
  /// Large screen
  lg,
  
  /// Extra large screen
  xl,
}

/// Responsive utils for handling responsive layouts
class ResponsiveUtils {
  /// Mobile width breakpoint
  static const double mobileBreakpoint = 600;
  
  /// Tablet width breakpoint
  static const double tabletBreakpoint = 900;
  
  /// Desktop width breakpoint
  static const double desktopBreakpoint = 1200;
  
  /// Extra small width breakpoint
  static const double xsBreakpoint = 480;
  
  /// Small width breakpoint
  static const double smBreakpoint = 600;
  
  /// Medium width breakpoint
  static const double mdBreakpoint = 840;
  
  /// Large width breakpoint
  static const double lgBreakpoint = 1024;
  
  /// Extra large width breakpoint
  static const double xlBreakpoint = 1440;
  
  /// Get device type based on width
  static DeviceType getDeviceType(double width) {
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < desktopBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
  
  /// Get screen size based on width
  static ScreenSize getScreenSize(double width) {
    if (width < xsBreakpoint) {
      return ScreenSize.xs;
    } else if (width < smBreakpoint) {
      return ScreenSize.sm;
    } else if (width < mdBreakpoint) {
      return ScreenSize.md;
    } else if (width < lgBreakpoint) {
      return ScreenSize.lg;
    } else {
      return ScreenSize.xl;
    }
  }
  
  /// Get number of grid columns based on width
  static int getGridColumns(double width) {
    final screenSize = getScreenSize(width);
    
    switch (screenSize) {
      case ScreenSize.xs:
        return 1;
      case ScreenSize.sm:
        return 2;
      case ScreenSize.md:
        return 3;
      case ScreenSize.lg:
        return 4;
      case ScreenSize.xl:
        return 5;
    }
  }
  
  /// Get grid item width based on width and spacing
  static double getGridItemWidth(double width, double spacing, {int? columns}) {
    final cols = columns ?? getGridColumns(width);
    return (width - (spacing * (cols - 1))) / cols;
  }
  
  /// Get font size based on width
  static double getFontSize(double width, double baseFontSize) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize;
      case DeviceType.tablet:
        return baseFontSize * 1.1;
      case DeviceType.desktop:
        return baseFontSize * 1.2;
    }
  }
  
  /// Get icon size based on width
  static double getIconSize(double width, double baseIconSize) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseIconSize;
      case DeviceType.tablet:
        return baseIconSize * 1.2;
      case DeviceType.desktop:
        return baseIconSize * 1.5;
    }
  }
  
  /// Get padding based on width
  static EdgeInsets getPadding(double width, EdgeInsets basePadding) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return basePadding;
      case DeviceType.tablet:
        return basePadding * 1.5;
      case DeviceType.desktop:
        return basePadding * 2;
    }
  }
  
  /// Get margin based on width
  static EdgeInsets getMargin(double width, EdgeInsets baseMargin) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseMargin;
      case DeviceType.tablet:
        return baseMargin * 1.5;
      case DeviceType.desktop:
        return baseMargin * 2;
    }
  }
  
  /// Get border radius based on width
  static BorderRadius getBorderRadius(double width, BorderRadius baseBorderRadius) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseBorderRadius;
      case DeviceType.tablet:
        return BorderRadius.circular(baseBorderRadius.topLeft.x * 1.5);
      case DeviceType.desktop:
        return BorderRadius.circular(baseBorderRadius.topLeft.x * 2);
    }
  }
  
  /// Get max width based on width
  static double getMaxWidth(double width) {
    final deviceType = getDeviceType(width);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return width;
      case DeviceType.tablet:
        return width * 0.8;
      case DeviceType.desktop:
        return width * 0.7;
    }
  }
  
  /// Get max height based on height
  static double getMaxHeight(double height) {
    return height * 0.8;
  }
}

/// Responsive widget for handling responsive layouts
class ResponsiveWidget extends StatelessWidget {
  /// Mobile widget
  final Widget mobile;
  
  /// Tablet widget
  final Widget? tablet;
  
  /// Desktop widget
  final Widget? desktop;
  
  /// Creates a new instance of [ResponsiveWidget]
  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final deviceType = ResponsiveUtils.getDeviceType(width);
        
        switch (deviceType) {
          case DeviceType.mobile:
            return mobile;
          case DeviceType.tablet:
            return tablet ?? mobile;
          case DeviceType.desktop:
            return desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}

/// Responsive grid for handling responsive grid layouts
class ResponsiveGrid extends StatelessWidget {
  /// Children widgets
  final List<Widget> children;
  
  /// Spacing between items
  final double spacing;
  
  /// Padding around the grid
  final EdgeInsets? padding;
  
  /// Creates a new instance of [ResponsiveGrid]
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16,
    this.padding,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final columns = ResponsiveUtils.getGridColumns(width);
        
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children: children.map((child) {
              final itemWidth = ResponsiveUtils.getGridItemWidth(
                width - (padding?.horizontal ?? 0),
                spacing,
                columns: columns,
              );
              
              return SizedBox(
                width: itemWidth,
                child: child,
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
