import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/logging_service.dart';

/// Global error handler for the application
class AppErrorHandler {
  /// Initialize the error handler
  static void initialize() {
    // Set up Flutter error handling
    FlutterError.onError = _handleFlutterError;
    
    // Set up Dart error handling
    PlatformDispatcher.instance.onError = _handlePlatformError;
    
    // Set up Zone error handling
    runZonedGuarded(
      () {
        // This is where the app would be started, but we're doing that in main.dart
      },
      _handleZoneError,
    );
    
    LoggingService.i('Error handler initialized');
  }
  
  /// Handle Flutter errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    LoggingService.e(
      'Flutter error',
      error: details.exception,
      stackTrace: details.stack,
      tag: 'FlutterError',
    );
    
    // Report to crash reporting service
    // TODO: Implement crash reporting
    
    // Let Flutter handle the error
    FlutterError.presentError(details);
  }
  
  /// Handle platform errors
  static bool _handlePlatformError(Object error, StackTrace stack) {
    LoggingService.e(
      'Platform error',
      error: error,
      stackTrace: stack,
      tag: 'PlatformError',
    );
    
    // Report to crash reporting service
    // TODO: Implement crash reporting
    
    // Return true to prevent the error from being handled by the platform
    return true;
  }
  
  /// Handle Zone errors
  static void _handleZoneError(Object error, StackTrace stack) {
    LoggingService.e(
      'Zone error',
      error: error,
      stackTrace: stack,
      tag: 'ZoneError',
    );
    
    // Report to crash reporting service
    // TODO: Implement crash reporting
  }
  
  /// Show error dialog
  static Future<void> showErrorDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(message),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
  
  /// Show error snackbar
  static void showErrorSnackbar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: duration,
        action: SnackBarAction(
          label: 'DISMISS',
          textColor: Theme.of(context).colorScheme.onError,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Handle API error
  static void handleApiError(
    BuildContext context,
    Object error, {
    String? fallbackMessage,
  }) {
    String message = fallbackMessage ?? 'An error occurred. Please try again.';
    
    if (error is Exception) {
      message = error.toString().replaceAll('Exception: ', '');
    }
    
    showErrorSnackbar(context, message);
  }
}
