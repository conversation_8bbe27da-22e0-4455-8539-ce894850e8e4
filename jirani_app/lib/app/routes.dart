import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Import screens
import '../screens/splash_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/home_screen.dart';
import '../screens/food_home_screen.dart';
import '../screens/explore_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/details_screen.dart';
import '../screens/boda_boda/boda_boda_screen.dart';
import '../screens/boda_boda/location_selection_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/auth/forgot_password_screen.dart';

// Import transition type
import 'transition_type.dart';

/// Router configuration for the app
class AppRouter {
  /// The router instance
  static final router = GoRouter(
    initialLocation: '/',
    routes: [
      // Splash screen
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Onboarding screen
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        pageBuilder: (context, state) => _buildPageWithAnimation(
          context: context,
          state: state,
          child: const OnboardingScreen(),
          transitionType: TransitionType.fadeAndSlide,
        ),
      ),

      // Login screen
      GoRoute(
        path: '/login',
        name: 'login',
        pageBuilder: (context, state) => _buildPageWithAnimation(
          context: context,
          state: state,
          child: const LoginScreen(),
          transitionType: TransitionType.fadeAndSlide,
        ),
      ),

      // Register screen
      GoRoute(
        path: '/register',
        name: 'register',
        pageBuilder: (context, state) => _buildPageWithAnimation(
          context: context,
          state: state,
          child: const RegisterScreen(),
          transitionType: TransitionType.fadeAndSlide,
        ),
      ),

      // Forgot password screen
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        pageBuilder: (context, state) => _buildPageWithAnimation(
          context: context,
          state: state,
          child: const ForgotPasswordScreen(),
          transitionType: TransitionType.fadeAndSlide,
        ),
      ),

      // Main app shell with bottom navigation
      ShellRoute(
        builder: (context, state, child) => ScaffoldWithNavBar(child: child),
        routes: [
          // Home screen
          GoRoute(
            path: '/home',
            name: 'home',
            pageBuilder: (context, state) => _buildPageWithAnimation(
              context: context,
              state: state,
              child: const HomeScreen(),
              transitionType: TransitionType.fadeAndSlide,
            ),
            routes: [
              // Service details screen
              GoRoute(
                path: 'service/:id',
                name: 'service_details',
                pageBuilder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return _buildPageWithAnimation(
                    context: context,
                    state: state,
                    child: DetailsScreen(id: id),
                    transitionType: TransitionType.fadeThrough,
                  );
                },
              ),

              // Food screen
              GoRoute(
                path: 'food',
                name: 'food',
                pageBuilder: (context, state) => _buildPageWithAnimation(
                  context: context,
                  state: state,
                  child: const FoodHomeScreen(),
                  transitionType: TransitionType.fadeThrough,
                ),
              ),

              // Boda Boda screen
              GoRoute(
                path: 'boda',
                name: 'boda',
                pageBuilder: (context, state) => _buildPageWithAnimation(
                  context: context,
                  state: state,
                  child: const BodaBodaScreen(),
                  transitionType: TransitionType.fadeThrough,
                ),
              ),

              // Modern Boda Boda screen
              GoRoute(
                path: 'modern-boda',
                name: 'modern-boda',
                pageBuilder: (context, state) => _buildPageWithAnimation(
                  context: context,
                  state: state,
                  child: const LocationSelectionScreen(),
                  transitionType: TransitionType.fadeThrough,
                ),
              ),
            ],
          ),

          // Explore screen
          GoRoute(
            path: '/explore',
            name: 'explore',
            pageBuilder: (context, state) => _buildPageWithAnimation(
              context: context,
              state: state,
              child: const ExploreScreen(),
              transitionType: TransitionType.fadeAndSlide,
            ),
          ),

          // Profile screen
          GoRoute(
            path: '/profile',
            name: 'profile',
            pageBuilder: (context, state) => _buildPageWithAnimation(
              context: context,
              state: state,
              child: const ProfileScreen(),
              transitionType: TransitionType.fadeAndSlide,
            ),
          ),
        ],
      ),
    ],
  );

  /// Builds a page with custom animation
  static CustomTransitionPage<void> _buildPageWithAnimation({
    required BuildContext context,
    required GoRouterState state,
    required Widget child,
    TransitionType transitionType = TransitionType.fade,
  }) {
    return CustomTransitionPage<void>(
      key: state.pageKey,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return transitionType.apply(child);
      },
    );
  }
}

/// Scaffold with bottom navigation bar for main app shell
class ScaffoldWithNavBar extends StatelessWidget {
  final Widget child;

  const ScaffoldWithNavBar({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: NavigationBar(
        onDestinationSelected: (index) {
          switch (index) {
            case 0:
              context.goNamed('home');
              break;
            case 1:
              context.goNamed('explore');
              break;
            case 2:
              context.goNamed('profile');
              break;
          }
        },
        selectedIndex: _calculateSelectedIndex(context),
        destinations: const [
          NavigationDestination(
            icon: Icon(Icons.home_outlined),
            selectedIcon: Icon(Icons.home),
            label: 'Home',
          ),
          NavigationDestination(
            icon: Icon(Icons.explore_outlined),
            selectedIcon: Icon(Icons.explore),
            label: 'Explore',
          ),
          NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/home')) {
      return 0;
    }
    if (location.startsWith('/explore')) {
      return 1;
    }
    if (location.startsWith('/profile')) {
      return 2;
    }
    return 0;
  }
}
