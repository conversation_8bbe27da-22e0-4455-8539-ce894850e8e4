import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:jirani_app/providers/connectivity_provider.dart';
import 'package:jirani_app/providers/message_provider.dart'; // Ensure this import is present
import 'package:jirani_app/screens/no_internet_screen.dart';

import '../providers/theme_provider.dart' as app_theme;
import 'routes.dart';
import 'theme.dart';

/// Main app widget
class JiraniApp extends ConsumerWidget {
  /// Creates a new instance of [JiraniApp]
  const JiraniApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityStatus = ref.watch(connectivityStatusProvider);

    // If disconnected or status is unknown, show NoInternetScreen
    if (connectivityStatus == ConnectivityStatus.disconnected || connectivityStatus == ConnectivityStatus.unknown) {
      // It's important that NoInternetScreen itself is a MaterialApp or provides one,
      // or that we wrap it in one here if it's just a Scaffold.
      // For simplicity, let's assume NoInternetScreen is a full screen ready to be displayed.
      // We might need to wrap it in a MaterialApp if it's not designed to be a root widget.
      // Let's provide a minimal MaterialApp for it.
      final themeMode = ref.watch(app_theme.themeModeProvider);
      final flutterThemeMode = themeMode == app_theme.ThemeMode.light
          ? ThemeMode.light
          : themeMode == app_theme.ThemeMode.dark
              ? ThemeMode.dark
              : ThemeMode.system;

      return MaterialApp(
        title: 'Jirani App - No Internet',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme(),
        darkTheme: AppTheme.darkTheme(),
        themeMode: flutterThemeMode,
        home: const NoInternetScreen(),
      );
    }

    // If connected, proceed with the main app
    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Get current theme mode from provider
    final themeMode = ref.watch(app_theme.themeModeProvider);

    // Convert app theme mode to Flutter theme mode
    final flutterThemeMode = themeMode == app_theme.ThemeMode.light
        ? ThemeMode.light
        : themeMode == app_theme.ThemeMode.dark
            ? ThemeMode.dark
            : ThemeMode.system;

    // Set system UI overlay style based on theme
    final brightness = ref.watch(app_theme.brightnessProvider);
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        statusBarBrightness: brightness == Brightness.dark ? Brightness.dark : Brightness.light,
      ),
    );

    return MaterialApp.router(
      title: 'Jirani App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: flutterThemeMode,
      routerConfig: AppRouter.router,
      // Add some global styling
      builder: (context, child) {
        return MediaQuery(
          // Apply fixed text scaling
          data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
          child: _AppMessageListener(child: child!), // Wrap child with listener
        );
      },
    );
  }
}

// Helper widget to listen for messages and show SnackBars
class _AppMessageListener extends ConsumerWidget {
  final Widget child;
  const _AppMessageListener({required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AppMessage?>(messageProvider, (previous, next) {
      if (next != null) {
        ScaffoldMessenger.of(context).removeCurrentSnackBar(); // Remove any existing snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(next.icon, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(next.text, style: const TextStyle(color: Colors.white))),
              ],
            ),
            backgroundColor: next.backgroundColor,
            duration: next.duration,
            behavior: SnackBarBehavior.floating, // Or .fixed depending on preference
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            margin: const EdgeInsets.all(10),
          ),
        );
        // Clear the message after showing it so it doesn't reappear on rebuilds
        ref.read(messageProvider.notifier).clearMessage();
      }
    });
    return child;
  }
}
