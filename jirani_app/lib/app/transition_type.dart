import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Transition types for page animations
enum TransitionType {
  /// Fade transition
  fade,

  /// Fade and slide transition
  fadeAndSlide,

  /// Slide from right transition
  slideFromRight,

  /// Slide from bottom transition
  slideFromBottom,

  /// Scale transition
  scale,

  /// Fade through transition
  fadeThrough,
}

/// Extension methods for TransitionType
extension TransitionTypeExtension on TransitionType {
  /// Apply the transition to a widget
  Widget apply(Widget child) {
    switch (this) {
      case TransitionType.fade:
        return child.animate(autoPlay: true)
            .fade(duration: 300.ms, curve: Curves.easeOut);

      case TransitionType.fadeAndSlide:
        return child.animate(autoPlay: true)
            .fade(duration: 300.ms, curve: Curves.easeOut)
            .slideY(begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOut);

      case TransitionType.slideFromRight:
        return child.animate(autoPlay: true)
            .slideX(begin: 1, end: 0, duration: 300.ms, curve: Curves.easeOut);

      case TransitionType.slideFromBottom:
        return child.animate(autoPlay: true)
            .slideY(begin: 1, end: 0, duration: 300.ms, curve: Curves.easeOut);

      case TransitionType.scale:
        return child.animate(autoPlay: true)
            .scale(begin: const Offset(0.9, 0.9), end: const Offset(1.0, 1.0), duration: 300.ms, curve: Curves.easeOut)
            .fade(duration: 300.ms, curve: Curves.easeOut);

      case TransitionType.fadeThrough:
        return child.animate(autoPlay: true)
            .fadeIn(duration: 300.ms, curve: Curves.easeOut)
            .scale(begin: const Offset(0.95, 0.95), end: const Offset(1.0, 1.0), duration: 300.ms, curve: Curves.easeOut);
    }
  }
}
