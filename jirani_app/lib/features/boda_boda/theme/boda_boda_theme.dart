import 'package:flutter/material.dart';
import '../../../app/theme.dart';

/// Theme for the BodaBoda feature
class BodaBodaTheme {
  /// Primary color for the BodaBoda feature
  static final Color primaryColor = AppTheme.primaryColor;

  /// Secondary color for the BodaBoda feature
  static final Color secondaryColor = AppTheme.secondaryColor;

  /// Text color for the BodaBoda feature
  static final Color textColor = Colors.black87;

  /// Background color for the BodaBoda feature
  static final Color backgroundColor = Colors.white;

  /// Card color for the BodaBoda feature
  static final Color cardColor = Colors.white;

  /// Shadow color for the BodaBoda feature
  static final Color shadowColor = Colors.black.withOpacity(0.1);

  /// Border radius for cards
  static const double cardBorderRadius = 24.0;

  /// Border radius for buttons
  static const double buttonBorderRadius = 24.0;

  /// Padding for cards
  static const EdgeInsets cardPadding = EdgeInsets.all(16.0);

  /// Padding for buttons
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    vertical: 16.0,
    horizontal: 24.0,
  );

  /// Shadow for cards
  static List<BoxShadow> get cardShadow => [
        BoxShadow(
          color: shadowColor,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ];

  /// Shadow for buttons
  static List<BoxShadow> get buttonShadow => [
        BoxShadow(
          color: shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ];

  /// Text style for headings
  static TextStyle get headingStyle => TextStyle(
        color: textColor,
        fontWeight: FontWeight.bold,
        fontSize: 18,
      );

  /// Text style for subheadings
  static TextStyle get subheadingStyle => TextStyle(
        color: textColor,
        fontWeight: FontWeight.w500,
        fontSize: 16,
      );

  /// Text style for body text
  static TextStyle get bodyStyle => TextStyle(
        color: textColor,
        fontSize: 14,
      );

  /// Text style for captions
  static TextStyle get captionStyle => TextStyle(
        color: textColor.withOpacity(0.7),
        fontSize: 12,
      );

  /// Button style for primary buttons
  static ButtonStyle get primaryButtonStyle => ButtonStyle(
        backgroundColor: WidgetStateProperty.all(primaryColor),
        foregroundColor: WidgetStateProperty.all(Colors.white),
        padding: WidgetStateProperty.all(buttonPadding),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonBorderRadius),
          ),
        ),
      );

  /// Button style for secondary buttons
  static ButtonStyle get secondaryButtonStyle => ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.white),
        foregroundColor: WidgetStateProperty.all(primaryColor),
        padding: WidgetStateProperty.all(buttonPadding),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonBorderRadius),
            side: BorderSide(color: primaryColor),
          ),
        ),
      );
}
