import 'package:flutter/material.dart';

/// Custom button widget
class CustomButton extends StatelessWidget {
  /// Button text
  final String text;

  /// Button onPressed callback
  final VoidCallback? onPressed;

  /// Is the button loading
  final bool isLoading;

  /// Button color
  final Color? color;

  /// Button text color
  final Color? textColor;

  /// Button border radius
  final double borderRadius;

  /// Button padding
  final EdgeInsetsGeometry padding;

  /// Button width
  final double? width;

  /// Button height
  final double? height;

  /// Button icon
  final IconData? icon;

  /// Button icon position
  final IconPosition iconPosition;

  /// Creates a new custom button
  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.color,
    this.textColor,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.left,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonColor = color ?? theme.colorScheme.primary;
    final buttonTextColor = textColor ?? theme.colorScheme.onPrimary;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : _buildButtonContent(),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (icon == null) {
      return Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (iconPosition == IconPosition.left) ...[
          Icon(icon, size: 20),
          const SizedBox(width: 8),
        ],
        Text(
          text,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        if (iconPosition == IconPosition.right) ...[
          const SizedBox(width: 8),
          Icon(icon, size: 20),
        ],
      ],
    );
  }
}

/// Icon position enum
enum IconPosition {
  /// Left position
  left,

  /// Right position
  right,
}
