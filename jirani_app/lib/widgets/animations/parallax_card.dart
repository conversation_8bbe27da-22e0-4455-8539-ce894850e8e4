import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// A card with a parallax effect
class ParallaxCard extends StatelessWidget {
  /// The image URL
  final String imageUrl;
  
  /// The title of the card
  final String title;
  
  /// The subtitle of the card
  final String? subtitle;
  
  /// The height of the card
  final double height;
  
  /// The width of the card
  final double? width;
  
  /// The border radius of the card
  final BorderRadius borderRadius;
  
  /// Called when the card is tapped
  final VoidCallback? onTap;
  
  /// The parallax effect factor (higher values create more movement)
  final double parallaxFactor;

  /// Creates a new instance of [ParallaxCard]
  const ParallaxCard({
    super.key,
    required this.imageUrl,
    required this.title,
    this.subtitle,
    this.height = 200,
    this.width,
    this.borderRadius = const BorderRadius.all(Radius.circular(16)),
    this.onTap,
    this.parallaxFactor = 0.3,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: Flow(
        delegate: ParallaxFlowDelegate(
          scrollable: Scrollable.of(context),
          listItemContext: context,
          parallaxFactor: parallaxFactor,
        ),
        children: [
          GestureDetector(
            onTap: onTap,
            child: ClipRRect(
              borderRadius: borderRadius,
              child: Stack(
                children: [
                  // Background image with parallax effect
                  CachedNetworkImage(
                    imageUrl: imageUrl,
                    height: height,
                    width: width ?? double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.error),
                    ),
                  ),
                  
                  // Gradient overlay
                  Container(
                    height: height,
                    width: width ?? double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: borderRadius,
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                        stops: const [0.6, 1.0],
                      ),
                    ),
                  ),
                  
                  // Title and subtitle
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withOpacity(0.8),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Flow delegate for parallax effect
class ParallaxFlowDelegate extends FlowDelegate {
  final ScrollableState scrollable;
  final BuildContext listItemContext;
  final double parallaxFactor;

  ParallaxFlowDelegate({
    required this.scrollable,
    required this.listItemContext,
    this.parallaxFactor = 0.3,
  }) : super(repaint: scrollable.position);

  @override
  BoxConstraints getConstraintsForChild(int i, BoxConstraints constraints) {
    return constraints.tighten(
      width: constraints.maxWidth,
    );
  }

  @override
  void paintChildren(FlowPaintingContext context) {
    // Calculate the position of this list item within the viewport
    final scrollableBox = scrollable.context.findRenderObject() as RenderBox;
    final listItemBox = listItemContext.findRenderObject() as RenderBox;
    final listItemOffset = listItemBox.localToGlobal(
      listItemBox.size.centerLeft(Offset.zero),
      ancestor: scrollableBox,
    );

    // Determine the percent position of this list item within the
    // scrollable area
    final viewportDimension = scrollable.position.viewportDimension;
    final scrollFraction = (listItemOffset.dy / viewportDimension).clamp(0.0, 1.0);

    // Calculate the vertical alignment of the background
    // based on the scroll percent
    final verticalAlignment = parallaxFactor * (0.5 - scrollFraction);

    // Convert the vertical alignment into a pixel offset for
    // painting purposes
    final backgroundSize = context.getChildSize(0)!;
    final listItemSize = context.size;
    final childRect = Offset(
      0.0,
      verticalAlignment * (backgroundSize.height - listItemSize.height),
    ) & listItemSize;

    // Paint the background
    context.paintChild(
      0,
      transform: Transform.translate(
        offset: Offset(0.0, childRect.top),
      ).transform,
    );
  }

  @override
  bool shouldRepaint(ParallaxFlowDelegate oldDelegate) {
    return scrollable != oldDelegate.scrollable ||
        listItemContext != oldDelegate.listItemContext ||
        parallaxFactor != oldDelegate.parallaxFactor;
  }
}
