import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A button that scales when pressed
class AnimatedScaleButton extends StatefulWidget {
  /// The child widget
  final Widget child;

  /// Called when the button is tapped
  final VoidCallback? onTap;

  /// The scale factor when pressed
  final double scaleFactor;

  /// The duration of the scale animation
  final Duration duration;

  /// Whether to provide haptic feedback when pressed
  final bool hapticFeedback;

  /// The type of haptic feedback to provide
  final HapticFeedbackType hapticFeedbackType;

  /// Creates a new instance of [AnimatedScaleButton]
  const AnimatedScaleButton({
    super.key,
    required this.child,
    this.onTap,
    this.scaleFactor = 0.97,
    this.duration = const Duration(milliseconds: 150),
    this.hapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.light,
  });

  @override
  State<AnimatedScaleButton> createState() => _AnimatedScaleButtonState();
}

class _AnimatedScaleButtonState extends State<AnimatedScaleButton> {
  bool _isPressed = false;

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });

    if (widget.hapticFeedback) {
      switch (widget.hapticFeedbackType) {
        case HapticFeedbackType.light:
          HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          HapticFeedback.selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          HapticFeedback.vibrate();
          break;
      }
    }
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.onTap,
      child: AnimatedScale(
        scale: _isPressed ? widget.scaleFactor : 1.0,
        duration: widget.duration,
        curve: Curves.easeOutCubic,
        child: AnimatedOpacity(
          opacity: _isPressed ? 0.9 : 1.0,
          duration: widget.duration,
          child: widget.child,
        ),
      ),
    );
  }
}

/// Types of haptic feedback
enum HapticFeedbackType {
  /// Light impact
  light,

  /// Medium impact
  medium,

  /// Heavy impact
  heavy,

  /// Selection click
  selection,

  /// Vibrate
  vibrate,
}
