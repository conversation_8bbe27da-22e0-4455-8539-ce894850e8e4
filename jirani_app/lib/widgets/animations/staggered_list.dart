import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Base class for animation effects
abstract class Effect {
  /// Creates a new instance of [Effect]
  const Effect();

  /// Transforms a value based on the effect
  double transform(double value);
}

/// Fade effect
class FadeEffect extends Effect {
  /// The starting opacity
  final double begin;

  /// The ending opacity
  final double end;

  /// Creates a new instance of [FadeEffect]
  const FadeEffect({
    required this.begin,
    required this.end,
  });

  @override
  double transform(double value) {
    return begin * (1 - value) + end * value;
  }
}

/// Scale effect
class ScaleEffect extends Effect {
  /// The starting scale
  final double begin;

  /// The ending scale
  final double end;

  /// Creates a new instance of [ScaleEffect]
  const ScaleEffect({
    required this.begin,
    required this.end,
  });

  @override
  double transform(double value) {
    return begin * (1 - value) + end * value;
  }
}

/// Slide effect
class SlideEffect extends Effect {
  /// The starting offset
  final Offset begin;

  /// The ending offset
  final Offset end;

  /// Creates a new instance of [SlideEffect]
  const SlideEffect({
    required this.begin,
    required this.end,
  });

  @override
  double transform(double value) {
    // Not used directly, as the offset is calculated in the builder
    return value;
  }
}

/// A list that animates its items with a staggered animation
class StaggeredList extends StatelessWidget {
  /// The list of items to display
  final List<Widget> children;

  /// The delay between each item's animation
  final Duration staggerDelay;

  /// The duration of each item's animation
  final Duration itemDuration;

  /// The animation effects to apply to each item
  final List<Effect> effects;

  /// The scroll physics to use
  final ScrollPhysics? physics;

  /// Whether the list should be scrollable
  final bool shrinkWrap;

  /// The padding around the list
  final EdgeInsetsGeometry? padding;

  /// Creates a new instance of [StaggeredList]
  const StaggeredList({
    super.key,
    required this.children,
    this.staggerDelay = const Duration(milliseconds: 50),
    this.itemDuration = const Duration(milliseconds: 400),
    this.effects = const [
      FadeEffect(begin: 0, end: 1),
      SlideEffect(begin: Offset(0, 0.1), end: Offset.zero),
    ],
    this.physics,
    this.shrinkWrap = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      itemCount: children.length,
      itemBuilder: (context, index) {
        return children[index]
            .animate()
            .custom(
              duration: itemDuration,
              delay: staggerDelay * index,
              begin: 0,
              end: 1,
              builder: (context, value, child) {
                return Opacity(
                  opacity: effects.any((e) => e is FadeEffect)
                      ? effects
                          .whereType<FadeEffect>()
                          .first
                          .transform(value)
                      : 1,
                  child: Transform.translate(
                    offset: effects.any((e) => e is SlideEffect)
                        ? Offset(
                            effects
                                    .whereType<SlideEffect>()
                                    .first
                                    .begin
                                    .dx *
                                (1 - value) +
                                effects
                                    .whereType<SlideEffect>()
                                    .first
                                    .end
                                    .dx *
                                value,
                            effects
                                    .whereType<SlideEffect>()
                                    .first
                                    .begin
                                    .dy *
                                (1 - value) +
                                effects
                                    .whereType<SlideEffect>()
                                    .first
                                    .end
                                    .dy *
                                value,
                          )
                        : Offset.zero,
                    child: Transform.scale(
                      scale: effects.any((e) => e is ScaleEffect)
                          ? effects
                                  .whereType<ScaleEffect>()
                                  .first
                                  .begin *
                              (1 - value) +
                              effects.whereType<ScaleEffect>().first.end * value
                          : 1,
                      child: child,
                    ),
                  ),
                );
              },
            );
      },
    );
  }
}
