import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart';

/// Loading widget for displaying loading states
class AppLoadingWidget extends StatelessWidget {
  /// Loading message
  final String? message;
  
  /// Whether to show the message
  final bool showMessage;
  
  /// Creates a new instance of [AppLoadingWidget]
  const AppLoadingWidget({
    super.key,
    this.message,
    this.showMessage = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Loading indicator
          SizedBox(
            width: 48,
            height: 48,
            child: CircularProgressIndicator(
              strokeWidth: 4,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          )
          .animate()
          .scale(
            duration: 600.ms,
            curve: Curves.elasticOut,
            begin: const Offset(0.5, 0.5),
            end: const Offset(1.0, 1.0),
          ),
          
          if (showMessage && message != null) ...[
            const SizedBox(height: 16),
            
            // Loading message
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            )
            .animate()
            .fadeIn(
              duration: 600.ms,
              delay: 200.ms,
              curve: Curves.easeOut,
            ),
          ],
        ],
      ),
    );
  }
}

/// Shimmer loading widget for displaying loading states with shimmer effect
class ShimmerLoadingWidget extends StatelessWidget {
  /// Child widget
  final Widget child;
  
  /// Base color
  final Color? baseColor;
  
  /// Highlight color
  final Color? highlightColor;
  
  /// Creates a new instance of [ShimmerLoadingWidget]
  const ShimmerLoadingWidget({
    super.key,
    required this.child,
    this.baseColor,
    this.highlightColor,
  });
  
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Shimmer.fromColors(
      baseColor: baseColor ?? colorScheme.surfaceContainerHighest,
      highlightColor: highlightColor ?? colorScheme.surfaceContainerLow,
      child: child,
    );
  }
}

/// Skeleton list item for displaying loading states
class SkeletonListItem extends StatelessWidget {
  /// Whether to show a thumbnail
  final bool hasThumbnail;
  
  /// Number of lines
  final int lines;
  
  /// Creates a new instance of [SkeletonListItem]
  const SkeletonListItem({
    super.key,
    this.hasThumbnail = false,
    this.lines = 2,
  });
  
  @override
  Widget build(BuildContext context) {
    return ShimmerLoadingWidget(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasThumbnail) ...[
            // Thumbnail
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            
            const SizedBox(width: 16),
          ],
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Lines
                for (var i = 0; i < lines - 1; i++) ...[
                  Container(
                    width: double.infinity,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton card for displaying loading states
class SkeletonCard extends StatelessWidget {
  /// Width
  final double width;
  
  /// Height
  final double height;
  
  /// Creates a new instance of [SkeletonCard]
  const SkeletonCard({
    super.key,
    required this.width,
    required this.height,
  });
  
  @override
  Widget build(BuildContext context) {
    return ShimmerLoadingWidget(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

/// Skeleton grid item for displaying loading states
class SkeletonGridItem extends StatelessWidget {
  /// Creates a new instance of [SkeletonGridItem]
  const SkeletonGridItem({super.key});
  
  @override
  Widget build(BuildContext context) {
    return ShimmerLoadingWidget(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Title
          Container(
            width: double.infinity,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Subtitle
          Container(
            width: 100,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton loader for displaying loading states
class SkeletonLoader extends StatelessWidget {
  /// Width
  final double? width;
  
  /// Height
  final double? height;
  
  /// Border radius
  final BorderRadius? borderRadius;
  
  /// Creates a new instance of [SkeletonLoader]
  const SkeletonLoader({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
  });
  
  @override
  Widget build(BuildContext context) {
    return ShimmerLoadingWidget(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }
}
