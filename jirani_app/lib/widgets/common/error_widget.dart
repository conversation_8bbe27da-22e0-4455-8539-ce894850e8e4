import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../animations/animated_scale_button.dart';

/// Error widget for displaying errors
class AppErrorWidget extends StatelessWidget {
  /// Error message
  final String message;
  
  /// Error details
  final String? details;
  
  /// Retry callback
  final VoidCallback? onRetry;
  
  /// Whether to show the retry button
  final bool showRetry;
  
  /// Error icon
  final IconData icon;
  
  /// Creates a new instance of [AppErrorWidget]
  const AppErrorWidget({
    super.key,
    required this.message,
    this.details,
    this.onRetry,
    this.showRetry = true,
    this.icon = Icons.error_outline,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error icon
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            )
            .animate()
            .scale(
              duration: 600.ms,
              curve: Curves.elasticOut,
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.0, 1.0),
            ),
            
            const SizedBox(height: 16),
            
            // Error message
            Text(
              message,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            )
            .animate()
            .fadeIn(
              duration: 600.ms,
              delay: 200.ms,
              curve: Curves.easeOut,
            ),
            
            if (details != null) ...[
              const SizedBox(height: 8),
              
              // Error details
              Text(
                details!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                delay: 400.ms,
                curve: Curves.easeOut,
              ),
            ],
            
            if (showRetry && onRetry != null) ...[
              const SizedBox(height: 24),
              
              // Retry button
              AnimatedScaleButton(
                onTap: onRetry,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Retry',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                delay: 600.ms,
                curve: Curves.easeOut,
              )
              .slideY(
                begin: 0.5,
                end: 0,
                duration: 600.ms,
                delay: 600.ms,
                curve: Curves.easeOut,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Network error widget
class NetworkErrorWidget extends AppErrorWidget {
  /// Creates a new instance of [NetworkErrorWidget]
  const NetworkErrorWidget({
    super.key,
    super.onRetry,
    super.showRetry = true,
  }) : super(
    message: 'Network Error',
    details: 'Please check your internet connection and try again.',
    icon: Icons.wifi_off,
  );
}

/// Server error widget
class ServerErrorWidget extends AppErrorWidget {
  /// Creates a new instance of [ServerErrorWidget]
  const ServerErrorWidget({
    super.key,
    super.onRetry,
    super.showRetry = true,
  }) : super(
    message: 'Server Error',
    details: 'Something went wrong on our end. Please try again later.',
    icon: Icons.cloud_off,
  );
}

/// Not found error widget
class NotFoundErrorWidget extends AppErrorWidget {
  /// Creates a new instance of [NotFoundErrorWidget]
  const NotFoundErrorWidget({
    super.key,
    super.onRetry,
    super.showRetry = true,
  }) : super(
    message: 'Not Found',
    details: 'The requested resource could not be found.',
    icon: Icons.search_off,
  );
}

/// Permission error widget
class PermissionErrorWidget extends AppErrorWidget {
  /// Creates a new instance of [PermissionErrorWidget]
  const PermissionErrorWidget({
    super.key,
    super.onRetry,
    super.showRetry = true,
  }) : super(
    message: 'Permission Denied',
    details: 'You do not have permission to access this resource.',
    icon: Icons.no_accounts,
  );
}

/// Location permission error widget
class LocationPermissionErrorWidget extends AppErrorWidget {
  /// Creates a new instance of [LocationPermissionErrorWidget]
  const LocationPermissionErrorWidget({
    super.key,
    super.onRetry,
    super.showRetry = true,
  }) : super(
    message: 'Location Permission Required',
    details: 'Please enable location services to use this feature.',
    icon: Icons.location_off,
  );
}

/// Empty state widget
class EmptyStateWidget extends StatelessWidget {
  /// Message
  final String message;
  
  /// Details
  final String? details;
  
  /// Action button text
  final String? actionText;
  
  /// Action callback
  final VoidCallback? onAction;
  
  /// Icon
  final IconData icon;
  
  /// Creates a new instance of [EmptyStateWidget]
  const EmptyStateWidget({
    super.key,
    required this.message,
    this.details,
    this.actionText,
    this.onAction,
    this.icon = Icons.inbox,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            )
            .animate()
            .scale(
              duration: 600.ms,
              curve: Curves.elasticOut,
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.0, 1.0),
            ),
            
            const SizedBox(height: 16),
            
            // Message
            Text(
              message,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            )
            .animate()
            .fadeIn(
              duration: 600.ms,
              delay: 200.ms,
              curve: Curves.easeOut,
            ),
            
            if (details != null) ...[
              const SizedBox(height: 8),
              
              // Details
              Text(
                details!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                delay: 400.ms,
                curve: Curves.easeOut,
              ),
            ],
            
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 24),
              
              // Action button
              AnimatedScaleButton(
                onTap: onAction,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    actionText!,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              )
              .animate()
              .fadeIn(
                duration: 600.ms,
                delay: 600.ms,
                curve: Curves.easeOut,
              )
              .slideY(
                begin: 0.5,
                end: 0,
                duration: 600.ms,
                delay: 600.ms,
                curve: Curves.easeOut,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
