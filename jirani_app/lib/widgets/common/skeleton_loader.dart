import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// A skeleton loader for a grid item
class SkeletonGridItem extends StatelessWidget {
  /// The aspect ratio of the grid item
  final double aspectRatio;
  
  /// Whether to show a title
  final bool hasTitle;
  
  /// Whether to show a subtitle
  final bool hasSubtitle;
  
  /// The padding around the item
  final EdgeInsetsGeometry padding;

  /// Creates a new instance of [SkeletonGridItem]
  const SkeletonGridItem({
    super.key,
    this.aspectRatio = 1.0,
    this.hasTitle = true,
    this.hasSubtitle = false,
    this.padding = const EdgeInsets.all(8),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          AspectRatio(
            aspectRatio: aspectRatio,
            child: const SkeletonLoader(
              height: double.infinity,
              width: double.infinity,
            ),
          ),
          
          // Title
          if (hasTitle) ...[
            const SizedBox(height: 8),
            const SkeletonLoader(height: 16),
          ],
          
          // Subtitle
          if (hasSubtitle) ...[
            const SizedBox(height: 4),
            SkeletonLoader(
              height: 12,
              width: MediaQuery.of(context).size.width * 0.3,
            ),
          ],
        ],
      ),
    );
  }
}

/// A skeleton loader for a list item
class SkeletonListItem extends StatelessWidget {
  /// Whether to show an avatar
  final bool hasAvatar;
  
  /// Whether to show a thumbnail
  final bool hasThumbnail;
  
  /// The number of lines to show
  final int lines;
  
  /// The height of each line
  final double lineHeight;
  
  /// The spacing between lines
  final double lineSpacing;
  
  /// The padding around the item
  final EdgeInsetsGeometry padding;

  /// Creates a new instance of [SkeletonListItem]
  const SkeletonListItem({
    super.key,
    this.hasAvatar = false,
    this.hasThumbnail = false,
    this.lines = 2,
    this.lineHeight = 16,
    this.lineSpacing = 8,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Avatar
          if (hasAvatar) ...[
            const SkeletonLoader(
              height: 48,
              width: 48,
              borderRadius: BorderRadius.all(Radius.circular(24)),
            ),
            const SizedBox(width: 16),
          ],
          
          // Thumbnail
          if (hasThumbnail) ...[
            const SkeletonLoader(
              height: 80,
              width: 80,
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            const SizedBox(width: 16),
          ],
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                lines,
                (index) => Padding(
                  padding: EdgeInsets.only(
                    bottom: index == lines - 1 ? 0 : lineSpacing,
                  ),
                  child: SkeletonLoader(
                    height: lineHeight,
                    width: index == 0
                        ? double.infinity
                        : (index == lines - 1)
                            ? MediaQuery.of(context).size.width * 0.3
                            : MediaQuery.of(context).size.width * 0.6,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A skeleton loader widget
class SkeletonLoader extends StatelessWidget {
  /// The height of the skeleton
  final double height;
  
  /// The width of the skeleton
  final double? width;
  
  /// The border radius of the skeleton
  final BorderRadius borderRadius;
  
  /// The base color of the shimmer effect
  final Color baseColor;
  
  /// The highlight color of the shimmer effect
  final Color highlightColor;

  /// Creates a new instance of [SkeletonLoader]
  const SkeletonLoader({
    super.key,
    required this.height,
    this.width,
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
        ),
      ),
    );
  }
}
