import 'dart:ui';

import 'package:flutter/material.dart';

/// A container with a glassmorphic effect
class GlassmorphicContainer extends StatelessWidget {
  /// The width of the container
  final double width;

  /// The height of the container
  final double height;

  /// The border radius of the container
  final BorderRadius borderRadius;

  /// The blur amount
  final double blur;

  /// The opacity of the container
  final double opacity;

  /// The gradient colors
  final List<Color> gradientColors;

  /// The gradient begin alignment
  final Alignment gradientBegin;

  /// The gradient end alignment
  final Alignment gradientEnd;

  /// The border width
  final double borderWidth;

  /// The border color
  final Color borderColor;

  /// The child widget
  final Widget? child;

  /// The padding around the child
  final EdgeInsetsGeometry padding;

  /// Creates a new instance of [GlassmorphicContainer]
  const GlassmorphicContainer({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(24)),
    this.blur = 8,
    this.opacity = 0.15,
    this.gradientColors = const [
      Colors.white24,
      Colors.white10,
    ],
    this.gradientBegin = Alignment.topLeft,
    this.gradientEnd = Alignment.bottomRight,
    this.borderWidth = 0.5,
    this.borderColor = Colors.white30,
    this.child,
    this.padding = const EdgeInsets.all(12),
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: SizedBox(
        width: width,
        height: height,
        child: Stack(
          children: [
            // Blurred background
            BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: blur,
                sigmaY: blur,
              ),
              child: Container(
                width: width,
                height: height,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                ),
              ),
            ),

            // Gradient overlay
            Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                borderRadius: borderRadius,
                border: Border.all(
                  color: borderColor,
                  width: borderWidth,
                ),
                gradient: LinearGradient(
                  begin: gradientBegin,
                  end: gradientEnd,
                  colors: gradientColors,
                ),
              ),
            ),

            // Background color with opacity
            Opacity(
              opacity: opacity,
              child: Container(
                width: width,
                height: height,
                decoration: BoxDecoration(
                  borderRadius: borderRadius,
                  color: Colors.white,
                ),
              ),
            ),

            // Child widget
            if (child != null)
              Padding(
                padding: padding,
                child: child,
              ),
          ],
        ),
      ),
    );
  }
}
