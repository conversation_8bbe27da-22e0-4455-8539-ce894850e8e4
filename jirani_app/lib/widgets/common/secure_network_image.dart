import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../utils/image_url_helper.dart';

/// A network image widget that ensures URLs use the appropriate protocol
class SecureNetworkImage extends StatelessWidget {
  /// The image URL
  final String imageUrl;
  
  /// The width of the image
  final double? width;
  
  /// The height of the image
  final double? height;
  
  /// How to fit the image in the space
  final BoxFit fit;
  
  /// Widget to show while loading
  final Widget? placeholder;
  
  /// Widget to show on error
  final Widget? errorWidget;
  
  /// Whether to use cached network image
  final bool cached;
  
  /// Creates a secure network image
  const SecureNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.cached = true,
  });
  
  @override
  Widget build(BuildContext context) {
    final secureUrl = ImageUrlHelper.getImageUrl(imageUrl);
    
    // Use cached network image if requested
    if (cached) {
      return CachedNetworkImage(
        imageUrl: secureUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => placeholder ?? Container(
          color: Colors.grey[300],
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => errorWidget ?? Container(
          color: Colors.grey[300],
          child: const Icon(Icons.error),
        ),
      );
    }
    
    // Use regular network image
    return Image.network(
      secureUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return placeholder ?? Container(
          color: Colors.grey[300],
          width: width,
          height: height,
          child: Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded / 
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? Container(
          color: Colors.grey[300],
          width: width,
          height: height,
          child: const Icon(Icons.error),
        );
      },
    );
  }
}

/// A circle avatar with a secure network image
class SecureNetworkCircleAvatar extends StatelessWidget {
  /// The image URL
  final String? imageUrl;
  
  /// The radius of the avatar
  final double radius;
  
  /// The background color of the avatar
  final Color? backgroundColor;
  
  /// The widget to show if no image URL is provided
  final Widget? child;
  
  /// Creates a secure network circle avatar
  const SecureNetworkCircleAvatar({
    super.key,
    this.imageUrl,
    required this.radius,
    this.backgroundColor,
    this.child,
  });
  
  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor,
        child: child,
      );
    }
    
    final secureUrl = ImageUrlHelper.getImageUrl(imageUrl!);
    
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor,
      backgroundImage: CachedNetworkImageProvider(secureUrl),
      child: null,
    );
  }
}
