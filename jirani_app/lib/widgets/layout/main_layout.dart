import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// Main layout with bottom navigation
class MainLayout extends StatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Creates a new instance of [MainLayout]
  const MainLayout({
    super.key,
    required this.child,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateIndexFromLocation(GoRouterState.of(context).uri.path);
  }

  void _updateIndexFromLocation(String location) {
    if (location.startsWith('/home')) {
      setState(() => _currentIndex = 0);
    } else if (location.startsWith('/explore')) {
      setState(() => _currentIndex = 1);
    } else if (location.startsWith('/profile')) {
      setState(() => _currentIndex = 2);
    }
  }

  void _onItemTapped(int index) {
    HapticFeedback.selectionClick();
    
    if (index == _currentIndex) {
      return;
    }
    
    setState(() => _currentIndex = index);
    
    switch (index) {
      case 0:
        context.goNamed('home');
        break;
      case 1:
        context.goNamed('explore');
        break;
      case 2:
        context.goNamed('profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show bottom navigation on details screen
    final location = GoRouterState.of(context).uri.path;
    final showBottomNav = !location.startsWith('/details');
    
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: showBottomNav
          ? NavigationBar(
              selectedIndex: _currentIndex,
              onDestinationSelected: _onItemTapped,
              destinations: const [
                NavigationDestination(
                  icon: Icon(Icons.home_outlined),
                  selectedIcon: Icon(Icons.home),
                  label: 'Home',
                ),
                NavigationDestination(
                  icon: Icon(Icons.explore_outlined),
                  selectedIcon: Icon(Icons.explore),
                  label: 'Explore',
                ),
                NavigationDestination(
                  icon: Icon(Icons.person_outline),
                  selectedIcon: Icon(Icons.person),
                  label: 'Profile',
                ),
              ],
            )
          : null,
    );
  }
}
