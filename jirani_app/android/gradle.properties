org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Mapbox SDK token
MAPBOX_DOWNLOADS_TOKEN=********************************************************************************************

# Disable deprecation warnings
android.suppressUnsupportedCompileSdk=35
android.lint.ignoreWarnings=true
