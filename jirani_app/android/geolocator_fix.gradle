// This file is used to fix the geolocator plugin build issues
// It will be applied to all plugins in the project

allprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            if (project.name == 'geolocator_android' || project.name.contains('geolocator')) {
                android {
                    compileSdkVersion 35

                    defaultConfig {
                        minSdkVersion 21
                        targetSdkVersion 35
                    }

                    // Add namespace configuration
                    namespace 'com.baseflow.geolocator'

                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
            }
        }
    }
}
