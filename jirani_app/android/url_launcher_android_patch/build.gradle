group 'io.flutter.plugins.urllauncher'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    buildFeatures {
        buildConfig true
    }
    namespace 'io.flutter.plugins.urllauncher'
    compileSdk 33

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }
}

dependencies {
    implementation "androidx.core:core:1.9.0"
    implementation 'androidx.annotation:annotation:1.5.0'
    implementation 'androidx.browser:browser:1.5.0'
}
