group 'com.baseflow.geolocator'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace "com.baseflow.geolocator"
    compileSdk 35

    defaultConfig {
        minSdk 21
        targetSdk 35
    }

    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'com.google.android.gms:play-services-location:21.2.0'
    implementation 'androidx.core:core:1.13.0'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.1.1'
}
