// This file is used to fix namespace issues for all plugins
// It will be applied to all plugins in the project

// Define a map of plugin names to their namespaces
def pluginNamespaces = [
    'background_locator_2': 'yukams.app.background_locator_2',
    'geolocator_android': 'com.baseflow.geolocator',
    'geocoding_android': 'com.baseflow.geocoding',
    'location': 'com.lyokone.location',
    'flutter_plugin_android_lifecycle': 'io.flutter.plugins.flutter_plugin_android_lifecycle',
    'url_launcher_android': 'io.flutter.plugins.urllauncher',
    'path_provider_android': 'io.flutter.plugins.pathprovider',
    'shared_preferences_android': 'io.flutter.plugins.sharedpreferences',
    'sqflite': 'com.tekartik.sqflite',
    'google_maps_flutter_android': 'io.flutter.plugins.googlemaps'
]

// Special fix for geolocator_android
project.ext {
    flutterCompileSdkVersion = 35
    flutterMinSdkVersion = 21
    flutterTargetSdkVersion = 35
}

allprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            // Special handling for geolocator_android
            if (project.name == 'geolocator_android') {
                android {
                    namespace "com.baseflow.geolocator"
                    compileSdk 35

                    defaultConfig {
                        minSdk 21
                        targetSdk 35
                    }

                    lintOptions {
                        disable 'InvalidPackage'
                        abortOnError false
                    }

                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
            }
            // Check if the current project needs a namespace
            else if (pluginNamespaces.containsKey(project.name)) {
                android {
                    compileSdkVersion 35

                    defaultConfig {
                        minSdkVersion 21
                        targetSdkVersion 35
                    }

                    // Add namespace configuration
                    namespace pluginNamespaces[project.name]

                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
            }

            // Apply to all projects
            android {
                // Disable deprecation warnings for all plugins
                tasks.withType(JavaCompile) {
                    options.compilerArgs << '-Xlint:none'
                    options.compilerArgs << '-nowarn'
                    options.compilerArgs << '-Xlint:-deprecation'
                    options.compilerArgs << '-Xlint:-unchecked'
                }

                lintOptions {
                    abortOnError false
                    checkReleaseBuilds false
                    disable 'InvalidPackage', 'DeprecatedApi', 'ObsoleteSdkInt', 'DeprecatedSdkInt'
                }
            }

            // Fix Kotlin JVM target compatibility for all projects
            if (project.plugins.hasPlugin('kotlin-android')) {
                project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = '1.8'
                    }
                }
            }
        }
    }
}
