// This file is used to fix the background_locator_2 plugin build issues
// It will be applied to all plugins in the project

allprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            if (project.name == 'background_locator_2') {
                android {
                    compileSdkVersion 35
                    
                    defaultConfig {
                        minSdkVersion 21
                        targetSdkVersion 35
                    }
                    
                    // Add namespace configuration
                    namespace 'yukams.app.background_locator_2'
                    
                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
            }
        }
    }
}
