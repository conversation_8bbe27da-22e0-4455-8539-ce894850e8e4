name: jirani_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management
  flutter_riverpod: ^2.4.10
  provider: ^6.1.2

  # Navigation
  go_router: ^15.1.2

  # UI and Animations
  flutter_animate: ^4.5.0
  google_fonts: ^6.2.0
  skeletonizer: ^1.0.1
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0

  # Network and API
  http: ^1.2.1
  dio: ^5.4.1
  web_socket_channel: ^2.4.0

  # Local storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0

  # Maps and location
  google_maps_flutter: ^2.6.0
  location: ^7.0.1
  # Using the official Mapbox SDK instead of mapbox_gl
  mapbox_maps_flutter: ^2.0.0
  mapbox_search: ^4.3.1
  # Using location package instead of geolocator
  geocoding: ^3.0.0
  flutter_polyline_points: ^2.0.0

  # Utilities
  intl: ^0.20.2
  logger: ^2.5.0
  url_launcher: ^6.2.5
  image_picker: ^1.0.7
  flutter_cache_manager: ^3.4.1
  path_provider: ^2.1.5
  connectivity_plus: ^6.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # App icon generator
  flutter_launcher_icons: ^0.13.1

# Flutter launcher icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/jirani_logo.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/jirani_logo.png"
  web:
    generate: true
    image_path: "assets/images/jirani_logo.png"
    background_color: "#FFFFFF"
    theme_color: "#FFFFFF"

# Override dependencies to ensure compatibility
dependency_overrides:
  flutter_plugin_android_lifecycle: '2.0.15'

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/jirani/
    - assets/jirani/favicon/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
