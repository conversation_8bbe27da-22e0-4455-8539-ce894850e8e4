{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "geocoding_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/", "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": []}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/", "native_build": true, "dependencies": []}, {"name": "mapbox_maps_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}], "android": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.15/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "geocoding_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/", "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/", "native_build": true, "dependencies": []}, {"name": "mapbox_maps_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/", "native_build": true, "dependencies": []}], "macos": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": []}, {"name": "file_selector_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}], "web": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": []}, {"name": "google_maps_flutter_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": []}, {"name": "location_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.4/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/", "dependencies": []}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "location", "dependencies": ["location_web"]}, {"name": "location_web", "dependencies": []}, {"name": "mapbox_maps_flutter", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-04 18:40:25.544101", "version": "3.24.3", "swift_package_manager_enabled": false}