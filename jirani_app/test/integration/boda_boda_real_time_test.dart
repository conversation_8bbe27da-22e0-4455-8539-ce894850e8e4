import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';
import 'package:jirani_app/main.dart' as app;
import 'package:jirani_app/models/websocket_message.dart';
import 'package:jirani_app/models/lat_lng.dart' as custom;
import 'package:jirani_app/services/websocket_service.dart';
import 'package:jirani_app/services/geofencing_service.dart';
import 'package:jirani_app/services/notification_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('BodaBoda Real-time Integration Tests', () {
    testWidgets('Complete ride flow with real-time updates', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Test WebSocket connection
      await _testWebSocketConnection(tester);
      
      // Test geofencing functionality
      await _testGeofencing(tester);
      
      // Test notification system
      await _testNotifications(tester);
      
      // Test complete ride flow
      await _testCompleteRideFlow(tester);
    });

    testWidgets('WebSocket message handling', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      final container = ProviderContainer();
      final webSocketService = WebSocketService();

      // Test different message types
      final testMessages = [
        WebSocketMessage.rideStatusUpdate(
          rideId: 'test_ride_123',
          status: 'rider_assigned',
          additionalData: {'rider_name': 'John Doe'},
        ),
        WebSocketMessage.driverLocation(
          latitude: -1.286389,
          longitude: 36.817223,
          driverId: 'driver_123',
          rideId: 'test_ride_123',
        ),
        WebSocketMessage.chat(
          rideId: 'test_ride_123',
          message: 'I am on my way',
          fromUserId: 'driver_123',
          toUserId: 'user_123',
          fromRole: 'driver',
        ),
        WebSocketMessage.notification(
          title: 'Rider Arriving',
          body: 'Your rider will arrive in 2 minutes',
          additionalData: {'estimated_time': '2 minutes'},
        ),
      ];

      for (final message in testMessages) {
        // Simulate receiving message
        expect(message.type, isNotNull);
        expect(message.timestamp, isNotNull);
        expect(message.data, isNotEmpty);
      }

      container.dispose();
    });

    testWidgets('Geofencing accuracy test', (WidgetTester tester) async {
      final geofencingService = GeofencingService();
      
      // Create test geofence
      final pickupLocation = custom.LatLng(-1.286389, 36.817223);
      final geofence = GeofencingService.createPickupGeofence('test_ride', pickupLocation);
      
      geofencingService.addGeofence(geofence);

      // Test locations
      final testLocations = [
        custom.LatLng(-1.286389, 36.817223), // Exact center
        custom.LatLng(-1.286400, 36.817230), // Slightly off center (should be inside)
        custom.LatLng(-1.287000, 36.818000), // Outside geofence
      ];

      final events = <GeofenceEvent>[];
      geofencingService.eventStream.listen((event) {
        events.add(event);
      });

      // Simulate location updates
      for (final location in testLocations) {
        geofencingService.updateLocation(location);
        await tester.pump(const Duration(milliseconds: 100));
      }

      // Verify events were triggered
      expect(events.length, greaterThan(0));
      
      geofencingService.dispose();
    });

    testWidgets('Notification system test', (WidgetTester tester) async {
      final notificationService = NotificationService();
      await notificationService.initialize();

      final notifications = <RideNotification>[];
      notificationService.notificationStream.listen((notification) {
        notifications.add(notification);
      });

      // Test different notification types
      final testNotifications = [
        NotificationService.createRideStatusNotification(
          status: 'rider_assigned',
          riderName: 'John Doe',
        ),
        NotificationService.createRideStatusNotification(
          status: 'rider_arriving',
          estimatedTime: '5 minutes',
        ),
        NotificationService.createEmergencyNotification(
          message: 'Emergency alert triggered',
        ),
      ];

      for (final notification in testNotifications) {
        await notificationService.showRideNotification(notification);
        await tester.pump(const Duration(milliseconds: 100));
      }

      // Verify notifications were processed
      expect(notifications.length, equals(testNotifications.length));
      
      notificationService.dispose();
    });
  });
}

Future<void> _testWebSocketConnection(WidgetTester tester) async {
  // Test WebSocket connection establishment
  final webSocketService = WebSocketService();
  
  // Note: In a real test, you would connect to a test WebSocket server
  // For this example, we're testing the service structure
  
  expect(webSocketService.isConnected, isFalse);
  
  // Test message creation
  final testMessage = WebSocketMessage.heartbeat();
  expect(testMessage.type, equals(WebSocketMessageType.heartbeat));
  expect(testMessage.data['status'], equals('alive'));
}

Future<void> _testGeofencing(WidgetTester tester) async {
  final geofencingService = GeofencingService();
  final rideGeofencingManager = RideGeofencingManager();
  
  // Test geofence setup
  final pickupLocation = custom.LatLng(-1.286389, 36.817223);
  final destinationLocation = custom.LatLng(-1.296389, 36.827223);
  
  rideGeofencingManager.setupRideGeofences(
    'test_ride_123',
    pickupLocation,
    destinationLocation,
  );
  
  // Test location updates
  rideGeofencingManager.updateLocation(pickupLocation);
  
  // Cleanup
  rideGeofencingManager.cleanupRideGeofences('test_ride_123');
  rideGeofencingManager.dispose();
}

Future<void> _testNotifications(WidgetTester tester) async {
  final notificationService = NotificationService();
  await notificationService.initialize();
  
  // Test notification creation and display
  final notification = NotificationService.createRideStatusNotification(
    status: 'rider_assigned',
    riderName: 'Test Rider',
  );
  
  expect(notification.type, equals(RideNotificationType.riderAssigned));
  expect(notification.title, equals('Rider Assigned'));
  expect(notification.body, contains('Test Rider'));
  
  await notificationService.showRideNotification(notification);
  
  notificationService.dispose();
}

Future<void> _testCompleteRideFlow(WidgetTester tester) async {
  // This would test the complete ride flow from request to completion
  // Including navigation between screens, real-time updates, and user interactions
  
  // 1. Navigate to BodaBoda feature
  await tester.tap(find.text('BodaBoda'));
  await tester.pumpAndSettle();
  
  // 2. Test destination selection
  // (This would require mocking location services and Mapbox)
  
  // 3. Test fare estimation
  // (This would require mocking the fare calculation API)
  
  // 4. Test ride request creation
  // (This would require mocking the ride creation API)
  
  // 5. Test real-time tracking
  // (This would require mocking WebSocket messages)
  
  // 6. Test ride completion
  // (This would require mocking the completion flow)
  
  // For now, we'll just verify the basic structure exists
  expect(find.byType(MaterialApp), findsOneWidget);
}

/// Mock WebSocket message for testing
class MockWebSocketMessage extends WebSocketMessage {
  const MockWebSocketMessage({
    required super.type,
    super.userId,
    super.rideId,
    required super.data,
    required super.timestamp,
  });
  
  factory MockWebSocketMessage.riderAssigned(String rideId) {
    return MockWebSocketMessage(
      type: WebSocketMessageType.rideStatusUpdate,
      rideId: rideId,
      data: {
        'status': 'rider_assigned',
        'rider': {
          'id': 'rider_123',
          'name': 'John Doe',
          'rating': 4.8,
          'vehicle': 'Honda CB 150',
          'license_plate': 'KCA 123A',
        },
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }
  
  factory MockWebSocketMessage.driverLocationUpdate(String rideId) {
    return MockWebSocketMessage(
      type: WebSocketMessageType.driverLocation,
      rideId: rideId,
      data: {
        'latitude': -1.286389,
        'longitude': 36.817223,
        'driver_id': 'rider_123',
        'speed': 25.5,
        'heading': 180.0,
      },
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }
}

/// Test utilities
class TestUtils {
  static Future<void> waitForAnimation(WidgetTester tester) async {
    await tester.pump(const Duration(milliseconds: 100));
    await tester.pumpAndSettle();
  }
  
  static Future<void> simulateNetworkDelay(WidgetTester tester) async {
    await tester.pump(const Duration(milliseconds: 500));
  }
  
  static Finder findByTextContaining(String text) {
    return find.byWidgetPredicate(
      (widget) => widget is Text && widget.data?.contains(text) == true,
    );
  }
}
