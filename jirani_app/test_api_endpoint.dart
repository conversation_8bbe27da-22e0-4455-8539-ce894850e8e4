import 'package:http/http.dart' as http;

/// Test script to verify the API endpoint configuration
void main() async {
  // Define the production API URL directly
  final apiUrl = 'https://api.jirani.tufiked.live/api/v1';

  print('Testing API endpoint: $apiUrl');

  // Test the API health endpoint
  print('Testing API health endpoint...');
  try {
    final response = await http.get(Uri.parse('$apiUrl/health'));
    if (response.statusCode == 200) {
      print('✅ API is healthy: ${response.body}');
    } else {
      print(
          '❌ API health check failed with status code: ${response.statusCode}');
      print('Response body: ${response.body}');
    }
  } catch (e) {
    print('❌ Error connecting to API: $e');
  }
}
