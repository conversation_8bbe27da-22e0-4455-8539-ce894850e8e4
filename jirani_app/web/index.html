<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Jirani App">
  <link rel="apple-touch-icon" href="apple-touch-icon.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="96x96" href="favicon.png"/>
  <link rel="icon" type="image/x-icon" href="favicon.ico"/>

  <title>Jirani App</title>
  <link rel="manifest" href="manifest.json">

  <!-- Mapbox GL JS -->
  <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
  <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />

  <!-- Geolocator plugin -->
  <script src="https://unpkg.com/@turf/turf@6/turf.min.js"></script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
