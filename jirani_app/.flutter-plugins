# This is a generated file; do not edit or check into version control.
connectivity_plus=/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
file_selector_linux=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/
file_selector_windows=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
flutter_plugin_android_lifecycle=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.15/
flutter_secure_storage=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
geocoding=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0/
geocoding_android=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/
geocoding_ios=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/
google_maps_flutter=/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.1/
google_maps_flutter_android=/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/
google_maps_flutter_ios=/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/
google_maps_flutter_web=/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/
image_picker=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+21/
image_picker_for_web=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
location=/home/<USER>/.pub-cache/hosted/pub.dev/location-7.0.1/
location_web=/home/<USER>/.pub-cache/hosted/pub.dev/location_web-5.0.4/
mapbox_maps_flutter=/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.8.0/
path_provider=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/
path_provider_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
shared_preferences=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/
shared_preferences_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
url_launcher=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/
url_launcher_ios=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
url_launcher_windows=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
