<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.18.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio_web_adapter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="/usr/local/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_animate">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_riverpod">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_shaders">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="/usr/local/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="/usr/local/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="go_router">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_fonts">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps-8.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.19.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/lints-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="location">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/location-5.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="location_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/location_platform_interface-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="location_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/location_web-4.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="logger">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/logger-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.15.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sanitize_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shimmer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="skeletonizer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="/usr/local/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="state_notifier">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-14.2.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.11.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.18.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_animate-4.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps-8.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter-2.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.19.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/lints-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/location-5.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/location_platform_interface-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/location_web-4.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/logger-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.15.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-14.2.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file:///usr/local/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file:///usr/local/flutter/packages/flutter/lib" />
      <root url="file:///usr/local/flutter/packages/flutter_test/lib" />
      <root url="file:///usr/local/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>