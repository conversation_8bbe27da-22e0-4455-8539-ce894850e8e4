import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

import 'lib/services/websocket_service.dart';
import 'lib/services/enhanced_location_service.dart';
import 'lib/services/emergency_service.dart';
import 'lib/services/error_recovery_service.dart';
import 'lib/models/websocket_message.dart';
import 'lib/models/ride.dart';

/// Production feature validation script
/// Tests all implemented real-time features
class ProductionFeatureValidator {
  final WebSocketService _webSocketService = WebSocketService();
  final EnhancedLocationService _locationService = EnhancedLocationService();
  final EmergencyService _emergencyService = EmergencyService();
  final ErrorRecoveryService _errorRecoveryService = ErrorRecoveryService();

  /// Run comprehensive validation tests
  Future<ValidationReport> runValidation() async {
    final report = ValidationReport();
    
    print('🚀 Starting Production Feature Validation...\n');
    
    try {
      // Test 1: WebSocket Connection Management
      await _testWebSocketConnection(report);
      
      // Test 2: Enhanced Location Services
      await _testLocationServices(report);
      
      // Test 3: Real-time Message Handling
      await _testMessageHandling(report);
      
      // Test 4: Emergency System
      await _testEmergencySystem(report);
      
      // Test 5: Error Recovery
      await _testErrorRecovery(report);
      
      // Test 6: Performance Optimization
      await _testPerformanceOptimization(report);
      
    } catch (e) {
      report.addError('Critical validation error: $e');
    }
    
    return report;
  }

  /// Test WebSocket connection management
  Future<void> _testWebSocketConnection(ValidationReport report) async {
    print('📡 Testing WebSocket Connection Management...');
    
    try {
      // Test connection with retry
      await _webSocketService.connectWithRetry(maxRetries: 3);
      
      if (_webSocketService.isConnected) {
        report.addSuccess('✅ WebSocket connection established');
      } else {
        report.addError('❌ WebSocket connection failed');
        return;
      }
      
      // Test connection health check
      await _webSocketService.ensureConnection();
      report.addSuccess('✅ Connection health check passed');
      
      // Test connection statistics
      final stats = _webSocketService.getConnectionStatistics();
      if (stats.isNotEmpty) {
        report.addSuccess('✅ Connection statistics available');
        print('   📊 Connection Stats: ${stats.length} metrics');
      }
      
    } catch (e) {
      report.addError('❌ WebSocket test failed: $e');
    }
    
    print('');
  }

  /// Test enhanced location services
  Future<void> _testLocationServices(ValidationReport report) async {
    print('📍 Testing Enhanced Location Services...');
    
    try {
      // Initialize location service
      await _locationService.initialize();
      report.addSuccess('✅ Location service initialized');
      
      // Test current location
      final location = await _locationService.getCurrentLocation(
        timeout: const Duration(seconds: 10),
      );
      
      if (location.latitude != null && location.longitude != null) {
        report.addSuccess('✅ Current location retrieved');
        print('   📍 Location: ${location.latitude}, ${location.longitude}');
      } else {
        report.addWarning('⚠️ Location data incomplete');
      }
      
      // Test performance optimization
      await _locationService.optimizeForBattery();
      report.addSuccess('✅ Battery optimization applied');
      
      await _locationService.optimizeForAccuracy();
      report.addSuccess('✅ Accuracy optimization applied');
      
      // Test adaptive tracking
      await _locationService.adaptiveLocationTracking(
        isRideActive: true,
        isEmergency: false,
      );
      report.addSuccess('✅ Adaptive location tracking configured');
      
      // Test performance metrics
      final metrics = _locationService.getPerformanceMetrics();
      if (metrics.isNotEmpty) {
        report.addSuccess('✅ Performance metrics available');
        print('   📊 Performance Metrics: ${metrics.length} metrics');
      }
      
    } catch (e) {
      report.addError('❌ Location service test failed: $e');
    }
    
    print('');
  }

  /// Test real-time message handling
  Future<void> _testMessageHandling(ValidationReport report) async {
    print('💬 Testing Real-time Message Handling...');
    
    try {
      // Test message routing
      final testMessage = WebSocketMessage(
        type: WebSocketMessageType.locationUpdate,
        rideId: 'test-ride-123',
        data: {
          'latitude': -1.2921,
          'longitude': 36.8219,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );
      
      // Test high priority message
      _webSocketService.sendHighPriorityMessage(testMessage);
      report.addSuccess('✅ High priority message sent');
      
      // Test message routing by ride ID
      final rideRouter = _webSocketService.getMessageRouterByRideId('test-ride-123');
      if (rideRouter != null) {
        report.addSuccess('✅ Message routing by ride ID working');
      }
      
      // Test message routing by type
      final typeRouter = _webSocketService.getMessageRouterByType(WebSocketMessageType.locationUpdate);
      if (typeRouter != null) {
        report.addSuccess('✅ Message routing by type working');
      }
      
    } catch (e) {
      report.addError('❌ Message handling test failed: $e');
    }
    
    print('');
  }

  /// Test emergency system
  Future<void> _testEmergencySystem(ValidationReport report) async {
    print('🚨 Testing Emergency System...');
    
    try {
      // Test emergency alert (without actually triggering)
      if (!_emergencyService.isEmergencyActive) {
        report.addSuccess('✅ Emergency system ready');
      }
      
      // Test emergency types
      final emergencyTypes = [
        EmergencyType.medical,
        EmergencyType.accident,
        EmergencyType.security,
        EmergencyType.breakdown,
      ];
      
      for (final type in emergencyTypes) {
        if (type.value.isNotEmpty) {
          report.addSuccess('✅ Emergency type ${type.value} configured');
        }
      }
      
      print('   🚨 Emergency system validated (no actual alerts sent)');
      
    } catch (e) {
      report.addError('❌ Emergency system test failed: $e');
    }
    
    print('');
  }

  /// Test error recovery system
  Future<void> _testErrorRecovery(ValidationReport report) async {
    print('🔧 Testing Error Recovery System...');
    
    try {
      // Initialize error recovery
      await _errorRecoveryService.initialize();
      report.addSuccess('✅ Error recovery service initialized');
      
      // Test error handling (simulate)
      await _errorRecoveryService.handleError(
        'test_error',
        'Simulated error for testing',
        shouldRecover: false, // Don't actually recover
      );
      report.addSuccess('✅ Error handling mechanism working');
      
      // Test error statistics
      final stats = _errorRecoveryService.getErrorStatistics();
      if (stats.isNotEmpty) {
        report.addSuccess('✅ Error statistics available');
        print('   📊 Error Stats: ${stats.length} metrics');
      }
      
    } catch (e) {
      report.addError('❌ Error recovery test failed: $e');
    }
    
    print('');
  }

  /// Test performance optimization
  Future<void> _testPerformanceOptimization(ValidationReport report) async {
    print('⚡ Testing Performance Optimization...');
    
    try {
      // Test WebSocket performance
      final wsStats = _webSocketService.getConnectionStatistics();
      if (wsStats.containsKey('messagesQueued')) {
        report.addSuccess('✅ WebSocket message queuing active');
      }
      
      // Test location performance
      final locationMetrics = _locationService.getPerformanceMetrics();
      if (locationMetrics.containsKey('cached_locations')) {
        report.addSuccess('✅ Location caching active');
      }
      
      // Test memory optimization
      _locationService.clearLocationCache();
      report.addSuccess('✅ Memory optimization available');
      
    } catch (e) {
      report.addError('❌ Performance optimization test failed: $e');
    }
    
    print('');
  }

  /// Cleanup resources
  void dispose() {
    _webSocketService.dispose();
    _locationService.dispose();
    _errorRecoveryService.dispose();
  }
}

/// Validation report class
class ValidationReport {
  final List<String> successes = [];
  final List<String> warnings = [];
  final List<String> errors = [];

  void addSuccess(String message) => successes.add(message);
  void addWarning(String message) => warnings.add(message);
  void addError(String message) => errors.add(message);

  void printReport() {
    print('📋 VALIDATION REPORT');
    print('=' * 50);
    print('✅ Successes: ${successes.length}');
    print('⚠️  Warnings: ${warnings.length}');
    print('❌ Errors: ${errors.length}');
    print('');
    
    if (successes.isNotEmpty) {
      print('✅ SUCCESSES:');
      for (final success in successes) {
        print('   $success');
      }
      print('');
    }
    
    if (warnings.isNotEmpty) {
      print('⚠️  WARNINGS:');
      for (final warning in warnings) {
        print('   $warning');
      }
      print('');
    }
    
    if (errors.isNotEmpty) {
      print('❌ ERRORS:');
      for (final error in errors) {
        print('   $error');
      }
      print('');
    }
    
    final totalTests = successes.length + warnings.length + errors.length;
    final successRate = totalTests > 0 ? (successes.length / totalTests * 100).toStringAsFixed(1) : '0.0';
    
    print('📊 SUMMARY:');
    print('   Total Tests: $totalTests');
    print('   Success Rate: $successRate%');
    print('   Status: ${errors.isEmpty ? '🟢 PASSED' : '🔴 FAILED'}');
  }

  bool get isPassed => errors.isEmpty;
}

/// Main validation function
Future<void> main() async {
  final validator = ProductionFeatureValidator();
  
  try {
    final report = await validator.runValidation();
    report.printReport();
    
    if (report.isPassed) {
      print('\n🎉 ALL PRODUCTION FEATURES VALIDATED SUCCESSFULLY!');
    } else {
      print('\n⚠️  SOME FEATURES NEED ATTENTION');
    }
    
  } catch (e) {
    print('💥 Validation failed with critical error: $e');
  } finally {
    validator.dispose();
  }
}
