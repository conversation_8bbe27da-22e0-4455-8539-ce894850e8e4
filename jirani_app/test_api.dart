import 'package:http/http.dart' as http;
import 'lib/services/api_config.dart';

void main() async {
  // Get the base URL from the API config
  final baseUrl = ApiConfig.getBaseUrl();

  // Test the API health endpoint
  print('Testing API health endpoint at $baseUrl...');
  try {
    final response = await http.get(Uri.parse('$baseUrl/health'));
    if (response.statusCode == 200) {
      print('API is healthy: ${response.body}');
    } else {
      print('API health check failed with status code: ${response.statusCode}');
      print('Response body: ${response.body}');
    }
  } catch (e) {
    print('Error connecting to API: $e');
  }
}
