# BodaBoda AI Implementation Context for Augment AI

## 1. AI Implementation Overview
This document provides comprehensive AI implementation guidelines for the BodaBoda feature development in the Jirani app. It ensures consistent, high-quality AI-driven development with specific file references, code snippets, and validation steps.

## 2. AI Implementation Principles

### 2.1 AI Development Philosophy
- **AI-First Implementation**: Every component must be implemented by AI with exact file paths and code snippets
- **Validation-Driven AI**: All AI implementations must pass predefined validation gates before proceeding
- **Performance-Conscious AI**: AI must achieve real-time features with sub-100ms response times
- **Security-First AI**: AI must implement encrypted data transmission and validation
- **User-Centric AI**: AI must enhance user experience and safety in every implementation
- **Augment-Compatible**: All file references use @ notation for seamless AI file access

### 2.2 Architecture Context
```
Frontend (Flutter) ↔ WebSocket Hub ↔ Backend (Go Fiber) ↔ Database (PostgreSQL)
                  ↕                                      ↕
            Mapbox APIs                              GCP Services
```

### 2.3 Technology Stack Context
- **Frontend**: Flutter 3.x with Riverpod state management
- **Backend**: Go Fiber framework with WebSocket support
- **Database**: PostgreSQL with GORM ORM
- **Real-time**: WebSocket connections with auto-reconnection
- **Maps**: Mapbox SDK for Flutter with custom styling
- **Cloud**: Google Cloud Platform with Docker deployment
- **Authentication**: JWT tokens with refresh mechanism
- **AI Assistant**: Augment AI with @ file referencing system

## 3. AI Implementation Rules

### 3.1 AI Flutter Implementation Context

#### AI State Management Implementation
**AI Action:** When implementing state management, AI must:
- Create Riverpod providers in `@jirani_app/lib/providers/` directory
- Use exact provider patterns: `StateNotifierProvider`, `FutureProvider`, `StreamProvider`
- Implement proper disposal in provider classes
- Add provider to main.dart ProviderScope
- Test provider functionality before proceeding

#### AI UI Implementation Rules
**AI Action:** When implementing UI components, AI must:
- Create widgets in `@jirani_app/lib/widgets/` or screen-specific directories
- Implement loading states with CircularProgressIndicator
- Add error handling with user-friendly messages
- Use AnimationController for smooth transitions
- Test UI responsiveness on different screen sizes

#### AI Memory Management Implementation
**AI Action:** When implementing components, AI must:
- Add dispose() methods to all StatefulWidgets
- Use AutoDisposeProvider for temporary state
- Implement proper controller disposal
- Add memory leak detection in debug mode
- Validate memory usage after implementation

#### AI Battery Optimization Implementation
**AI Action:** When implementing location features, AI must:
- Use configurable location accuracy modes
- Implement motion-based location updates
- Add background processing optimization
- Use efficient WebSocket connection management
- Test battery impact and optimize accordingly

### 3.2 AI Go Fiber Backend Implementation Context

#### AI API Implementation Rules
**AI Action:** When implementing backend APIs, AI must:
- Create handlers in `@jiranibackend/internal/handlers/` directory
- Follow RESTful patterns with proper HTTP status codes
- Implement input validation using struct tags
- Add middleware for authentication and logging
- Test API endpoints with curl or Postman

#### AI WebSocket Implementation Rules
**AI Action:** When implementing WebSocket features, AI must:
- Use existing WebSocket hub in `@jiranibackend/internal/websocket/`
- Implement proper connection management
- Add message broadcasting to relevant handlers
- Implement heartbeat and reconnection logic
- Test real-time communication end-to-end

#### AI Database Implementation Rules
**AI Action:** When implementing database operations, AI must:
- Use GORM transactions in `@jiranibackend/internal/models/` directory
- Implement proper error handling with rollback
- Add database connection pooling configuration
- Use prepared statements for all queries
- Test database operations with sample data

#### AI Middleware Implementation Rules
**AI Action:** When implementing middleware, AI must:
- Add middleware to `@jiranibackend/internal/middleware/` directory
- Implement authentication middleware for protected routes
- Add logging middleware for request/response tracking
- Implement rate limiting for API protection
- Test middleware functionality with API calls

### 3.3 AI GCP Integration Context

#### AI Service Authentication Implementation
**AI Action:** When implementing GCP services, AI must:
- Use service account keys from environment variables
- Implement proper IAM role configuration
- Add token rotation mechanism
- Implement access monitoring
- Test GCP service connectivity

#### AI Monitoring Implementation
**AI Action:** When implementing monitoring, AI must:
- Add structured logging to all components
- Implement Cloud Monitoring metrics
- Set up alerting for critical events
- Add distributed tracing
- Test monitoring functionality

#### AI Scaling Implementation
**AI Action:** When implementing scaling features, AI must:
- Configure auto-scaling policies
- Implement load balancing
- Add circuit breaker patterns
- Implement caching strategies
- Test scaling under load

## 4. AI Validation Gates

### 4.1 AI Pre-Implementation Validation
**AI Must Verify Before Starting:**
- [ ] All file paths are accessible using @ notation
- [ ] Development environment variables are set
- [ ] Database connection is working
- [ ] GCP services are accessible
- [ ] WebSocket infrastructure is functional
- [ ] API documentation is accessible via @ notation
- [ ] Security requirements are implemented in code
- [ ] Performance benchmarks are defined

### 4.2 AI Implementation Validation
**AI Must Verify During Implementation:**
- [ ] Unit tests pass with >90% coverage
- [ ] Integration tests validate cross-service communication
- [ ] WebSocket connections work in real-time
- [ ] Real-time features synchronize correctly
- [ ] Error handling provides user-friendly messages
- [ ] Security vulnerabilities are addressed
- [ ] Performance requirements are met

### 4.3 AI Post-Implementation Validation
**AI Must Verify After Implementation:**
- [ ] Performance benchmarks met in test environment
- [ ] Security implementation passes validation
- [ ] End-to-end testing successful
- [ ] Production deployment ready
- [ ] Monitoring and alerting functional
- [ ] Documentation updated with implementation details
- [ ] All AI implementation tasks completed

## 5. AI Success Metrics

### 5.1 AI Technical Implementation Metrics
**AI Must Achieve:**
- **Response Time**: <200ms for API calls, <100ms for WebSocket messages
- **Uptime**: >99.9% system availability during testing
- **Error Rate**: <0.1% error rate for critical operations
- **Performance**: <5% CPU usage, <100MB memory usage on mobile
- **Scalability**: Support 1000+ concurrent WebSocket connections

### 5.2 AI User Experience Implementation Metrics
**AI Must Implement Features That Achieve:**
- **User Satisfaction**: Features that support >4.5/5 app store rating
- **Completion Rate**: Implementation supporting >95% ride completion
- **Response Time**: Real-time features with <30 second response
- **Safety**: Zero security vulnerabilities in implementation
- **Accessibility**: Full accessibility compliance in UI components

### 5.3 AI Business Implementation Metrics
**AI Must Implement Features Supporting:**
- **Ride Success Rate**: Implementation supporting >98% completion
- **Driver Utilization**: Real-time features supporting >80% active time
- **Revenue**: Proper payment processing implementation
- **Growth**: Scalable architecture supporting user growth
- **Cost Efficiency**: Optimized cloud resource usage implementation

## 6. AI Emergency Implementation Procedures

### 6.1 AI Implementation Failure Recovery
**AI Action When Implementation Fails:**
- Revert to previous working code state
- Analyze error logs and implementation issues
- Re-read context documentation and requirements
- Implement fixes with proper validation
- Test thoroughly before proceeding

### 6.2 AI Security Implementation Issues
**AI Action When Security Issues Found:**
- Immediately stop implementation of vulnerable code
- Review security requirements in context documentation
- Implement proper security measures
- Validate security implementation
- Document security fixes applied

### 6.3 AI Performance Implementation Issues
**AI Action When Performance Targets Not Met:**
- Profile and analyze performance bottlenecks
- Optimize code implementation
- Review performance requirements
- Implement performance improvements
- Validate performance targets achieved

## 7. AI Continuous Implementation Improvement

### 7.1 AI Implementation Reviews
**AI Must Perform Regular Reviews:**
- Validate implementation against requirements
- Check performance metrics achievement
- Review security implementation
- Verify code quality and standards
- Update implementation based on feedback

### 7.2 AI Implementation Updates
**AI Must Handle Updates:**
- Update dependencies with proper testing
- Upgrade frameworks with validation
- Optimize implementation based on metrics
- Enhance features based on requirements
- Maintain implementation documentation

This AI implementation context framework ensures consistent, high-quality AI-driven development of the BodaBoda feature with specific file references, code snippets, and validation steps.
+