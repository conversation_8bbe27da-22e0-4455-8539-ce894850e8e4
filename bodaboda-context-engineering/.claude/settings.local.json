{"project_context": {"name": "BodaBoda Feature Development", "version": "1.0.0", "description": "Real-time ride-hailing service for motorcycle taxis in Kenya", "primary_language": "Flutter/Dart", "backend_language": "Go", "database": "PostgreSQL", "cloud_platform": "Google Cloud Platform"}, "development_rules": {"state_management": "Riverpod", "architecture_pattern": "Clean Architecture", "testing_framework": "flutter_test", "code_coverage_minimum": 90, "performance_requirements": {"api_response_time_ms": 200, "websocket_message_delay_ms": 100, "memory_usage_mb": 100, "battery_drain_percent_per_hour": 10}}, "validation_gates": {"pre_implementation": ["dependencies_configured", "environment_setup", "database_migrations_ready", "gcp_services_provisioned", "api_documentation_complete"], "implementation": ["unit_tests_pass", "integration_tests_pass", "websocket_stability_test", "real_time_sync_test", "error_handling_test"], "post_implementation": ["performance_benchmarks_met", "security_audit_passed", "user_acceptance_testing", "production_deployment_successful", "monitoring_functional"]}, "context_references": {"always_include": ["CLAUDE.md", "INITIAL.md", "current_prp", "flutter_patterns", "go_fiber_patterns"], "documentation_paths": ["documentation/", "examples/", "PRPs/"]}, "quality_standards": {"code_style": {"flutter": "dart_style with custom rules", "go": "gofmt with golint", "documentation": "comprehensive_inline_docs"}, "security": {"authentication": "JWT with refresh tokens", "data_encryption": "AES-256", "api_security": "rate_limiting and input_validation", "websocket_security": "secure_connections_only"}, "performance": {"database_queries": "optimized_with_indexing", "api_caching": "redis_caching_layer", "frontend_optimization": "lazy_loading and code_splitting", "real_time_optimization": "efficient_message_routing"}}, "deployment_context": {"remote_development": {"enabled": true, "server": "instance-********-210238", "zone": "us-central1-c", "project": "gen-lang-client-**********", "remote_dir": "/home/<USER>/jirani-backend", "docker_repo": "<PERSON><PERSON><PERSON><PERSON>", "fast_deploy_enabled": true}, "environments": {"development": {"database": "remote_postgresql_via_gcp", "backend_url": "https://api.jirani.tufiked.live", "websocket_url": "wss://api.jirani.tufiked.live/ws", "deployment_method": "remote_docker_compose"}, "staging": {"database": "gcp_cloud_sql", "backend_url": "https://staging-api.jirani.tufiked.live", "websocket_url": "wss://staging-api.jirani.tufiked.live/ws"}, "production": {"database": "gcp_cloud_sql_production", "backend_url": "https://api.jirani.tufiked.live", "websocket_url": "wss://api.jirani.tufiked.live/ws"}}, "deployment_strategy": "incremental_with_validation", "optimization": {"smart_builds": "only_build_when_changed", "incremental_deployment": "api_only_restart", "validation_gates": "context_engineering_compliant", "fast_mode": "skip_validation_for_speed"}, "monitoring": {"logging": "structured_json_logs", "metrics": "docker_compose_monitoring", "alerting": "console_output", "health_checks": "bodaboda_endpoint_validation"}}, "emergency_procedures": {"system_downtime": {"notification_channels": ["slack", "email", "sms"], "escalation_time_minutes": 15, "rollback_procedure": "automated_with_manual_approval"}, "security_incident": {"isolation_procedure": "immediate_service_isolation", "notification_time_minutes": 5, "forensic_analysis": "preserve_evidence_and_logs"}, "performance_degradation": {"auto_scaling": "enabled", "load_balancing": "automatic_redistribution", "resource_monitoring": "real_time_alerts"}}, "team_context": {"roles": {"frontend_developer": "Flutter/Dart expertise", "backend_developer": "Go/Fiber expertise", "devops_engineer": "GCP and deployment expertise", "qa_engineer": "Testing and quality assurance", "product_manager": "Feature requirements and user experience"}, "communication": {"daily_standups": "9:00 AM EAT", "sprint_planning": "bi_weekly", "code_reviews": "mandatory_for_all_changes", "documentation_updates": "with_every_feature"}}, "success_metrics": {"technical": {"uptime_percentage": 99.9, "error_rate_percentage": 0.1, "response_time_ms": 200, "concurrent_users": 1000}, "user_experience": {"app_store_rating": 4.5, "ride_completion_rate": 95, "driver_response_time_seconds": 30, "emergency_response_time_seconds": 10}, "business": {"ride_success_rate": 98, "driver_utilization_percentage": 80, "user_retention_rate": 85, "revenue_target_achievement": 100}}}