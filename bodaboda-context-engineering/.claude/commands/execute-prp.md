# Execute PRP Command

## Purpose
This command executes a Problem Resolution Plan (PRP) for BodaBoda feature development, ensuring proper implementation following the defined plan and validation criteria.

## Usage
```
/execute-prp [prp_id] [phase]
```

## Parameters
- `prp_id`: The unique PRP identifier (e.g., PRP-WEBSOCKET-PERF-001)
- `phase`: Execution phase (prepare, implement, validate, deploy, monitor)

## Execution Phases

### Phase 1: Prepare
**Objective**: Set up environment and validate prerequisites

#### Pre-execution Checklist
- [ ] PRP has been reviewed and approved
- [ ] All dependencies are available and configured
- [ ] Development environment is properly set up
- [ ] Required tools and libraries are installed
- [ ] Team members are assigned and briefed
- [ ] Backup and rollback procedures are prepared

#### Context Validation
- [ ] CLAUDE.md context rules are understood
- [ ] INITIAL.md current state is reviewed
- [ ] Related PRPs and dependencies are identified
- [ ] Performance baselines are established
- [ ] Security requirements are confirmed

#### Environment Setup
```bash
# Example preparation commands
git checkout -b prp-[prp_id]
docker-compose up -d
flutter pub get
go mod tidy
```

### Phase 2: Implement
**Objective**: Execute the technical implementation according to the PRP

#### Implementation Guidelines
1. **Follow PRP Specification**: Implement exactly as specified in the PRP
2. **Maintain Code Quality**: Follow established coding standards
3. **Document Changes**: Add comprehensive comments and documentation
4. **Test Incrementally**: Test each component as it's implemented
5. **Monitor Progress**: Track implementation against timeline

#### Code Quality Standards
- **Flutter/Dart**: Follow dart_style with custom linting rules
- **Go**: Use gofmt and golint for consistent formatting
- **Documentation**: Add comprehensive inline documentation
- **Testing**: Write tests alongside implementation
- **Security**: Follow security best practices

#### Implementation Tracking
```markdown
## Implementation Progress
- [ ] Task 1: [Description] - [Status] - [Assignee]
- [ ] Task 2: [Description] - [Status] - [Assignee]
- [ ] Task 3: [Description] - [Status] - [Assignee]

## Blockers and Issues
- Issue 1: [Description] - [Resolution]
- Issue 2: [Description] - [Resolution]

## Deviations from Plan
- Deviation 1: [Description] - [Justification]
- Deviation 2: [Description] - [Justification]
```

### Phase 3: Validate
**Objective**: Verify implementation meets all PRP success criteria

#### Validation Categories

##### Technical Validation
- [ ] Unit tests pass with >90% coverage
- [ ] Integration tests validate cross-component communication
- [ ] Performance benchmarks are met or exceeded
- [ ] Security requirements are satisfied
- [ ] Error handling works correctly

##### Functional Validation
- [ ] All specified features work as intended
- [ ] User workflows are smooth and intuitive
- [ ] Edge cases are handled properly
- [ ] Backward compatibility is maintained
- [ ] API contracts are preserved

##### Performance Validation
- [ ] Response times meet requirements (<200ms API, <100ms WebSocket)
- [ ] Memory usage is within limits (<100MB mobile)
- [ ] Battery usage is optimized (<10% drain per hour)
- [ ] Concurrent user capacity is verified
- [ ] Load testing passes under stress conditions

##### Security Validation
- [ ] Authentication and authorization work correctly
- [ ] Data encryption is properly implemented
- [ ] Input validation prevents injection attacks
- [ ] WebSocket connections are secure
- [ ] Sensitive data is properly protected

#### Validation Commands
```bash
# Run comprehensive test suite
flutter test --coverage
go test ./... -v -race -coverprofile=coverage.out

# Performance testing
flutter drive --target=test_driver/performance_test.dart
go test -bench=. -benchmem

# Security scanning
flutter analyze
gosec ./...
```

### Phase 4: Deploy
**Objective**: Deploy the implementation to appropriate environments

#### Deployment Strategy
1. **Development**: Deploy to local development environment
2. **Staging**: Deploy to staging environment for final validation
3. **Production**: Deploy to production with proper monitoring

#### Deployment Checklist
- [ ] Database migrations are prepared and tested
- [ ] Environment variables are configured
- [ ] Monitoring and logging are set up
- [ ] Health checks are implemented
- [ ] Rollback procedures are ready
- [ ] Team is notified of deployment

#### Deployment Commands
```bash
# Build and deploy
docker build -t jirani-api:prp-[prp_id] .
docker push kimathinrian/jirani-api:prp-[prp_id]

# Update production
kubectl apply -f k8s/
kubectl rollout status deployment/jirani-api
```

### Phase 5: Monitor
**Objective**: Monitor the implementation in production and ensure stability

#### Monitoring Areas
- **Performance Metrics**: Response times, throughput, error rates
- **System Health**: CPU, memory, disk usage, network traffic
- **User Experience**: App crashes, user feedback, completion rates
- **Business Metrics**: Ride success rates, driver utilization
- **Security Events**: Failed authentication, suspicious activities

#### Monitoring Tools
- **Application Monitoring**: New Relic, Datadog, or similar
- **Infrastructure Monitoring**: Google Cloud Monitoring
- **Log Aggregation**: ELK stack or Google Cloud Logging
- **Alerting**: PagerDuty, Slack integration
- **User Analytics**: Firebase Analytics, Mixpanel

#### Post-deployment Validation
- [ ] All systems are operational
- [ ] Performance metrics are within expected ranges
- [ ] No critical errors or alerts
- [ ] User feedback is positive
- [ ] Business metrics are improving

## Error Handling and Rollback

### Common Issues and Solutions
1. **Test Failures**: Review test results, fix issues, re-run validation
2. **Performance Degradation**: Optimize code, review algorithms, scale resources
3. **Security Vulnerabilities**: Apply security patches, review code
4. **Deployment Failures**: Check configurations, validate dependencies
5. **Production Issues**: Monitor closely, prepare for rollback if needed

### Rollback Procedures
```bash
# Quick rollback to previous version
kubectl rollout undo deployment/jirani-api
docker-compose down && docker-compose up -d

# Database rollback (if needed)
migrate -path migrations -database $DATABASE_URL down 1
```

## Success Criteria Validation

### Technical Success
- [ ] All tests pass consistently
- [ ] Performance benchmarks are met
- [ ] Security requirements are satisfied
- [ ] Code quality standards are maintained
- [ ] Documentation is comprehensive

### Business Success
- [ ] User experience is improved
- [ ] System reliability is enhanced
- [ ] Performance is optimized
- [ ] Security is strengthened
- [ ] Scalability is increased

## Documentation and Knowledge Transfer

### Required Documentation Updates
- [ ] API documentation updated
- [ ] Architecture diagrams updated
- [ ] Deployment procedures updated
- [ ] Troubleshooting guides updated
- [ ] Team knowledge base updated

### Knowledge Transfer
- [ ] Team walkthrough of changes
- [ ] Documentation review session
- [ ] Operational procedures training
- [ ] Monitoring and alerting setup
- [ ] Support team briefing

## PRP Closure

### Completion Checklist
- [ ] All implementation tasks completed
- [ ] All validation criteria met
- [ ] Deployment successful
- [ ] Monitoring established
- [ ] Documentation updated
- [ ] Team trained
- [ ] Lessons learned documented

### Lessons Learned
Document key insights for future PRPs:
- What worked well?
- What could be improved?
- What unexpected challenges arose?
- How can the process be optimized?

### PRP Status Update
Update PRP status to "Completed" with:
- Completion date
- Final metrics achieved
- Lessons learned summary
- Recommendations for future work
