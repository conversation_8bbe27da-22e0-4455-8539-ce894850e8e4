# AI Implementation PRP Executor for Augment AI

## Purpose
This command executes AI implementation plans (PRPs) for BodaBoda feature development, ensuring proper AI implementation following exact file paths, code snippets, and validation criteria.

## Usage
```
@execute-prp [prp_id] [phase]
```

## Parameters
- `prp_id`: The unique AI implementation PRP identifier (e.g., PRP-WEBSOCKET-PERF-001)
- `phase`: AI execution phase (analyze, implement, validate, deploy, monitor)

## AI Implementation Execution Phases

### AI Phase 1: Analyze
**Objective**: AI analyzes current code state and validates implementation prerequisites

#### AI Pre-execution Checklist
- [ ] AI has read and understood PRP implementation requirements
- [ ] AI has access to all required files using @ notation
- [ ] AI has validated current code state matches PRP assumptions
- [ ] AI has identified all file dependencies and integration points
- [ ] AI has verified API endpoints and database models are accessible
- [ ] AI has confirmed WebSocket infrastructure is functional
- [ ] AI has validated authentication and authorization systems

#### AI Context Validation
- [ ] AI reads @bodaboda-context-engineering/AUGMENT.md for implementation guidelines
- [ ] AI reviews @bodaboda-context-engineering/INITIAL.md for current system state
- [ ] AI identifies related PRPs in @bodaboda-context-engineering/PRPs/bodaboda_prps/ and dependencies
- [ ] AI establishes performance baselines for implementation
- [ ] AI confirms security requirements for implementation

#### AI Environment Validation
```bash
# AI validates environment access
# AI checks file accessibility using @ notation
# AI verifies API endpoints are responsive
# AI confirms database connectivity
# AI validates WebSocket functionality
```

### AI Phase 2: Implement
**Objective**: AI executes implementation plan following exact PRP specifications with file paths and code snippets

#### AI Implementation Guidelines
1. **Follow PRP Specification**: AI implements exactly as specified with file paths and line numbers
2. **Maintain Code Quality**: AI follows established coding standards and patterns
3. **Document Changes**: AI adds comprehensive comments and updates documentation
4. **Test Incrementally**: AI tests each component as it's implemented
5. **Monitor Progress**: AI tracks implementation against validation criteria

#### AI Code Quality Standards
- **Flutter/Dart**: AI follows dart_style with existing project patterns
- **Go**: AI uses gofmt and follows existing backend patterns
- **Documentation**: AI adds comprehensive inline documentation with @ references
- **Testing**: AI writes tests alongside implementation
- **Security**: AI follows security best practices and validates implementation

#### Implementation Tracking
```markdown
## Implementation Progress
- [ ] Task 1: [Description] - [Status] - [Assignee]
- [ ] Task 2: [Description] - [Status] - [Assignee]
- [ ] Task 3: [Description] - [Status] - [Assignee]

## Blockers and Issues
- Issue 1: [Description] - [Resolution]
- Issue 2: [Description] - [Resolution]

## Deviations from Plan
- Deviation 1: [Description] - [Justification]
- Deviation 2: [Description] - [Justification]
```

### Phase 3: Validate
**Objective**: Verify implementation meets all PRP success criteria

#### Validation Categories

##### Technical Validation
- [ ] Unit tests pass with >90% coverage
- [ ] Integration tests validate cross-component communication
- [ ] Performance benchmarks are met or exceeded
- [ ] Security requirements are satisfied
- [ ] Error handling works correctly

##### Functional Validation
- [ ] All specified features work as intended
- [ ] User workflows are smooth and intuitive
- [ ] Edge cases are handled properly
- [ ] Backward compatibility is maintained
- [ ] API contracts are preserved

##### Performance Validation
- [ ] Response times meet requirements (<200ms API, <100ms WebSocket)
- [ ] Memory usage is within limits (<100MB mobile)
- [ ] Battery usage is optimized (<10% drain per hour)
- [ ] Concurrent user capacity is verified
- [ ] Load testing passes under stress conditions

##### Security Validation
- [ ] Authentication and authorization work correctly
- [ ] Data encryption is properly implemented
- [ ] Input validation prevents injection attacks
- [ ] WebSocket connections are secure
- [ ] Sensitive data is properly protected

#### Validation Commands
```bash
# Run comprehensive test suite
flutter test --coverage
go test ./... -v -race -coverprofile=coverage.out

# Performance testing
flutter drive --target=test_driver/performance_test.dart
go test -bench=. -benchmem

# Security scanning
flutter analyze
gosec ./...
```

### Phase 4: Deploy
**Objective**: Deploy the implementation to appropriate environments

#### Deployment Strategy
1. **Development**: Deploy to local development environment
2. **Staging**: Deploy to staging environment for final validation
3. **Production**: Deploy to production with proper monitoring

#### Deployment Checklist
- [ ] Database migrations are prepared and tested
- [ ] Environment variables are configured
- [ ] Monitoring and logging are set up
- [ ] Health checks are implemented
- [ ] Rollback procedures are ready
- [ ] Team is notified of deployment

#### Deployment Commands
```bash
# Build and deploy
docker build -t jirani-api:prp-[prp_id] .
docker push kimathinrian/jirani-api:prp-[prp_id]

# Update production
kubectl apply -f k8s/
kubectl rollout status deployment/jirani-api
```

### Phase 5: Monitor
**Objective**: Monitor the implementation in production and ensure stability

#### Monitoring Areas
- **Performance Metrics**: Response times, throughput, error rates
- **System Health**: CPU, memory, disk usage, network traffic
- **User Experience**: App crashes, user feedback, completion rates
- **Business Metrics**: Ride success rates, driver utilization
- **Security Events**: Failed authentication, suspicious activities

#### Monitoring Tools
- **Application Monitoring**: New Relic, Datadog, or similar
- **Infrastructure Monitoring**: Google Cloud Monitoring
- **Log Aggregation**: ELK stack or Google Cloud Logging
- **Alerting**: PagerDuty, Slack integration
- **User Analytics**: Firebase Analytics, Mixpanel

#### Post-deployment Validation
- [ ] All systems are operational
- [ ] Performance metrics are within expected ranges
- [ ] No critical errors or alerts
- [ ] User feedback is positive
- [ ] Business metrics are improving

## Error Handling and Rollback

### Common Issues and Solutions
1. **Test Failures**: Review test results, fix issues, re-run validation
2. **Performance Degradation**: Optimize code, review algorithms, scale resources
3. **Security Vulnerabilities**: Apply security patches, review code
4. **Deployment Failures**: Check configurations, validate dependencies
5. **Production Issues**: Monitor closely, prepare for rollback if needed

### Rollback Procedures
```bash
# Quick rollback to previous version
kubectl rollout undo deployment/jirani-api
docker-compose down && docker-compose up -d

# Database rollback (if needed)
migrate -path migrations -database $DATABASE_URL down 1
```

## Success Criteria Validation

### Technical Success
- [ ] All tests pass consistently
- [ ] Performance benchmarks are met
- [ ] Security requirements are satisfied
- [ ] Code quality standards are maintained
- [ ] Documentation is comprehensive

### Business Success
- [ ] User experience is improved
- [ ] System reliability is enhanced
- [ ] Performance is optimized
- [ ] Security is strengthened
- [ ] Scalability is increased

## Documentation and Knowledge Transfer

### Required Documentation Updates
- [ ] API documentation updated
- [ ] Architecture diagrams updated
- [ ] Deployment procedures updated
- [ ] Troubleshooting guides updated
- [ ] Team knowledge base updated

### Knowledge Transfer
- [ ] Team walkthrough of changes
- [ ] Documentation review session
- [ ] Operational procedures training
- [ ] Monitoring and alerting setup
- [ ] Support team briefing

## PRP Closure

### Completion Checklist
- [ ] All implementation tasks completed
- [ ] All validation criteria met
- [ ] Deployment successful
- [ ] Monitoring established
- [ ] Documentation updated
- [ ] Team trained
- [ ] Lessons learned documented

### Lessons Learned
Document key insights for future PRPs:
- What worked well?
- What could be improved?
- What unexpected challenges arose?
- How can the process be optimized?

### PRP Status Update
Update PRP status to "Completed" with:
- Completion date
- Final metrics achieved
- Lessons learned summary
- Recommendations for future work
