# AI Implementation PRP Generator for Augment AI

## Purpose
This command generates AI implementation plans (PRPs) for BodaBoda feature development. It creates step-by-step AI implementation guides with exact file paths, code snippets, and validation criteria.

## Usage
```
@generate-prp [component] [issue_type] [priority]
```

## Parameters
- `component`: The system component (frontend, backend, websocket, database, deployment)
- `issue_type`: Type of AI implementation (bug, enhancement, performance, security, scalability)
- `priority`: Priority level (critical, high, medium, low)

## AI Implementation Context Requirements
Before generating an AI implementation PRP, ensure you have:
1. ✅ Read @bodaboda-context-engineering/AUGMENT.md for AI implementation rules
2. ✅ Reviewed @bodaboda-context-engineering/INITIAL.md for current AI-accessible code
3. ✅ Identified specific AI implementation requirements
4. ✅ Gathered relevant file paths, API endpoints, and existing functionality
5. ✅ Reviewed existing PRPs in @bodaboda-context-engineering/PRPs/bodaboda_prps/ to avoid duplication

## AI Implementation PRP Generation Process

### Step 1: AI Implementation Analysis
- **AI Task Identification**: What specific implementation must AI complete?
- **File Path Analysis**: What files must AI access using @ notation?
- **Code Integration Analysis**: What existing code must AI integrate with?
- **AI Implementation Scope**: What components must AI modify or create?

### Step 2: AI Implementation Design
- **AI Implementation Approach**: Choose specific AI implementation strategy
- **Technical Specification**: Define exact file paths, line numbers, and code snippets
- **AI Dependencies**: Identify files and APIs AI must access
- **AI Risk Assessment**: Evaluate AI implementation risks and mitigation

### Step 3: AI Implementation Plan
- **AI Task Breakdown**: Break down into specific AI implementation steps
- **AI Timeline Estimation**: Provide realistic AI implementation phases
- **AI Resource Requirements**: Identify file access and API requirements
- **AI Testing Strategy**: Define AI validation and testing approach

### Step 4: AI Validation Criteria
- **AI Success Metrics**: Define measurable AI implementation outcomes
- **AI Testing Requirements**: Specify AI validation steps
- **AI Performance Benchmarks**: Set AI implementation performance targets
- **AI Acceptance Criteria**: Define AI implementation completion criteria

## AI Implementation PRP Example

### AI Input
```
@generate-prp websocket performance high
```

### AI Output Structure
```markdown
# PRP-WEBSOCKET-PERF-001: AI WebSocket Performance Implementation

## AI Implementation Problem Statement
AI must implement WebSocket performance optimization to achieve <100ms message delivery

## AI Implementation Analysis
- **File Dependencies**: @jiranibackend/internal/websocket/hub.go, @jirani_app/lib/services/websocket_service.dart
- **Code Integration**: Existing WebSocket infrastructure needs performance optimization
- **AI Implementation Scope**: Message queue optimization, routing algorithm improvement

## AI Implementation Approach
**AI Task 1**: Optimize message queue in @jiranibackend/internal/websocket/hub.go lines 45-67
**AI Task 2**: Implement efficient routing in @jiranibackend/internal/websocket/message.go lines 23-45
**AI Task 3**: Add connection pooling in @jirani_app/lib/services/websocket_service.dart lines 78-95

## AI Implementation Plan
**Phase 1**: AI analyzes existing WebSocket code and identifies bottlenecks
**Phase 2**: AI implements message queue optimization with exact code changes
**Phase 3**: AI validates performance improvements and tests under load

## AI Validation Criteria
- AI achieves message delivery <100ms under 1000 concurrent connections
- AI implements zero message loss during peak usage
- AI ensures stable connections with auto-reconnection

## AI Success Metrics
- AI implementation reduces message delivery time by 50%
- AI achieves 99.9% message delivery success rate
- AI supports 2000+ concurrent connections
```

## AI Implementation PRP Templates Available
1. **AI Bug Fix PRP**: For AI to resolve identified bugs with exact code fixes
2. **AI Performance PRP**: For AI performance optimization with specific implementations
3. **AI Security PRP**: For AI security enhancements with code examples
4. **AI Feature PRP**: For AI new feature development with step-by-step guides
5. **AI Scalability PRP**: For AI system scaling improvements with implementation details

## Quality Checklist
Before finalizing a PRP, ensure:
- [ ] Problem is clearly defined and scoped
- [ ] Solution approach is technically sound
- [ ] Implementation plan is detailed and realistic
- [ ] Validation criteria are measurable and comprehensive
- [ ] Success metrics align with business objectives
- [ ] Dependencies and risks are identified
- [ ] Timeline estimates are realistic
- [ ] Resource requirements are specified

## AI Implementation Workflow Integration
1. **AI PRP Creation**: Generate AI implementation PRP using this command
2. **AI Implementation**: AI executes PRP following exact implementation steps
3. **AI Validation**: AI verifies all success criteria are met through testing
4. **AI Documentation**: AI updates relevant documentation with implementation details
5. **AI Completion**: AI marks PRP as complete with implementation results

## AI Implementation Context Awareness Rules
When generating AI implementation PRPs, always consider:
- **Current System State**: Reference @bodaboda-context-engineering/INITIAL.md for AI-accessible code
- **Architecture Constraints**: Follow @bodaboda-context-engineering/AUGMENT.md AI implementation guidelines
- **Performance Requirements**: AI must meet defined performance benchmarks
- **Security Standards**: AI must maintain security best practices in implementation
- **User Experience**: AI must prioritize user experience and safety in implementations
- **Scalability Needs**: AI must design for future growth
- **Maintenance Burden**: AI must consider long-term maintenance implications
- **Augment Integration**: AI must use @ notation for all file references

## AI Implementation PRP Naming Convention
```
PRP-[COMPONENT]-[TYPE]-[NUMBER]
```

AI Implementation Examples:
- `PRP-FRONTEND-BUG-001`: AI frontend bug fix implementation
- `PRP-BACKEND-PERF-002`: AI backend performance optimization implementation
- `PRP-WEBSOCKET-SCALE-003`: AI WebSocket scalability improvement implementation
- `PRP-DATABASE-SEC-004`: AI database security enhancement implementation

## AI Implementation Storage and Tracking
- All AI implementation PRPs stored in @bodaboda-context-engineering/PRPs/bodaboda_prps/ directory
- AI uses consistent naming and numbering
- AI maintains PRP status tracking (draft, in-progress, completed)
- AI links PRPs to implementation files, commits, and deployments
- AI references PRPs using @ notation: @bodaboda-context-engineering/PRPs/bodaboda_prps/PRP-XXX-XXX-001.md

## Continuous Improvement
- Review completed PRPs for lessons learned
- Update PRP templates based on experience
- Maintain metrics on PRP effectiveness
- Use PRP data for process improvement
