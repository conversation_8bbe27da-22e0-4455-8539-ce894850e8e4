# Generate PRP Command for Augment AI

## Purpose
This command generates a new Problem Resolution Plan (PRP) for BodaBoda feature development based on identified issues or enhancement requirements using Augment AI's capabilities.

## Usage
```
@generate-prp [component] [issue_type] [priority]
```

## Parameters
- `component`: The system component (frontend, backend, websocket, database, deployment)
- `issue_type`: Type of issue (bug, enhancement, performance, security, scalability)
- `priority`: Priority level (critical, high, medium, low)

## Context Requirements
Before generating a PRP, ensure you have:
1. ✅ Read and understood @bodaboda-context-engineering/AUGMENT.md context rules
2. ✅ Reviewed @bodaboda-context-engineering/INITIAL.md for current implementation status
3. ✅ Identified the specific problem or enhancement need
4. ✅ Gathered relevant error logs, performance metrics, or user feedback
5. ✅ Reviewed existing PRPs in @bodaboda-context-engineering/PRPs/bodaboda_prps/ to avoid duplication

## PRP Generation Process

### Step 1: Problem Analysis
- **Symptom Identification**: What is the observable issue?
- **Root Cause Analysis**: What is causing the problem?
- **Impact Assessment**: How does this affect users and system performance?
- **Scope Definition**: What components are affected?

### Step 2: Solution Design
- **Approach Selection**: Choose the most appropriate solution approach
- **Technical Specification**: Define the technical implementation details
- **Dependencies**: Identify any dependencies on other components
- **Risk Assessment**: Evaluate potential risks and mitigation strategies

### Step 3: Implementation Plan
- **Task Breakdown**: Break down the solution into manageable tasks
- **Timeline Estimation**: Provide realistic time estimates
- **Resource Requirements**: Identify required skills and tools
- **Testing Strategy**: Define comprehensive testing approach

### Step 4: Validation Criteria
- **Success Metrics**: Define measurable success criteria
- **Testing Requirements**: Specify all required tests
- **Performance Benchmarks**: Set performance targets
- **User Acceptance Criteria**: Define user-facing success criteria

## Example PRP Generation

### Input
```
@generate-prp websocket performance high
```

### Output Structure
```markdown
# PRP-WEBSOCKET-PERF-001: WebSocket Performance Optimization

## Problem Statement
WebSocket message delivery experiencing delays >200ms during peak usage

## Root Cause Analysis
- Message queue bottleneck during high concurrent connections
- Inefficient message routing algorithm
- Lack of connection pooling optimization

## Solution Approach
1. Implement message queue optimization with Redis
2. Redesign message routing with efficient algorithms
3. Add connection pooling and load balancing

## Implementation Plan
[Detailed implementation steps]

## Validation Criteria
- Message delivery <100ms under 1000 concurrent connections
- Zero message loss during peak usage
- Stable connections with auto-reconnection

## Success Metrics
- 50% reduction in message delivery time
- 99.9% message delivery success rate
- Support for 2000+ concurrent connections
```

## PRP Templates Available
1. **Bug Fix PRP**: For resolving identified bugs
2. **Performance PRP**: For performance optimization
3. **Security PRP**: For security enhancements
4. **Feature PRP**: For new feature development
5. **Scalability PRP**: For system scaling improvements

## Quality Checklist
Before finalizing a PRP, ensure:
- [ ] Problem is clearly defined and scoped
- [ ] Solution approach is technically sound
- [ ] Implementation plan is detailed and realistic
- [ ] Validation criteria are measurable and comprehensive
- [ ] Success metrics align with business objectives
- [ ] Dependencies and risks are identified
- [ ] Timeline estimates are realistic
- [ ] Resource requirements are specified

## Integration with Development Workflow
1. **PRP Creation**: Generate PRP using this command
2. **Review Process**: Team review and approval
3. **Implementation**: Execute PRP following the plan
4. **Validation**: Verify all success criteria are met
5. **Documentation**: Update relevant documentation
6. **Closure**: Mark PRP as complete with lessons learned

## Context Awareness Rules
When generating PRPs, always consider:
- **Current System State**: Reference @bodaboda-context-engineering/INITIAL.md for current implementation
- **Architecture Constraints**: Follow @bodaboda-context-engineering/AUGMENT.md architecture guidelines
- **Performance Requirements**: Meet defined performance benchmarks
- **Security Standards**: Maintain security best practices
- **User Experience**: Prioritize user experience and safety
- **Scalability Needs**: Design for future growth
- **Maintenance Burden**: Consider long-term maintenance implications
- **Augment Integration**: Use @ notation for all file references

## PRP Naming Convention
```
PRP-[COMPONENT]-[TYPE]-[NUMBER]
```

Examples:
- `PRP-FRONTEND-BUG-001`: Frontend bug fix
- `PRP-BACKEND-PERF-002`: Backend performance optimization
- `PRP-WEBSOCKET-SCALE-003`: WebSocket scalability improvement
- `PRP-DATABASE-SEC-004`: Database security enhancement

## Storage and Tracking
- All PRPs are stored in @bodaboda-context-engineering/PRPs/bodaboda_prps/ directory
- Use consistent naming and numbering
- Maintain PRP status tracking (draft, approved, in-progress, completed)
- Link PRPs to related issues, commits, and deployments
- Reference PRPs using @ notation: @bodaboda-context-engineering/PRPs/bodaboda_prps/PRP-XXX-XXX-001.md

## Continuous Improvement
- Review completed PRPs for lessons learned
- Update PRP templates based on experience
- Maintain metrics on PRP effectiveness
- Use PRP data for process improvement
