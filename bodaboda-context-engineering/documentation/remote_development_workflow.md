# Remote Development Workflow for BodaBoda Feature with Augment AI

## Overview
This guide provides an optimized workflow for developing the BodaBoda feature using remote development on GCP, following context engineering principles with Augment AI's @ file referencing system while maximizing development speed and cost-effectiveness.

## Current Setup Analysis

### Server Configuration
- **Instance**: `instance-20250518-210238`
- **Zone**: `us-central1-c`
- **Project**: `gen-lang-client-**********`
- **Access**: `gcloud compute ssh --zone "us-central1-c" "instance-20250518-210238" --project "gen-lang-client-**********"`

### Container Status ✅
- **API**: Running (kimathinrian/jirani-api:latest) - Some bot attacks but functional
- **Database**: PostgreSQL healthy
- **Cache**: Redis healthy
- **Queue**: RabbitMQ healthy
- **Storage**: MinIO healthy
- **Proxy**: Nginx healthy with SSL

## Optimized Development Workflows

### 1. Fast Development Cycle (< 2 minutes)

For quick iterations during active development:

```bash
# Copy the fast deploy script to your backend directory
cp bodaboda-context-engineering/examples/gcp_integration/fast_deploy.sh jiranibackend/
chmod +x jiranibackend/fast_deploy.sh

# Navigate to backend directory
cd jiranibackend

# Quick deploy (build + push + deploy)
./fast_deploy.sh deploy

# Or use interactive mode
./fast_deploy.sh
```

**Timeline**: ~90-120 seconds
- Build: 15-20s
- Docker build: 20-30s
- Push: 30-40s
- Deploy: 15-20s
- Verification: 10-15s

### 2. Context Engineering Compliant Deployment (< 5 minutes)

For production-ready changes with full validation:

```bash
# Copy the optimized deployment script
cp bodaboda-context-engineering/examples/gcp_integration/optimized_deployment.sh jiranibackend/
chmod +x jiranibackend/optimized_deployment.sh

# Full deployment with validation
./optimized_deployment.sh

# Fast mode (skip validation)
./optimized_deployment.sh --fast

# Dry run to see what would happen
./optimized_deployment.sh --dry-run
```

**Timeline**: ~3-5 minutes with validation
- Pre-validation: 30-60s
- Tests: 60-90s
- Build & Deploy: 90-120s
- Post-validation: 30-60s

### 3. Emergency Quick Fix (< 1 minute)

For urgent hotfixes:

```bash
# Just restart the API service
./fast_deploy.sh restart

# Or check logs immediately
./fast_deploy.sh logs
```

## Development Commands Reference

### Quick Commands
```bash
# Build and deploy in one command
make deploy-api

# Just restart API service
make restart-api

# Update docker-compose only
make update-compose

# Check service status
./fast_deploy.sh status

# Test BodaBoda endpoints
./fast_deploy.sh test
```

### Context Engineering Commands
```bash
# Generate PRP for new feature
@generate-prp bodaboda enhancement medium

# Execute PRP with validation
@execute-prp PRP-BODABODA-ENH-001 implement

# Full validation deployment
./optimized_deployment.sh --no-fast
```

## BodaBoda Endpoint Testing

### Quick Health Check
```bash
# Test drivers endpoint (should return 401 without auth)
curl -s -w '%{http_code}' https://api.jirani.tufiked.live/api/boda/drivers?latitude=-1.2921&longitude=36.8219

# Test WebSocket endpoint (should return 400/426)
curl -s -w '%{http_code}' https://api.jirani.tufiked.live/ws
```

### Comprehensive Testing
```bash
# Run the endpoint test script
./fast_deploy.sh test

# Check API logs for errors
./fast_deploy.sh logs

# Monitor container health
docker-compose -f docker-compose.prod.yml ps
```

## Cost Optimization Strategies

### 1. Smart Building
- **Incremental builds**: Only rebuild when source changes
- **Docker layer caching**: Reuse unchanged layers
- **Binary checking**: Skip build if binary is newer than source

### 2. Efficient Deployment
- **API-only restarts**: Don't restart entire stack
- **Image reuse**: Only pull if image changed
- **Selective updates**: Update only changed services

### 3. Resource Management
- **No local testing**: All testing on remote server
- **Shared development**: Multiple developers use same instance
- **Optimized images**: Minimal Docker images for faster transfers

## Troubleshooting Guide

### Common Issues

#### 1. API Container Unhealthy
```bash
# Check logs
docker logs jirani-api --tail 50

# Restart API
./fast_deploy.sh restart

# Full redeploy if needed
./optimized_deployment.sh --force-rebuild
```

#### 2. Build Failures
```bash
# Check Go code
go vet ./...

# Run tests locally
go test ./...

# Clean build
./optimized_deployment.sh --force-rebuild
```

#### 3. Deployment Timeouts
```bash
# Check server resources
gcloud compute ssh --zone "us-central1-c" "instance-20250518-210238" --project "gen-lang-client-**********" --command "htop"

# Check Docker resources
docker system df
docker system prune -f
```

### Performance Monitoring

#### Container Health
```bash
# Check all containers
docker-compose -f docker-compose.prod.yml ps

# Check resource usage
docker stats

# Check logs
docker-compose -f docker-compose.prod.yml logs -f api
```

#### API Performance
```bash
# Test response times
time curl -s https://api.jirani.tufiked.live/api/boda/drivers?latitude=-1.2921&longitude=36.8219

# Monitor WebSocket connections
# (Check logs for WebSocket connection counts)
```

## Security Considerations

### 1. Bot Attack Mitigation
The server is receiving bot attacks trying to access .env files. This is normal but should be monitored:

```bash
# Check attack patterns
docker logs jirani-api | grep "\.env" | tail -10

# Monitor for unusual activity
docker logs jirani-api | grep -E "(404|401|403)" | tail -20
```

### 2. Access Control
- Use gcloud authentication for server access
- Docker images are pushed to public registry (consider private registry for production)
- SSL certificates are properly configured via Let's Encrypt

## Best Practices for Remote Development

### 1. Development Workflow
1. **Make changes locally** in your IDE
2. **Test syntax** with `go vet ./...`
3. **Quick deploy** with `./fast_deploy.sh deploy`
4. **Test endpoints** with `./fast_deploy.sh test`
5. **Check logs** if issues: `./fast_deploy.sh logs`

### 2. Context Engineering Integration
1. **Use PRPs** for significant changes
2. **Run validation** before important deployments
3. **Document changes** in context engineering files
4. **Update patterns** when creating new solutions

### 3. Collaboration
1. **Coordinate deployments** with team members
2. **Use version tags** for tracking changes
3. **Share PRP documents** for complex changes
4. **Update documentation** after successful deployments

## Performance Benchmarks

### Target Metrics (Context Engineering Compliant)
- **Deployment Time**: < 2 minutes for fast deploy, < 5 minutes for full validation
- **API Response**: < 200ms for BodaBoda endpoints
- **WebSocket**: < 100ms message delivery
- **Container Health**: All services healthy within 30s of deployment

### Current Performance
- **Fast Deploy**: ~90-120 seconds
- **Full Deploy**: ~3-5 minutes
- **API Response**: Functional (exact timing needs measurement)
- **Container Startup**: ~15-30 seconds

## Next Steps

1. **Implement monitoring** for deployment times
2. **Add automated testing** for BodaBoda endpoints
3. **Create staging environment** for safer testing
4. **Optimize Docker images** for faster transfers
5. **Implement blue-green deployment** for zero-downtime updates

This workflow provides a balance between development speed, cost-effectiveness, and quality assurance while maintaining the context engineering standards defined for the BodaBoda feature.
