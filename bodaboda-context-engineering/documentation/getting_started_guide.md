# BodaBoda Development - Getting Started Guide with Augment AI

## Overview
This guide provides a complete walkthrough for starting BodaBoda feature development using our optimized context engineering approach with Augment AI's @ file referencing system. It ensures seamless communication between frontend and backend while maintaining high development velocity.

## Prerequisites Verification

### 1. Environment Setup ✅
- **Local Development**: Flutter app development environment
- **Remote Backend**: GCP server with Docker containers
- **Context Engineering**: Framework files in place

### 2. Current Status Verification ✅
- **Backend API**: Running and healthy at `https://api.jirani.tufiked.live`
- **BodaBoda Endpoints**: Properly configured under `/api/v1/boda/`
- **Authentication**: JWT middleware working correctly
- **WebSocket**: Available at `wss://api.jirani.tufiked.live/ws`

## Step-by-Step Development Workflow

### Phase 1: Verify Frontend-Backend Communication

#### Step 1.1: Test Backend Endpoints
```bash
# Navigate to backend directory
cd jiranibackend

# Test BodaBoda endpoints (should return 401 - authentication required)
make test-endpoints

# Check API status
make status

# View logs if needed
make logs
```

**Expected Results:**
- BodaBoda drivers endpoint: HTTP 401 (Unauthorized) ✅
- WebSocket endpoint: HTTP 400/426 (Upgrade required) ✅
- All containers: Healthy status ✅

#### Step 1.2: Test Flutter App API Configuration
```bash
# Navigate to Flutter app
cd jirani_app

# Check API configuration
flutter pub get

# Verify API config points to production
grep -n "api.jirani.tufiked.live" lib/services/api_config.dart
```

**Expected Results:**
- API URL: `https://api.jirani.tufiked.live/api/v1` ✅
- Force production API: `true` ✅

#### Step 1.3: Test Authentication Flow
```bash
# Run Flutter app
flutter run

# Test user registration/login
# Navigate to BodaBoda feature
# Verify API calls in debug console
```

**Expected Results:**
- Authentication endpoints working ✅
- JWT tokens being generated ✅
- BodaBoda screens accessible ✅

### Phase 2: Context Engineering Setup

#### Step 2.1: Review Context Engineering Files
```bash
# Review core context using Augment's @ notation
# Reference: @bodaboda-context-engineering/AUGMENT.md
# Reference: @bodaboda-context-engineering/INITIAL.md

# Check deployment scripts
ls -la jiranibackend/fast_deploy.sh
ls -la jiranibackend/optimized_deployment.sh
```

#### Step 2.2: Understand Development Patterns
```bash
# Review Flutter patterns using Augment's @ notation
# Reference: @bodaboda-context-engineering/examples/flutter_patterns/riverpod_state_management.dart

# Review Go patterns using Augment's @ notation
# Reference: @bodaboda-context-engineering/examples/go_fiber_patterns/api_patterns.go
```

### Phase 3: Development Workflow Implementation

#### Step 3.1: Make Your First Change
```bash
# Example: Add a simple log to BodaBoda controller
cd jiranibackend

# Edit the controller
# Add: log.Printf("BodaBoda drivers endpoint called")
# to internal/controllers/boda_controller.go

# Quick deploy
make fast-deploy

# Test the change
make test-endpoints

# Check logs to see your change
make logs
```

#### Step 3.2: Frontend Development Cycle
```bash
cd jirani_app

# Make changes to BodaBoda screens
# Example: Update UI text or add debug prints

# Hot reload in Flutter
# Test changes immediately

# If backend changes needed:
cd ../jiranibackend
make fast-deploy
cd ../jirani_app
# Continue Flutter development
```

### Phase 4: Context Engineering Workflow

#### Step 4.1: For Small Changes (< 30 minutes)
```bash
# Use fast deployment
cd jiranibackend
make fast-deploy

# Test immediately
make test-endpoints
```

#### Step 4.2: For Significant Changes (> 30 minutes)
```bash
# Generate PRP first using Augment's @ notation
# Use context engineering approach

# Example PRP for new feature
@generate-prp ride-tracking enhancement high

# Full deployment with validation
make deploy

# Comprehensive testing
make test-endpoints
flutter test
```

## Development Commands Reference

### Backend Development
```bash
# Quick iteration cycle
make fast-deploy          # Deploy changes (~2 minutes)
make test-endpoints       # Test BodaBoda functionality
make logs                 # Check for issues
make restart-api          # Restart if needed

# Full development cycle
make deploy               # Context engineering compliant
make test                 # Run Go tests
make test-coverage        # Coverage report
```

### Frontend Development
```bash
# Flutter development
flutter run               # Start app with hot reload
flutter test              # Run tests
flutter analyze           # Static analysis
flutter build apk         # Build for testing
```

### Integrated Testing
```bash
# Test complete flow
cd jiranibackend && make test-endpoints
cd ../jirani_app && flutter test
```

## Verification Checklist

### ✅ Backend Verification
- [ ] API responds to health checks
- [ ] BodaBoda endpoints return 401 (auth required)
- [ ] WebSocket endpoint available
- [ ] Docker containers healthy
- [ ] Logs show no critical errors

### ✅ Frontend Verification
- [ ] App builds without errors
- [ ] API configuration correct
- [ ] Authentication flow works
- [ ] BodaBoda screens load
- [ ] WebSocket connection establishes

### ✅ Integration Verification
- [ ] Login creates valid JWT token
- [ ] BodaBoda API calls include auth headers
- [ ] Real-time features work via WebSocket
- [ ] Error handling displays user-friendly messages
- [ ] Offline scenarios handled gracefully

## Common Issues and Solutions

### Issue 1: API Endpoints Return 404
**Solution:**
```bash
# Check if routes are properly configured
grep -r "boda" jiranibackend/internal/routes/
# Ensure routes are under /api/v1/boda/
```

### Issue 2: Authentication Failures
**Solution:**
```bash
# Check JWT configuration
grep -r "JWT_SECRET" jiranibackend/
# Verify token generation and validation
```

### Issue 3: WebSocket Connection Issues
**Solution:**
```bash
# Check WebSocket endpoint
make logs | grep -i websocket
# Verify SSL certificates
```

### Issue 4: Flutter Build Errors
**Solution:**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter pub deps
```

## Performance Monitoring

### Backend Performance
```bash
# Monitor API response times
make test-endpoints | grep "Response Time"

# Check container resources
make status
```

### Frontend Performance
```bash
# Monitor app performance
flutter run --profile
# Check memory usage and frame rates
```

## Next Steps for Feature Development

### 1. Immediate Tasks
- [ ] Verify all endpoints work with authentication
- [ ] Test WebSocket real-time features
- [ ] Confirm map integration works
- [ ] Test ride flow end-to-end

### 2. Development Priorities
- [ ] Enhance real-time tracking accuracy
- [ ] Improve error handling and user feedback
- [ ] Optimize battery usage for location tracking
- [ ] Add comprehensive logging for debugging

### 3. Context Engineering Integration
- [ ] Create PRPs for major enhancements
- [ ] Follow validation gates for all changes
- [ ] Update documentation with new patterns
- [ ] Maintain performance benchmarks

## Success Metrics

### Development Velocity
- **Fast Deploy**: < 2 minutes
- **Full Deploy**: < 5 minutes
- **Test Cycle**: < 30 seconds
- **Hot Reload**: < 3 seconds

### Quality Metrics
- **API Response**: < 200ms
- **WebSocket Latency**: < 100ms
- **App Startup**: < 3 seconds
- **Test Coverage**: > 90%

This guide ensures you can start developing the BodaBoda feature immediately while maintaining the high standards defined in our context engineering framework. The optimized workflow balances development speed with quality assurance, enabling rapid iteration while ensuring production-ready code.
