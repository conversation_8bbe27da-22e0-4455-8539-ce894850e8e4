// BodaBoda Go Fiber Patterns - API Development
// This file contains standard patterns for API development in the BodaBoda backend

package patterns

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/websocket"
	"gorm.io/gorm"
)

// ============================================================================
// PATTERN 1: Standard API Response Structure
// ============================================================================

type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    *Meta       `json:"meta,omitempty"`
}

type Meta struct {
	Page       int `json:"page,omitempty"`
	Limit      int `json:"limit,omitempty"`
	Total      int `json:"total,omitempty"`
	TotalPages int `json:"total_pages,omitempty"`
}

// Success response helper
func SuccessResponse(c *fiber.Ctx, message string, data interface{}) error {
	return c.JSON(APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	})
}

// Error response helper
func ErrorResponse(c *fiber.Ctx, statusCode int, message string, err error) error {
	response := APIResponse{
		Success: false,
		Message: message,
	}
	
	if err != nil {
		response.Error = err.Error()
		log.Printf("API Error: %s - %v", message, err)
	}
	
	return c.Status(statusCode).JSON(response)
}

// Paginated response helper
func PaginatedResponse(c *fiber.Ctx, message string, data interface{}, page, limit, total int) error {
	totalPages := (total + limit - 1) / limit
	
	return c.JSON(APIResponse{
		Success: true,
		Message: message,
		Data:    data,
		Meta: &Meta{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

// ============================================================================
// PATTERN 2: Request Validation and Binding
// ============================================================================

type RideRequestDTO struct {
	PickupLatitude   float64 `json:"pickup_latitude" validate:"required,latitude"`
	PickupLongitude  float64 `json:"pickup_longitude" validate:"required,longitude"`
	PickupAddress    string  `json:"pickup_address" validate:"required,min=5,max=255"`
	DropoffLatitude  float64 `json:"dropoff_latitude" validate:"required,latitude"`
	DropoffLongitude float64 `json:"dropoff_longitude" validate:"required,longitude"`
	DropoffAddress   string  `json:"dropoff_address" validate:"required,min=5,max=255"`
	PaymentMethod    string  `json:"payment_method" validate:"required,oneof=cash card mobile_money"`
	Notes            string  `json:"notes" validate:"max=500"`
}

// Validate and bind request data
func ValidateAndBind(c *fiber.Ctx, dto interface{}) error {
	// Parse JSON body
	if err := c.BodyParser(dto); err != nil {
		return ErrorResponse(c, fiber.StatusBadRequest, "Invalid request body", err)
	}
	
	// Validate struct
	if err := validator.Struct(dto); err != nil {
		return ErrorResponse(c, fiber.StatusBadRequest, "Validation failed", err)
	}
	
	return nil
}

// ============================================================================
// PATTERN 3: Database Transaction Pattern
// ============================================================================

// Execute database operation with transaction
func WithTransaction(fn func(*gorm.DB) error) error {
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()
	
	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}
	
	return tx.Commit().Error
}

// Example: Create ride with transaction
func CreateRideWithTransaction(c *fiber.Ctx) error {
	var req RideRequestDTO
	if err := ValidateAndBind(c, &req); err != nil {
		return err
	}
	
	userID := c.Locals("user_id").(string)
	var ride models.Ride
	
	err := WithTransaction(func(tx *gorm.DB) error {
		// Calculate fare and distance
		distance, duration, fare, err := calculateRideMetrics(
			req.PickupLatitude, req.PickupLongitude,
			req.DropoffLatitude, req.DropoffLongitude,
		)
		if err != nil {
			return err
		}
		
		// Create ride
		ride = models.Ride{
			UserID:           userID,
			Status:           models.RideStatusRequested,
			PickupLatitude:   req.PickupLatitude,
			PickupLongitude:  req.PickupLongitude,
			PickupAddress:    req.PickupAddress,
			DropoffLatitude:  req.DropoffLatitude,
			DropoffLongitude: req.DropoffLongitude,
			DropoffAddress:   req.DropoffAddress,
			Distance:         distance,
			Duration:         duration,
			EstimatedFare:    fare,
			PaymentStatus:    models.PaymentStatusPending,
			Notes:            req.Notes,
		}
		
		if err := tx.Create(&ride).Error; err != nil {
			return err
		}
		
		// Create notification
		notification := models.Notification{
			UserID:  userID,
			Type:    models.RideRequested,
			Title:   "Ride Requested",
			Message: "Your ride request has been submitted. Finding a driver...",
			RideID:  ride.ID,
		}
		
		return tx.Create(&notification).Error
	})
	
	if err != nil {
		return ErrorResponse(c, fiber.StatusInternalServerError, "Failed to create ride", err)
	}
	
	// Broadcast ride request to nearby drivers (outside transaction)
	go broadcastRideRequest(ride)
	
	return SuccessResponse(c, "Ride requested successfully", ride)
}

// ============================================================================
// PATTERN 4: Middleware Pattern
// ============================================================================

// Authentication middleware
func AuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		token := c.Get("Authorization")
		if token == "" {
			return ErrorResponse(c, fiber.StatusUnauthorized, "Authorization token required", nil)
		}
		
		// Remove "Bearer " prefix
		if len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}
		
		// Validate JWT token
		claims, err := validateJWT(token)
		if err != nil {
			return ErrorResponse(c, fiber.StatusUnauthorized, "Invalid token", err)
		}
		
		// Set user context
		c.Locals("user_id", claims.UserID)
		c.Locals("user_role", claims.Role)
		
		return c.Next()
	}
}

// Role-based access control middleware
func RequireRole(requiredRole string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		userRole := c.Locals("user_role").(string)
		
		if userRole != requiredRole && userRole != "admin" {
			return ErrorResponse(c, fiber.StatusForbidden, "Insufficient permissions", nil)
		}
		
		return c.Next()
	}
}

// Rate limiting middleware
func RateLimitMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Next() // Skip rate limiting for unauthenticated requests
		}
		
		// Implement rate limiting logic here
		// This is a simplified example
		key := fmt.Sprintf("rate_limit:%s", userID)
		
		// Check rate limit (implementation depends on your cache/redis setup)
		if isRateLimited(key) {
			return ErrorResponse(c, fiber.StatusTooManyRequests, "Rate limit exceeded", nil)
		}
		
		return c.Next()
	}
}

// ============================================================================
// PATTERN 5: WebSocket Integration Pattern
// ============================================================================

// Broadcast ride request to nearby drivers
func broadcastRideRequest(ride models.Ride) {
	// Find nearby drivers
	nearbyDrivers, err := findNearbyDrivers(
		ride.PickupLatitude,
		ride.PickupLongitude,
		5.0, // 5km radius
	)
	if err != nil {
		log.Printf("Error finding nearby drivers: %v", err)
		return
	}
	
	// Prepare ride request message
	rideData := map[string]interface{}{
		"ride_id":           ride.ID,
		"pickup_latitude":   ride.PickupLatitude,
		"pickup_longitude":  ride.PickupLongitude,
		"pickup_address":    ride.PickupAddress,
		"dropoff_latitude":  ride.DropoffLatitude,
		"dropoff_longitude": ride.DropoffLongitude,
		"dropoff_address":   ride.DropoffAddress,
		"estimated_fare":    ride.EstimatedFare,
		"distance":          ride.Distance,
		"duration":          ride.Duration,
	}
	
	// Broadcast to nearby drivers
	driverIDs := make([]string, len(nearbyDrivers))
	for i, driver := range nearbyDrivers {
		driverIDs[i] = driver.UserID
	}
	
	websocket.BroadcastRideRequest(driverIDs, rideData)
}

// ============================================================================
// PATTERN 6: Error Handling Pattern
// ============================================================================

// Custom error types
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e APIError) Error() string {
	return e.Message
}

// Error handling middleware
func ErrorHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		err := c.Next()
		
		if err != nil {
			// Handle different error types
			switch e := err.(type) {
			case *fiber.Error:
				return ErrorResponse(c, e.Code, e.Message, nil)
			case APIError:
				return ErrorResponse(c, e.Code, e.Message, nil)
			case *gorm.ErrRecordNotFound:
				return ErrorResponse(c, fiber.StatusNotFound, "Resource not found", nil)
			default:
				log.Printf("Unhandled error: %v", err)
				return ErrorResponse(c, fiber.StatusInternalServerError, "Internal server error", nil)
			}
		}
		
		return nil
	}
}

// ============================================================================
// PATTERN 7: Pagination Pattern
// ============================================================================

type PaginationParams struct {
	Page  int `query:"page"`
	Limit int `query:"limit"`
}

func ParsePagination(c *fiber.Ctx) PaginationParams {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return PaginationParams{
		Page:  page,
		Limit: limit,
	}
}

func GetUserRides(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(string)
	pagination := ParsePagination(c)
	
	var rides []models.Ride
	var total int64
	
	// Count total records
	if err := database.DB.Model(&models.Ride{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return ErrorResponse(c, fiber.StatusInternalServerError, "Failed to count rides", err)
	}
	
	// Get paginated results
	offset := (pagination.Page - 1) * pagination.Limit
	if err := database.DB.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pagination.Limit).
		Find(&rides).Error; err != nil {
		return ErrorResponse(c, fiber.StatusInternalServerError, "Failed to get rides", err)
	}
	
	return PaginatedResponse(c, "Rides retrieved successfully", rides, 
		pagination.Page, pagination.Limit, int(total))
}

// ============================================================================
// PATTERN 8: Background Job Pattern
// ============================================================================

// Background job for ride timeout handling
func StartRideTimeoutWorker() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			handleRideTimeouts()
		}
	}
}

func handleRideTimeouts() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// Find rides that have been in "requested" status for too long
	var timedOutRides []models.Ride
	timeout := time.Now().Add(-5 * time.Minute) // 5 minutes timeout
	
	err := database.DB.WithContext(ctx).
		Where("status = ? AND created_at < ?", models.RideStatusRequested, timeout).
		Find(&timedOutRides).Error
	
	if err != nil {
		log.Printf("Error finding timed out rides: %v", err)
		return
	}
	
	// Process timed out rides
	for _, ride := range timedOutRides {
		err := WithTransaction(func(tx *gorm.DB) error {
			// Update ride status
			ride.Status = models.RideStatusCancelled
			ride.CancelledAt = &time.Time{}
			*ride.CancelledAt = time.Now()
			
			if err := tx.Save(&ride).Error; err != nil {
				return err
			}
			
			// Create notification
			notification := models.Notification{
				UserID:  ride.UserID,
				Type:    models.RideCancelled,
				Title:   "Ride Cancelled",
				Message: "Your ride request was cancelled due to no available drivers.",
				RideID:  ride.ID,
			}
			
			return tx.Create(&notification).Error
		})
		
		if err != nil {
			log.Printf("Error handling ride timeout for ride %s: %v", ride.ID, err)
			continue
		}
		
		// Notify user via WebSocket
		websocket.BroadcastNotification(ride.UserID, map[string]interface{}{
			"type":    "ride_cancelled",
			"message": "Your ride request was cancelled due to no available drivers.",
			"ride_id": ride.ID,
		})
	}
}

// ============================================================================
// PATTERN 9: Health Check Pattern
// ============================================================================

func HealthCheck(c *fiber.Ctx) error {
	// Check database connection
	sqlDB, err := database.DB.DB()
	if err != nil {
		return ErrorResponse(c, fiber.StatusServiceUnavailable, "Database connection error", err)
	}
	
	if err := sqlDB.Ping(); err != nil {
		return ErrorResponse(c, fiber.StatusServiceUnavailable, "Database ping failed", err)
	}
	
	// Check WebSocket hub
	connectedUsers := websocket.GetConnectedUsers()
	
	healthData := map[string]interface{}{
		"status":           "healthy",
		"timestamp":        time.Now(),
		"database":         "connected",
		"websocket_users":  connectedUsers,
		"version":          "1.0.0",
	}
	
	return SuccessResponse(c, "Service is healthy", healthData)
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

func calculateRideMetrics(pickupLat, pickupLng, dropoffLat, dropoffLng float64) (float64, int, float64, error) {
	// Implement distance calculation and fare estimation
	// This is a simplified example
	distance := calculateDistance(pickupLat, pickupLng, dropoffLat, dropoffLng)
	duration := int(distance * 3) // Rough estimate: 3 minutes per km
	fare := distance * 50.0       // 50 KES per km
	
	return distance, duration, fare, nil
}

func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	// Implement Haversine formula or use a mapping service
	// This is a simplified placeholder
	return 5.0 // Return 5km as example
}

func findNearbyDrivers(lat, lng, radiusKm float64) ([]models.Driver, error) {
	var drivers []models.Driver
	
	err := database.DB.Where("is_available = ? AND is_verified = ?", true, true).
		Find(&drivers).Error
	
	if err != nil {
		return nil, err
	}
	
	// Filter by distance (simplified)
	var nearbyDrivers []models.Driver
	for _, driver := range drivers {
		if driver.CurrentLatitude != 0 && driver.CurrentLongitude != 0 {
			distance := calculateDistance(lat, lng, driver.CurrentLatitude, driver.CurrentLongitude)
			if distance <= radiusKm {
				nearbyDrivers = append(nearbyDrivers, driver)
			}
		}
	}
	
	return nearbyDrivers, nil
}

func validateJWT(token string) (*JWTClaims, error) {
	// Implement JWT validation
	// Return claims if valid
	return &JWTClaims{
		UserID: "user_123",
		Role:   "user",
	}, nil
}

func isRateLimited(key string) bool {
	// Implement rate limiting check
	// Return true if rate limited
	return false
}

type JWTClaims struct {
	UserID string `json:"user_id"`
	Role   string `json:"role"`
}
