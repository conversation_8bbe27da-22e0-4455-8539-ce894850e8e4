// BodaBoda Flutter Patterns - Riverpod State Management for Augment AI
// This file contains standard patterns for state management in the BodaBoda feature
// Reference this file using: @bodaboda-context-engineering/examples/flutter_patterns/riverpod_state_management.dart
// NOTE: This is a pattern/template file - imports may not resolve as this is for reference only

import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:jirani_app/models/ride.dart';
import 'package:jirani_app/models/rider.dart';
import 'package:jirani_app/models/lat_lng.dart' as custom;
import 'package:jirani_app/services/logging_service.dart';

// ============================================================================
// PATTERN 1: StateNotifierProvider for Complex State Management
// ============================================================================

/// Provider for managing current ride state with complex business logic
final currentRideProvider =
    StateNotifierProvider<CurrentRideNotifier, AsyncValue<Ride?>>((ref) {
  return CurrentRideNotifier(ref);
});

class CurrentRideNotifier extends StateNotifier<AsyncValue<Ride?>> {
  final Ref _ref;
  StreamSubscription? _rideStatusSubscription;

  CurrentRideNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initializeRideTracking();
  }

  /// Initialize ride tracking with WebSocket subscription
  void _initializeRideTracking() {
    try {
      // Listen to WebSocket messages for ride updates
      final webSocketService = _ref.read(webSocketServiceProvider);
      _rideStatusSubscription = webSocketService.messageStream
          .where((message) =>
              message.type == WebSocketMessageType.rideStatusUpdate)
          .listen(_handleRideStatusUpdate);

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      LoggingService.e('Failed to initialize ride tracking',
          error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Handle ride status updates from WebSocket
  void _handleRideStatusUpdate(WebSocketMessage message) {
    try {
      final rideData = message.data;
      final updatedRide = Ride.fromJson(rideData);

      state = AsyncValue.data(updatedRide);

      // Trigger side effects based on status
      _handleStatusSideEffects(updatedRide);
    } catch (e, stackTrace) {
      LoggingService.e('Failed to handle ride status update',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Handle side effects when ride status changes
  void _handleStatusSideEffects(Ride ride) {
    switch (ride.status) {
      case RideStatus.accepted:
        _ref.read(notificationServiceProvider).showRideAccepted(ride);
        break;
      case RideStatus.enRouteToPickup:
        _ref.read(geofencingServiceProvider).startPickupMonitoring(ride.pickup);
        break;
      case RideStatus.arrivedAtPickup:
        _ref.read(notificationServiceProvider).showDriverArrived(ride);
        break;
      case RideStatus.inProgress:
        _ref
            .read(geofencingServiceProvider)
            .startDestinationMonitoring(ride.dropoff);
        break;
      case RideStatus.completed:
        _ref.read(geofencingServiceProvider).stopAllMonitoring();
        break;
      default:
        break;
    }
  }

  /// Request a new ride
  Future<void> requestRide({
    required custom.LatLng pickup,
    required custom.LatLng dropoff,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      state = const AsyncValue.loading();

      final rideService = _ref.read(rideServiceProvider);
      final ride = await rideService.createRideRequest(
        pickup: pickup,
        dropoff: dropoff,
        paymentMethod: paymentMethod,
      );

      if (ride != null) {
        state = AsyncValue.data(ride);

        // Broadcast ride request to nearby drivers
        final webSocketService = _ref.read(webSocketServiceProvider);
        webSocketService.broadcastRideRequest(ride);
      } else {
        throw Exception('Failed to create ride request');
      }
    } catch (e, stackTrace) {
      LoggingService.e('Failed to request ride',
          error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Cancel current ride
  Future<void> cancelRide() async {
    final currentRide = state.value;
    if (currentRide == null) return;

    try {
      final rideService = _ref.read(rideServiceProvider);
      await rideService.cancelRide(currentRide.id);

      state = const AsyncValue.data(null);

      // Clean up side effects
      _ref.read(geofencingServiceProvider).stopAllMonitoring();
    } catch (e, stackTrace) {
      LoggingService.e('Failed to cancel ride',
          error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  @override
  void dispose() {
    _rideStatusSubscription?.cancel();
    super.dispose();
  }
}

// ============================================================================
// PATTERN 2: FutureProvider for Async Data Fetching
// ============================================================================

/// Provider for fetching nearby riders with automatic refresh
final nearbyRidersProvider =
    FutureProvider.autoDispose<List<Rider>>((ref) async {
  // Get user location
  final userLocationAsync = ref.watch(userLocationProvider);

  return userLocationAsync.when(
    data: (location) async {
      if (location == null) return [];

      final riderService = ref.read(riderServiceProvider);
      return await riderService.getNearbyRiders(
        userLocation: location,
        radiusInKm: 5.0,
      );
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// ============================================================================
// PATTERN 3: StreamProvider for Real-time Data
// ============================================================================

/// Provider for real-time driver location updates
final driverLocationProvider =
    StreamProvider.family<custom.LatLng?, String>((ref, driverId) {
  final webSocketService = ref.read(webSocketServiceProvider);

  return webSocketService.messageStream
      .where((message) =>
          message.type == WebSocketMessageType.driverLocation &&
          message.data['driver_id'] == driverId)
      .map((message) {
    final lat = message.data['latitude'] as double?;
    final lng = message.data['longitude'] as double?;

    if (lat != null && lng != null) {
      return custom.LatLng(lat, lng);
    }
    return null;
  });
});

// ============================================================================
// PATTERN 4: StateProvider for Simple State
// ============================================================================

/// Provider for selected payment method
final selectedPaymentMethodProvider = StateProvider<PaymentMethod>((ref) {
  return PaymentMethod.cash; // Default payment method
});

/// Provider for pickup location
final pickupLocationProvider = StateProvider<custom.LatLng?>((ref) {
  return null;
});

/// Provider for dropoff location
final dropoffLocationProvider = StateProvider<custom.LatLng?>((ref) {
  return null;
});

// ============================================================================
// PATTERN 5: Provider with Dependencies and Error Handling
// ============================================================================

/// Provider for estimated fare calculation with dependency injection
final estimatedFareProvider = FutureProvider.autoDispose<double?>((ref) async {
  final pickup = ref.watch(pickupLocationProvider);
  final dropoff = ref.watch(dropoffLocationProvider);

  // Return null if either location is missing
  if (pickup == null || dropoff == null) {
    return null;
  }

  try {
    final fareService = ref.read(fareCalculationServiceProvider);
    return await fareService.calculateFare(
      pickup: pickup,
      dropoff: dropoff,
    );
  } catch (e, stackTrace) {
    LoggingService.e('Failed to calculate fare',
        error: e, stackTrace: stackTrace);
    // Return null instead of throwing to allow UI to handle gracefully
    return null;
  }
});

// ============================================================================
// PATTERN 6: Provider with Caching and Refresh Logic
// ============================================================================

/// Provider for ride history with caching
final rideHistoryProvider =
    StateNotifierProvider<RideHistoryNotifier, AsyncValue<List<Ride>>>((ref) {
  return RideHistoryNotifier(ref);
});

class RideHistoryNotifier extends StateNotifier<AsyncValue<List<Ride>>> {
  final Ref _ref;
  DateTime? _lastFetch;
  static const _cacheTimeout = Duration(minutes: 5);

  RideHistoryNotifier(this._ref) : super(const AsyncValue.loading()) {
    _fetchRideHistory();
  }

  /// Fetch ride history with caching logic
  Future<void> _fetchRideHistory() async {
    // Check if we need to refresh based on cache timeout
    if (_lastFetch != null &&
        DateTime.now().difference(_lastFetch!) < _cacheTimeout &&
        state is AsyncData) {
      return; // Use cached data
    }

    try {
      state = const AsyncValue.loading();

      final rideService = _ref.read(rideServiceProvider);
      final rides = await rideService.getUserRideHistory();

      state = AsyncValue.data(rides);
      _lastFetch = DateTime.now();
    } catch (e, stackTrace) {
      LoggingService.e('Failed to fetch ride history',
          error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Force refresh ride history
  Future<void> refresh() async {
    _lastFetch = null;
    await _fetchRideHistory();
  }

  /// Add new ride to history
  void addRide(Ride ride) {
    state.whenData((rides) {
      final updatedRides = [ride, ...rides];
      state = AsyncValue.data(updatedRides);
    });
  }
}

// ============================================================================
// PATTERN 7: Service Providers (Dependency Injection)
// ============================================================================

/// WebSocket service provider
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService();
});

/// Ride service provider
final rideServiceProvider = Provider<RideService>((ref) {
  return RideService();
});

/// Rider service provider
final riderServiceProvider = Provider<RiderService>((ref) {
  return RiderService();
});

/// Notification service provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

/// Geofencing service provider
final geofencingServiceProvider = Provider<GeofencingService>((ref) {
  return GeofencingService();
});

/// Fare calculation service provider
final fareCalculationServiceProvider = Provider<FareCalculationService>((ref) {
  return FareCalculationService();
});

// ============================================================================
// PATTERN 8: Computed Providers (Derived State)
// ============================================================================

/// Provider for available riders (filtered from nearby riders)
final availableRidersProvider = Provider.autoDispose<List<Rider>>((ref) {
  final nearbyRidersAsync = ref.watch(nearbyRidersProvider);

  return nearbyRidersAsync.when(
    data: (riders) =>
        riders.where((rider) => rider.status == RiderStatus.available).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for ride request validation
final canRequestRideProvider = Provider.autoDispose<bool>((ref) {
  final pickup = ref.watch(pickupLocationProvider);
  final dropoff = ref.watch(dropoffLocationProvider);
  final currentRide = ref.watch(currentRideProvider);

  return pickup != null && dropoff != null && currentRide.value == null;
});

// ============================================================================
// USAGE PATTERNS IN WIDGETS
// ============================================================================

/*
// Pattern 1: Basic provider consumption
class RideStatusWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentRideAsync = ref.watch(currentRideProvider);
    
    return currentRideAsync.when(
      data: (ride) => ride != null 
          ? RideActiveWidget(ride: ride)
          : NoActiveRideWidget(),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => ErrorWidget(error: error),
    );
  }
}

// Pattern 2: Provider with side effects
class RideRequestButton extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final canRequest = ref.watch(canRequestRideProvider);
    
    return ElevatedButton(
      onPressed: canRequest ? () async {
        final pickup = ref.read(pickupLocationProvider);
        final dropoff = ref.read(dropoffLocationProvider);
        final paymentMethod = ref.read(selectedPaymentMethodProvider);
        
        await ref.read(currentRideProvider.notifier).requestRide(
          pickup: pickup!,
          dropoff: dropoff!,
          paymentMethod: paymentMethod,
        );
      } : null,
      child: Text('Request Ride'),
    );
  }
}

// Pattern 3: Listening to provider changes
class RideTrackingScreen extends ConsumerStatefulWidget {
  @override
  _RideTrackingScreenState createState() => _RideTrackingScreenState();
}

class _RideTrackingScreenState extends ConsumerState<RideTrackingScreen> {
  @override
  void initState() {
    super.initState();
    
    // Listen to ride status changes
    ref.listenManual(currentRideProvider, (previous, next) {
      next.whenData((ride) {
        if (ride?.status == RideStatus.completed) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (_) => RideCompletionScreen(ride: ride!)),
          );
        }
      });
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer(
        builder: (context, ref, child) {
          final currentRideAsync = ref.watch(currentRideProvider);
          
          return currentRideAsync.when(
            data: (ride) => ride != null 
                ? RideTrackingMap(ride: ride)
                : NoActiveRideWidget(),
            loading: () => LoadingWidget(),
            error: (error, stack) => ErrorWidget(error: error),
          );
        },
      ),
    );
  }
}
*/
