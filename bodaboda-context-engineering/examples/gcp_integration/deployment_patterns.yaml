# BodaBoda GCP Integration Patterns
# This file contains standard patterns for deploying and managing the BodaBoda feature on Google Cloud Platform

# ============================================================================
# PATTERN 1: Docker Compose for Development
# ============================================================================
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: jirani_postgres
    environment:
      POSTGRES_DB: jirani_dev
      POSTGRES_USER: jirani_user
      POSTGRES_PASSWORD: jirani_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - jirani_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jirani_user -d jirani_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching and Session Management
  redis:
    image: redis:7-alpine
    container_name: jirani_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - jirani_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Go Backend API
  api:
    build:
      context: ./jiranibackend
      dockerfile: Dockerfile
    container_name: jirani_api
    environment:
      - ENV=development
      - DATABASE_URL=****************************************************/jirani_dev?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your_jwt_secret_here
      - PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - jirani_network
    volumes:
      - ./jiranibackend:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: jirani_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - jirani_network

volumes:
  postgres_data:
  redis_data:

networks:
  jirani_network:
    driver: bridge

---
# ============================================================================
# PATTERN 2: Kubernetes Deployment for Production
# ============================================================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jirani-api
  namespace: jirani-production
  labels:
    app: jirani-api
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: jirani-api
  template:
    metadata:
      labels:
        app: jirani-api
        version: v1.0.0
    spec:
      containers:
      - name: jirani-api
        image: gcr.io/gen-lang-client-**********/jirani-api:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: jirani-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: jirani-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jirani-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true

---
# ============================================================================
# PATTERN 3: Service Configuration
# ============================================================================
apiVersion: v1
kind: Service
metadata:
  name: jirani-api-service
  namespace: jirani-production
  labels:
    app: jirani-api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: jirani-api

---
# ============================================================================
# PATTERN 4: Horizontal Pod Autoscaler
# ============================================================================
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: jirani-api-hpa
  namespace: jirani-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: jirani-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# ============================================================================
# PATTERN 5: Ingress Configuration with SSL
# ============================================================================
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jirani-api-ingress
  namespace: jirani-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.jirani.tufiked.live
    secretName: jirani-api-tls
  rules:
  - host: api.jirani.tufiked.live
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jirani-api-service
            port:
              number: 80

---
# ============================================================================
# PATTERN 6: ConfigMap for Application Configuration
# ============================================================================
apiVersion: v1
kind: ConfigMap
metadata:
  name: jirani-api-config
  namespace: jirani-production
data:
  app.yaml: |
    server:
      port: 8080
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 120s
    
    database:
      max_open_conns: 25
      max_idle_conns: 5
      conn_max_lifetime: 300s
    
    redis:
      pool_size: 10
      min_idle_conns: 5
      dial_timeout: 5s
      read_timeout: 3s
      write_timeout: 3s
    
    websocket:
      read_buffer_size: 1024
      write_buffer_size: 1024
      heartbeat_interval: 30s
      max_connections: 1000
    
    rate_limiting:
      requests_per_minute: 100
      burst_size: 20
    
    logging:
      level: info
      format: json
      output: stdout

---
# ============================================================================
# PATTERN 7: Secret Management
# ============================================================================
apiVersion: v1
kind: Secret
metadata:
  name: jirani-secrets
  namespace: jirani-production
type: Opaque
data:
  # Base64 encoded values
  database-url: <base64-encoded-database-url>
  redis-url: <base64-encoded-redis-url>
  jwt-secret: <base64-encoded-jwt-secret>
  mapbox-token: <base64-encoded-mapbox-token>

---
# ============================================================================
# PATTERN 8: Network Policy for Security
# ============================================================================
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: jirani-api-network-policy
  namespace: jirani-production
spec:
  podSelector:
    matchLabels:
      app: jirani-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# ============================================================================
# PATTERN 9: Monitoring and Observability
# ============================================================================
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: jirani-api-metrics
  namespace: jirani-production
  labels:
    app: jirani-api
spec:
  selector:
    matchLabels:
      app: jirani-api
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
# ============================================================================
# PATTERN 10: Cloud SQL Proxy for Database Connection
# ============================================================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloudsql-proxy
  namespace: jirani-production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloudsql-proxy
  template:
    metadata:
      labels:
        app: cloudsql-proxy
    spec:
      serviceAccountName: cloudsql-proxy
      containers:
      - name: cloudsql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:1.33.2
        command:
        - "/cloud_sql_proxy"
        - "-instances=gen-lang-client-**********:us-central1:jirani-db=tcp:0.0.0.0:5432"
        - "-credential_file=/secrets/service_account.json"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: cloudsql-instance-credentials
          mountPath: /secrets/
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: cloudsql-instance-credentials
        secret:
          secretName: cloudsql-instance-credentials

---
apiVersion: v1
kind: Service
metadata:
  name: cloudsql-proxy-service
  namespace: jirani-production
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: cloudsql-proxy
