#!/bin/bash
# Optimized BodaBoda Development Workflow
# Context Engineering Compliant Deployment Script

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-**********"
REMOTE_DIR="/home/<USER>/jirani-backend"
DOCKER_REPO="kimathinrian"
APP_NAME="jirani-api"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev-$(date +%s)")

# Context Engineering Validation Gates
VALIDATION_ENABLED=true
PERFORMANCE_CHECK=true
SECURITY_CHECK=true
HEALTH_CHECK=true

# Deployment Options
FAST_MODE=false
SKIP_TESTS=false
SKIP_BUILD=false
FORCE_REBUILD=false
DRY_RUN=false

print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}$message${NC}"
}

print_step() {
    print_colored "$BLUE" "🔄 $1"
}

print_success() {
    print_colored "$GREEN" "✅ $1"
}

print_warning() {
    print_colored "$YELLOW" "⚠️  $1"
}

print_error() {
    print_colored "$RED" "❌ $1"
}

print_usage() {
    echo "Optimized BodaBoda Development Workflow"
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --fast              Fast deployment (skip validation gates)"
    echo "  --skip-tests        Skip running tests"
    echo "  --skip-build        Skip building (use existing image)"
    echo "  --force-rebuild     Force complete rebuild"
    echo "  --dry-run           Show what would be done without executing"
    echo "  --no-validation     Skip context engineering validation"
    echo "  --help              Show this help message"
    echo ""
    echo "Context Engineering Features:"
    echo "  - Pre-deployment validation gates"
    echo "  - Performance benchmarking"
    echo "  - Security checks"
    echo "  - Health verification"
    echo "  - Incremental deployment"
}

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --fast) FAST_MODE=true ;;
        --skip-tests) SKIP_TESTS=true ;;
        --skip-build) SKIP_BUILD=true ;;
        --force-rebuild) FORCE_REBUILD=true ;;
        --dry-run) DRY_RUN=true ;;
        --no-validation) VALIDATION_ENABLED=false ;;
        --help) print_usage; exit 0 ;;
        *) print_error "Unknown parameter: $1"; print_usage; exit 1 ;;
    esac
    shift
done

# Fast mode adjustments
if [ "$FAST_MODE" = true ]; then
    VALIDATION_ENABLED=false
    SKIP_TESTS=true
    print_warning "Fast mode enabled - skipping validation gates"
fi

# Context Engineering: Pre-Implementation Validation
validate_pre_implementation() {
    if [ "$VALIDATION_ENABLED" = false ]; then
        return 0
    fi
    
    print_step "Running Context Engineering Pre-Implementation Validation"
    
    # Check if we're in the correct directory
    if [ ! -f "go.mod" ] || [ ! -d "internal" ]; then
        print_error "Not in jirani backend directory"
        return 1
    fi
    
    # Check if BodaBoda context files exist
    if [ ! -f "../bodaboda-context-engineering/CLAUDE.md" ]; then
        print_warning "BodaBoda context engineering files not found"
    fi
    
    # Validate Go code
    print_step "Validating Go code..."
    if ! go vet ./...; then
        print_error "Go vet failed"
        return 1
    fi
    
    # Check for BodaBoda specific files
    if [ ! -f "internal/controllers/boda_controller.go" ]; then
        print_error "BodaBoda controller not found"
        return 1
    fi
    
    print_success "Pre-implementation validation passed"
    return 0
}

# Context Engineering: Run Tests
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        print_warning "Skipping tests"
        return 0
    fi
    
    print_step "Running tests with coverage..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would run go test -cover ./..."
        return 0
    fi
    
    if ! go test -cover ./...; then
        print_error "Tests failed"
        return 1
    fi
    
    print_success "Tests passed"
    return 0
}

# Smart Build - Only build if needed
smart_build() {
    if [ "$SKIP_BUILD" = true ]; then
        print_warning "Skipping build"
        return 0
    fi
    
    print_step "Checking if build is needed..."
    
    # Check if binary exists and is newer than source files
    if [ "$FORCE_REBUILD" = false ] && [ -f "$APP_NAME" ]; then
        # Check if any Go files are newer than the binary
        if [ -z "$(find . -name "*.go" -newer "$APP_NAME")" ]; then
            print_success "Binary is up to date, skipping build"
            return 0
        fi
    fi
    
    print_step "Building application..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would build application"
        return 0
    fi
    
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $APP_NAME ./cmd/api/main.go
    
    print_success "Build completed"
    return 0
}

# Smart Docker Build - Only build if needed
smart_docker_build() {
    print_step "Checking if Docker image needs rebuilding..."
    
    # Check if image exists locally
    if docker image inspect $DOCKER_REPO/$APP_NAME:latest >/dev/null 2>&1; then
        # Get image creation time
        local image_created=$(docker image inspect $DOCKER_REPO/$APP_NAME:latest --format '{{.Created}}')
        local image_timestamp=$(date -d "$image_created" +%s)
        local binary_timestamp=$(stat -c %Y $APP_NAME 2>/dev/null || echo 0)
        
        if [ "$FORCE_REBUILD" = false ] && [ $image_timestamp -gt $binary_timestamp ]; then
            print_success "Docker image is up to date"
            return 0
        fi
    fi
    
    print_step "Building Docker image..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would build Docker image"
        return 0
    fi
    
    docker build -t $DOCKER_REPO/$APP_NAME:$VERSION -t $DOCKER_REPO/$APP_NAME:latest .
    
    print_success "Docker image built"
    return 0
}

# Push Docker image
push_docker_image() {
    print_step "Pushing Docker image to registry..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would push Docker image"
        return 0
    fi
    
    docker push $DOCKER_REPO/$APP_NAME:$VERSION
    docker push $DOCKER_REPO/$APP_NAME:latest
    
    print_success "Docker image pushed"
    return 0
}

# Incremental deployment - only restart API if needed
incremental_deploy() {
    print_step "Performing incremental deployment..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would perform incremental deployment"
        return 0
    fi
    
    # Check if API container needs updating
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        cd $REMOTE_DIR
        echo 'Checking if API update is needed...'
        
        # Pull latest image
        docker-compose -f docker-compose.prod.yml pull api
        
        # Restart only API service
        docker-compose -f docker-compose.prod.yml up -d api
        
        echo 'API service updated'
    "
    
    print_success "Incremental deployment completed"
    return 0
}

# Context Engineering: Health Check
verify_deployment() {
    if [ "$HEALTH_CHECK" = false ]; then
        return 0
    fi
    
    print_step "Verifying deployment health..."
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN: Would verify deployment"
        return 0
    fi
    
    # Wait for services to start
    sleep 15
    
    # Check container status
    print_step "Checking container status..."
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml ps
    "
    
    # Test BodaBoda endpoints
    print_step "Testing BodaBoda endpoints..."
    
    # Test nearby drivers endpoint (should return 401 without auth, but endpoint should exist)
    local drivers_test=$(gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        curl -s -o /dev/null -w '%{http_code}' https://api.jirani.tufiked.live/api/boda/drivers?latitude=-1.2921&longitude=36.8219
    " 2>/dev/null || echo "000")
    
    if [ "$drivers_test" = "401" ] || [ "$drivers_test" = "200" ]; then
        print_success "BodaBoda drivers endpoint responding"
    else
        print_warning "BodaBoda drivers endpoint returned: $drivers_test"
    fi
    
    print_success "Deployment verification completed"
    return 0
}

# Main execution flow
main() {
    print_step "Starting Optimized BodaBoda Deployment"
    print_step "Version: $VERSION"
    
    # Context Engineering Validation Gates
    validate_pre_implementation || exit 1
    run_tests || exit 1
    
    # Build and Deploy
    smart_build || exit 1
    smart_docker_build || exit 1
    push_docker_image || exit 1
    incremental_deploy || exit 1
    
    # Post-deployment validation
    verify_deployment || exit 1
    
    print_success "🎉 Optimized deployment completed successfully!"
    print_success "API available at: https://api.jirani.tufiked.live"
    print_success "BodaBoda endpoints ready for testing"
}

# Execute main function
main "$@"
