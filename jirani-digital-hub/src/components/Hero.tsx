
import React from 'react';
import { Button } from '@/components/ui/button';

const Hero: React.FC = () => {
  return (
    <section 
      id="home"
      className="relative pt-28 pb-20 md:pt-32 md:pb-28 overflow-hidden bg-gradient-to-br from-primary/5 via-white to-secondary/5"
    >
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex flex-col lg:flex-row items-center">
          <div className="w-full lg:w-1/2 text-center lg:text-left mb-10 lg:mb-0">
            <div className="inline-block bg-primary/10 text-primary px-4 py-1 rounded-full font-montserrat text-sm font-semibold mb-4">
              Multi-Service App
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-secondary mb-6 font-poppins leading-tight">
              Your Neighborhood Services, <span className="text-primary">Delivered</span>
            </h1>
            <p className="text-lg md:text-xl text-text-muted mb-8 max-w-lg mx-auto lg:mx-0">
              From rides to meals, Jirani connects you with local services in seconds. Experience convenience at your fingertips while building community.
            </p>
            <div className="flex flex-col sm:flex-row justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-4">
              <Button className="button-primary flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z" /><path d="M12 7c2 0 3-1 3-1" /></svg>
                App Store
              </Button>
              <Button className="button-secondary flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="5 3 19 12 5 21 5 3" /></svg>
                Google Play
              </Button>
            </div>
          </div>
          
          <div className="w-full lg:w-1/2 relative">
            <div className="relative z-10 animate-float">
              <div className="bg-secondary/10 backdrop-blur-sm rounded-3xl p-4 rotate-3 shadow-xl">
                <img 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=460&q=80" 
                  alt="Jirani App Interface" 
                  className="rounded-2xl border-8 border-white shadow-lg max-w-[300px] mx-auto"
                />
              </div>
            </div>
            
            <div className="absolute -top-12 right-10 w-16 h-16 bg-primary/30 rounded-full backdrop-blur-md animate-pulse-light"></div>
            <div className="absolute top-1/3 -left-8 w-24 h-24 bg-accent/20 rounded-full backdrop-blur-md animate-pulse-light delay-300"></div>
            <div className="absolute bottom-10 right-4 w-20 h-20 bg-secondary/20 rounded-full backdrop-blur-md animate-pulse-light delay-700"></div>
          </div>
        </div>
      </div>
      
      <div className="absolute -bottom-10 left-0 right-0 h-20 bg-gradient-to-b from-transparent to-white z-10"></div>
      
      {/* Decorative elements */}
      <div className="hidden lg:block absolute top-20 left-10 w-4 h-4 bg-primary rounded-full"></div>
      <div className="hidden lg:block absolute bottom-40 left-20 w-6 h-6 bg-accent rounded-full"></div>
      <div className="hidden lg:block absolute top-40 right-20 w-8 h-8 border-2 border-secondary rounded-full"></div>
    </section>
  );
};

export default Hero;
