
import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface FAQItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onClick: () => void;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, isOpen, onClick }) => {
  return (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        className="flex justify-between items-center w-full py-4 text-left"
        onClick={onClick}
      >
        <h3 className="font-medium text-secondary font-poppins">{question}</h3>
        <span className="ml-6 flex-shrink-0 text-primary">
          {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </span>
      </button>
      {isOpen && (
        <div className="pb-4 text-text-muted">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
};

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState(0);
  
  const faqs = [
    {
      question: "How do I download the Jirani app?",
      answer: "The Jirani app is available for download on both the App Store for iOS devices and Google Play Store for Android devices. Simply search for 'Jirani' in either store and tap the download button."
    },
    {
      question: "Is Jirani available in my area?",
      answer: "Jirani is currently available in major cities and surrounding areas. We're expanding rapidly to new locations. When you download the app, you can check if service is available in your specific location."
    },
    {
      question: "How do payments work in the app?",
      answer: "Jirani offers multiple secure payment options including mobile money, credit/debit cards, and cash for certain services. All digital payments are processed securely, and you'll receive receipts for all transactions."
    },
    {
      question: "How do I become a service provider on Jirani?",
      answer: "If you're interested in becoming a service provider (driver, food delivery partner, professional service provider), you can sign up through the 'Become a Partner' section in the app or visit our website to fill out an application."
    },
    {
      question: "Is there a customer loyalty program?",
      answer: "Yes! Jirani rewards loyal users with points for every service booked through the app. These points can be redeemed for discounts on future services, special offers, and exclusive benefits."
    },
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  return (
    <section id="faq" className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="section-heading">Frequently Asked Questions</h2>
          <p className="section-subheading">Find answers to common questions about using Jirani</p>
        </div>
        
        <div className="max-w-3xl mx-auto bg-muted rounded-xl p-6 md:p-8 shadow-sm">
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              onClick={() => toggleFAQ(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
