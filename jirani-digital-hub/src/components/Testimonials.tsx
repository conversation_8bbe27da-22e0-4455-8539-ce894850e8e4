
import React, { useState } from 'react';
import { Star } from 'lucide-react';

interface TestimonialProps {
  name: string;
  role: string;
  image: string;
  rating: number;
  comment: string;
}

const TestimonialCard: React.FC<TestimonialProps> = ({ name, role, image, rating, comment }) => {
  return (
    <div className="bg-white rounded-xl shadow-md p-6 flex flex-col h-full">
      <div className="flex items-center mb-4">
        <img 
          src={image} 
          alt={name} 
          className="w-12 h-12 rounded-full object-cover mr-4 border-2 border-primary"
        />
        <div>
          <h4 className="font-semibold text-secondary font-poppins">{name}</h4>
          <p className="text-sm text-text-muted">{role}</p>
        </div>
      </div>
      
      <div className="flex mb-3">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            size={16}
            className={`${i < rating ? 'text-primary fill-primary' : 'text-text-muted'}`}
          />
        ))}
      </div>
      
      <p className="italic text-text-muted mb-4 flex-grow">{comment}</p>
      
      <div className="text-xs text-text-muted">Verified User</div>
    </div>
  );
};

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      name: "David Mwangi",
      role: "Daily Commuter",
      image: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      rating: 5,
      comment: "Jirani has transformed my daily commute. The BodaBoda service is reliable and affordable, and I love supporting local riders in my community."
    },
    {
      name: "Sarah Achieng",
      role: "Food Enthusiast",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      rating: 4,
      comment: "The food delivery is amazing! I can order from local restaurants that never delivered before. Fast service and the food arrives hot!"
    },
    {
      name: "Michael Omondi",
      role: "Business Owner",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      rating: 5,
      comment: "As a small business owner, Jirani has helped me reach more customers and grow my business. The platform is easy to use and the support team is very helpful."
    },
  ];

  return (
    <section id="testimonials" className="py-20 bg-muted">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="section-heading">What Our Users Say</h2>
          <p className="section-subheading">Hear from community members who use Jirani every day</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              name={testimonial.name}
              role={testimonial.role}
              image={testimonial.image}
              rating={testimonial.rating}
              comment={testimonial.comment}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
