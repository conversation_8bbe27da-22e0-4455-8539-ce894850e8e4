
import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const AppScreenshots: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const screenshots = [
    {
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      caption: "Search for rides or services in your area with ease"
    },
    {
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      caption: "Track your ride or delivery in real-time"
    },
    {
      image: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      caption: "Browse and order from local restaurants"
    },
    {
      image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      caption: "Pay securely with multiple payment options"
    },
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === screenshots.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? screenshots.length - 1 : prevIndex - 1
    );
  };

  return (
    <section className="py-20 bg-gradient-to-br from-secondary to-secondary/80 text-white">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-poppins">App Screenshots</h2>
          <p className="text-xl md:text-2xl font-medium text-white/70 mb-8 font-inter max-w-2xl mx-auto">
            See how Jirani helps you connect with local services
          </p>
        </div>
        
        <div className="relative max-w-4xl mx-auto">
          <div className="flex overflow-x-hidden">
            <div className="flex transition-transform duration-500 ease-in-out" style={{ transform: `translateX(-${currentIndex * 100}%)` }}>
              {screenshots.map((screenshot, index) => (
                <div key={index} className="min-w-full flex flex-col md:flex-row items-center justify-center px-4">
                  <div className="relative mb-8 md:mb-0 md:mr-8">
                    <div className="relative">
                      <div className="bg-gradient-to-r from-primary to-accent p-2 rounded-3xl shadow-xl">
                        <img 
                          src={screenshot.image} 
                          alt={`Screenshot ${index + 1}`} 
                          className="rounded-2xl border-4 border-white shadow-lg max-w-[200px] md:max-w-[250px]"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="md:w-1/2 text-center md:text-left">
                    <h3 className="text-2xl font-semibold mb-4 font-poppins">Screenshot {index + 1}</h3>
                    <p className="text-white/80 text-lg">{screenshot.caption}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <button 
            onClick={prevSlide} 
            className="absolute top-1/2 left-2 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 text-white p-2 rounded-full backdrop-blur-sm transition-colors z-10"
          >
            <ChevronLeft size={24} />
          </button>
          
          <button 
            onClick={nextSlide} 
            className="absolute top-1/2 right-2 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 text-white p-2 rounded-full backdrop-blur-sm transition-colors z-10"
          >
            <ChevronRight size={24} />
          </button>
          
          <div className="flex justify-center mt-8 space-x-2">
            {screenshots.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${index === currentIndex ? 'bg-primary' : 'bg-white/30 hover:bg-white/50'}`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppScreenshots;
