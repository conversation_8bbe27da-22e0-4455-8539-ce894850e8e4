
import React from 'react';
import { Clock, MapPin, Users } from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6 transform transition-transform duration-300 hover:-translate-y-2 hover:shadow-xl">
      <div className="flex flex-col items-center text-center">
        <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-5 text-primary">
          {icon}
        </div>
        <h3 className="text-xl font-bold mb-3 font-poppins text-secondary">{title}</h3>
        <p className="text-text-muted">{description}</p>
      </div>
    </div>
  );
};

const Features: React.FC = () => {
  const features = [
    {
      icon: <MapPin size={32} />,
      title: "BodaBoda Rides",
      description: "Quick, affordable transportation at your fingertips. Get picked up in minutes and reach your destination safely."
    },
    {
      icon: <Clock size={32} />,
      title: "Food Delivery",
      description: "Your favorite local restaurants delivered to your door. Enjoy meals from community kitchens to premium restaurants."
    },
    {
      icon: <Users size={32} />,
      title: "Local Services",
      description: "Connect with trusted service providers in your area. From home repairs to professional services, find what you need."
    }
  ];

  return (
    <section id="features" className="py-20 bg-muted">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="section-heading">Our Features</h2>
          <p className="section-subheading">Discover how Jirani makes your life easier by connecting you with essential services in your community</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
