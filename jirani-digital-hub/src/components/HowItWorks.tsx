
import React from 'react';

interface StepProps {
  number: number;
  title: string;
  description: string;
  isLast?: boolean;
}

const Step: React.FC<StepProps> = ({ number, title, description, isLast = false }) => {
  return (
    <div className="flex">
      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center font-bold text-xl">
          {number}
        </div>
        {!isLast && (
          <div className="h-full w-0.5 bg-primary/30 my-2"></div>
        )}
      </div>
      <div className="ml-6 pb-8">
        <h3 className="text-xl font-semibold text-secondary mb-2 font-poppins">{title}</h3>
        <p className="text-text-muted">{description}</p>
      </div>
    </div>
  );
};

const HowItWorks: React.FC = () => {
  const steps = [
    {
      title: "Download the App",
      description: "Get <PERSON><PERSON> from the App Store or Google Play and install it on your smartphone."
    },
    {
      title: "Create Your Account",
      description: "Sign up with your phone number or email and complete your profile."
    },
    {
      title: "Choose Your Service",
      description: "Select from rides, food delivery, or other local services based on your needs."
    },
    {
      title: "Enjoy Convenience",
      description: "Track your service in real-time, make secure payments, and rate your experience."
    }
  ];

  return (
    <section id="how-it-works" className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="section-heading">How Does Jirani Work?</h2>
          <p className="section-subheading">Getting started with Jirani is easy. Follow these simple steps to connect with local services.</p>
        </div>
        
        <div className="flex flex-col md:flex-row items-center">
          <div className="w-full md:w-1/2 mb-10 md:mb-0 md:pr-8">
            <div className="relative">
              <div className="bg-gradient-to-br from-primary to-secondary rounded-bl-[80px] rounded-tr-[80px] p-5 shadow-xl">
                <img 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=460&q=80" 
                  alt="Jirani App Interface" 
                  className="rounded-lg border-4 border-white shadow-lg max-w-full"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-accent rounded-full opacity-20 z-0"></div>
            </div>
          </div>
          
          <div className="w-full md:w-1/2 md:pl-8">
            <div className="space-y-2">
              {steps.map((step, index) => (
                <Step
                  key={index}
                  number={index + 1}
                  title={step.title}
                  description={step.description}
                  isLast={index === steps.length - 1}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
