
import React from 'react';
import { Button } from '@/components/ui/button';

const Download: React.FC = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-primary to-secondary text-white relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="w-full md:w-1/2 mb-10 md:mb-0">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 font-poppins">
              Download Jirani Today
            </h2>
            <p className="text-lg md:text-xl text-white/80 mb-8 max-w-lg">
              Join thousands of users already enjoying the convenience of local services at their fingertips. Download the app now!
            </p>
            
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-8">
              <Button className="bg-white text-primary hover:bg-white/90 font-montserrat flex items-center justify-center gap-2 py-6 px-8">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z" /><path d="M12 7c2 0 3-1 3-1" /></svg>
                <div className="text-left">
                  <div className="text-xs">Download on the</div>
                  <div className="text-lg font-semibold">App Store</div>
                </div>
              </Button>
              <Button className="bg-white text-primary hover:bg-white/90 font-montserrat flex items-center justify-center gap-2 py-6 px-8">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="5 3 19 12 5 21 5 3" /></svg>
                <div className="text-left">
                  <div className="text-xs">GET IT ON</div>
                  <div className="text-lg font-semibold">Google Play</div>
                </div>
              </Button>
            </div>
            
            <div className="flex items-center">
              <div className="mr-6">
                <div className="text-4xl font-bold">50K+</div>
                <div className="text-white/80">Downloads</div>
              </div>
              <div className="mr-6">
                <div className="text-4xl font-bold">4.8</div>
                <div className="text-white/80">App Rating</div>
              </div>
              <div>
                <div className="text-4xl font-bold">200+</div>
                <div className="text-white/80">Service Providers</div>
              </div>
            </div>
          </div>
          
          <div className="w-full md:w-1/2 flex justify-center md:justify-end relative">
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-6 rotate-3 shadow-2xl">
                <img 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=460&q=80" 
                  alt="Jirani App Interface" 
                  className="rounded-2xl border-8 border-white/70 shadow-lg max-w-[300px]"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-accent rounded-full opacity-20"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-40 h-40 bg-white opacity-5 rounded-full"></div>
      <div className="absolute bottom-10 right-10 w-60 h-60 bg-white opacity-5 rounded-full"></div>
    </section>
  );
};

export default Download;
