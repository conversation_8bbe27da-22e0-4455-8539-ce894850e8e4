
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 25 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 215 44% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 142 100% 39%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-bold;
  }
  
  button, .cta {
    @apply font-montserrat font-semibold;
  }
}

.glass-card {
  @apply backdrop-blur-md bg-white/20 border border-white/30 shadow-lg;
}

@layer components {
  .container-section {
    @apply max-w-7xl mx-auto px-4 md:px-8 py-16 md:py-24;
  }
  
  .button-primary {
    @apply bg-primary hover:bg-primary-hover text-white font-montserrat font-semibold py-3 px-8 rounded-full transition-all duration-300 shadow-md hover:shadow-lg;
  }
  
  .button-secondary {
    @apply bg-secondary hover:bg-secondary-hover text-white font-montserrat font-semibold py-3 px-8 rounded-full transition-all duration-300 shadow-md hover:shadow-lg;
  }
  
  .button-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-white font-montserrat font-semibold py-[10px] px-8 rounded-full transition-all duration-300;
  }
  
  .section-heading {
    @apply text-3xl md:text-4xl font-bold text-secondary mb-4 font-poppins;
  }
  
  .section-subheading {
    @apply text-xl md:text-2xl font-medium text-text-muted mb-8 font-inter max-w-2xl mx-auto;
  }
}
