#!/bin/bash

# This script sets up Let's Encrypt certificates for the Jirani API

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"

echo "Ensuring VM has the correct network tags..."
gcloud compute instances add-tags $SERVER --tags=http-server,https-server --zone $ZONE --project $PROJECT

echo "Connecting to the server to set up Let's Encrypt certificates..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "bash -c '
cd /home/<USER>/jirani-backend

# Stop all containers
echo \"Stopping all containers...\"
docker-compose -f docker-compose.prod.yml down

# Update Nginx configuration
echo \"Updating Nginx configuration...\"
cat > nginx/conf/default.conf << \"EOF\"
# Default server configuration
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    server_tokens off;

    # For Let'\''s Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# API Server
server {
    listen 80;
    listen [::]:80;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # For Let'\''s Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.jirani.tufiked.live/privkey.pem;
    
    # API Proxy
    location / {
        proxy_pass http://api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
    add_header Referrer-Policy strict-origin-when-cross-origin;
}

# Storage Server (MinIO API)
server {
    listen 80;
    listen [::]:80;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # For Let'\''s Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/storage.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/storage.jirani.tufiked.live/privkey.pem;
    
    # MinIO API Proxy
    location / {
        proxy_pass http://minio:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection \"\";
    }

    # Security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
}

# Admin Server (RabbitMQ Management)
server {
    listen 80;
    listen [::]:80;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # For Let'\''s Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/admin.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.jirani.tufiked.live/privkey.pem;
    
    # RabbitMQ Management Proxy
    location / {
        proxy_pass http://rabbitmq:15672;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Basic Authentication
        auth_basic \"Restricted Access\";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}

# Console Server (MinIO Console)
server {
    listen 80;
    listen [::]:80;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # For Let'\''s Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/console.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/console.jirani.tufiked.live/privkey.pem;
    
    # MinIO Console Proxy
    location / {
        proxy_pass http://minio:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection \"\";
        
        # Basic Authentication
        auth_basic \"Restricted Access\";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}
EOF

# Create .htpasswd file for basic authentication
echo \"Creating .htpasswd file for basic authentication...\"
docker run --rm --entrypoint htpasswd nginx:alpine -Bbn admin admin123 > nginx/conf/.htpasswd

# Clean up existing certificates
echo \"Cleaning up existing certificates...\"
rm -rf nginx/certbot/conf
rm -rf nginx/certbot/www
mkdir -p nginx/certbot/conf/live
mkdir -p nginx/certbot/www

# Start Nginx for certificate validation
echo \"Starting Nginx for certificate validation...\"
docker-compose -f docker-compose.prod.yml up -d nginx
sleep 5

# Request Let'\''s Encrypt certificates
echo \"Requesting Let'\''s Encrypt certificates...\"
for domain in api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live; do
  echo \"Requesting certificate for $domain...\"
  docker-compose -f docker-compose.prod.yml run --rm --entrypoint \"\\
    certbot certonly --webroot -w /var/www/certbot \\
      --email <EMAIL> \\
      -d $domain \\
      --rsa-key-size 4096 \\
      --agree-tos \\
      --force-renewal\" certbot
done

# Start all services
echo \"Starting all services...\"
docker-compose -f docker-compose.prod.yml up -d
sleep 10

# Reload Nginx
echo \"Reloading Nginx...\"
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload

echo \"All done! Your services should now be accessible with proper Let'\''s Encrypt SSL certificates.\"
'"

echo "All done! Your services should now be accessible with proper Let's Encrypt SSL certificates."
