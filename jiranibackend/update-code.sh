#!/bin/bash

# <PERSON>rani API Code Update Script
# This script updates the code on the server without rebuilding the Docker image

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"
APP_NAME="jirani-api"

# Parse command line arguments
SKIP_BUILD=false
RESTART_API=true

print_usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  --skip-build     Skip building the application"
  echo "  --no-restart     Don't restart the API service"
  echo "  --help           Show this help message"
}

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --skip-build) SKIP_BUILD=true ;;
    --no-restart) RESTART_API=false ;;
    --help) print_usage; exit 0 ;;
    *) echo "Unknown parameter: $1"; print_usage; exit 1 ;;
  esac
  shift
done

# Step 1: Build the application (if not skipped)
if [ "$SKIP_BUILD" = false ]; then
  echo "Building the application..."
  CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $APP_NAME ./cmd/api/main.go
fi

# Step 2: Create a temporary directory for the binary
TMP_DIR=$(mktemp -d)
cp "$APP_NAME" "$TMP_DIR/"

# Step 3: Transfer the binary to the server
echo "Transferring binary to server..."
gcloud compute scp --zone $ZONE --project $PROJECT --recurse "$TMP_DIR/$APP_NAME" $SERVER:$REMOTE_DIR/bin/api

# Clean up the temporary directory
rm -rf $TMP_DIR

# Step 4: Restart the API service (if requested)
if [ "$RESTART_API" = true ]; then
  echo "Restarting API service on server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml restart api"
fi

echo "Code update completed successfully!"
