#!/bin/bash

echo "🗄️ Running BodaBoda Migration on Server..."
echo "=========================================="

cd /home/<USER>/CODES/jirani-app/jiranibackend

# Check if migration file exists
if [ ! -f "migrate_boda.sql" ]; then
    echo "❌ Migration file not found!"
    exit 1
fi

echo "📍 Found migration file, executing..."

# Run the migration using docker-compose
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U jirani_user -d jirani_db < migrate_boda.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
else
    echo "❌ Migration failed!"
    exit 1
fi

echo ""
echo "🎉 BodaBoda database migration complete!"
