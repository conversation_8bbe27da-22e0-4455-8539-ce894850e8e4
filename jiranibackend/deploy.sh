#!/bin/bash

# Jirani API Deployment Script
# This script provides a streamlined way to deploy the Jirani API

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"
DOCKER_REPO="kimathinrian"
APP_NAME="jirani-api"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")

# Parse command line arguments
SKIP_BUILD=false
SKIP_PUSH=false
SKIP_COMPOSE=false
FORCE_LOGIN=false
RESTART_NGINX=false
CLEAN_DEPLOY=true

print_usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  --skip-build     Skip building the application and Docker image"
  echo "  --skip-push      Skip pushing the Docker image to the registry"
  echo "  --skip-compose   Skip updating the docker-compose.prod.yml file"
  echo "  --force-login    Force Docker login on the server"
  echo "  --restart-nginx  Restart Nginx after deployment"
  echo "  --no-clean       Skip the clean deployment (keep existing containers/networks)"
  echo "  --help           Show this help message"
}

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --skip-build) SKIP_BUILD=true ;;
    --skip-push) SKIP_PUSH=true ;;
    --skip-compose) SKIP_COMPOSE=true ;;
    --force-login) FORCE_LOGIN=true ;;
    --restart-nginx) RESTART_NGINX=true ;;
    --no-clean) CLEAN_DEPLOY=false ;;
    --help) print_usage; exit 0 ;;
    *) echo "Unknown parameter: $1"; print_usage; exit 1 ;;
  esac
  shift
done

# Step 1: Build the application (if not skipped)
if [ "$SKIP_BUILD" = false ]; then
  echo "Building the application..."
  CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $APP_NAME ./cmd/api/main.go

  echo "Building Docker image..."
  docker build -t $DOCKER_REPO/$APP_NAME:$VERSION -t $DOCKER_REPO/$APP_NAME:latest .
fi

# Step 2: Push the Docker image (if not skipped)
if [ "$SKIP_PUSH" = false ]; then
  echo "Pushing Docker image to registry..."
  docker push $DOCKER_REPO/$APP_NAME:$VERSION
  docker push $DOCKER_REPO/$APP_NAME:latest
fi

# Step 3: Force Docker login on the server (if requested)
if [ "$FORCE_LOGIN" = true ]; then
  echo "Logging in to Docker Hub on the server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker login"
fi

# Step 4: Update docker-compose.prod.yml (if not skipped)
if [ "$SKIP_COMPOSE" = false ]; then
  echo "Updating docker-compose.prod.yml on server..."
  gcloud compute scp --zone $ZONE --project $PROJECT docker-compose.prod.yml $SERVER:$REMOTE_DIR/
fi

# Step 5: Clean deployment (if enabled)
if [ "$CLEAN_DEPLOY" = true ]; then
  echo "Performing clean deployment - stopping all containers and cleaning up..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
    cd $REMOTE_DIR
    echo 'Stopping all containers...'
    docker-compose -f docker-compose.prod.yml down

    echo 'Removing all unused containers, networks, images, and build cache...'
    docker system prune -af --volumes

    echo 'Removing any remaining networks...'
    docker network prune -f

    echo 'Pulling latest images...'
    docker-compose -f docker-compose.prod.yml pull

    echo 'Starting all services fresh...'
    docker-compose -f docker-compose.prod.yml up -d

    echo 'Running database migrations...'
    sleep 10  # Wait for services to start
    docker run --rm --network jirani-network \
      -e DB_HOST=jirani-postgres \
      -e DB_PORT=5432 \
      -e DB_USER=jirani \
      -e DB_PASSWORD=jirani123 \
      -e DB_NAME=jiranidb \
      -e DB_SSL_MODE=disable \
      -e REDIS_HOST=jirani-redis \
      -e REDIS_PORT=6379 \
      -e REDIS_PASSWORD=redis123 \
      $DOCKER_REPO/$APP_NAME:latest /app/$APP_NAME migrate || echo 'Migration completed with warnings (this is normal)'
  "
else
  # Step 5 (Alternative): Restart the API service only
  echo "Restarting API service on server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml pull api && docker-compose -f docker-compose.prod.yml up -d api"
fi

# Step 6: Restart Nginx (if requested)
if [ "$RESTART_NGINX" = true ]; then
  echo "Restarting Nginx service on server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml restart nginx"
fi

# Step 7: Verify deployment
echo "Verifying deployment..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml ps"

echo "Deployment completed successfully!"
echo "API should be available at: https://api.jirani.tufiked.live"
