# OpenCode.md

This file contains build/lint/test commands and code style guidelines for this repository.

## Commands

- Docker Build: `make docker-build`
- Docker Up: `make docker-up`
- Docker Down: `make docker-down`
- Build: `make build`
- Test: `make test`
- Run single test: `go test -run TestName ./...`
- Lint: `make lint`
- Format: `make fmt`
- Run migrations: `make migrate-up`

## Code Style Guidelines

- Imports: Use Go modules for dependency management.
- Formatting: Follow `go fmt` standards. Use `golangci-lint` for linting.
- Types: Use explicit types where necessary for clarity.
- Naming Conventions: Use camelCase for variables and functions, PascalCase for types.
- Error Handling: Handle errors explicitly, returning errors where appropriate.
- Use descriptive variable and function names.
- Follow the existing patterns in the codebase for consistency.

## Notes
- The project uses PostgreSQL for the database.
- Redis is used for caching and websocket functionality.
- RabbitMQ is used for messaging.
- MinIO is used for storage.
