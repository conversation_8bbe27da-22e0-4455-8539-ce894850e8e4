#!/bin/bash

# This script deploys the Nginx configuration to the server and restarts the services

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"

# Parse command line arguments
RESTART_ALL=false
FORCE_RENEWAL=false

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --restart-all) RESTART_ALL=true ;;
    --force-renewal) FORCE_RENEWAL=true ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

echo "Checking if domains are properly configured..."
./check-domains.sh
if [ $? -ne 0 ]; then
  echo "WARNING: Some domains are not properly configured. Continuing anyway, but certificates may fail."
  echo "Press Ctrl+C to abort, or any key to continue..."
  read -n 1 -s
fi

echo "Ensuring VM has the correct network tags..."
gcloud compute instances add-tags $SERVER --tags=http-server,https-server --zone $ZONE --project $PROJECT

if [ "$RESTART_ALL" = true ]; then
  echo "Stopping all Docker containers on the server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker stop \$(docker ps -q) || true"
else
  echo "Stopping only the Nginx container on the server..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker stop jirani-nginx || true"
fi

echo "Creating directories and setting permissions on the server..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "sudo mkdir -p $REMOTE_DIR/nginx/conf $REMOTE_DIR/nginx/certbot/conf $REMOTE_DIR/nginx/certbot/www && sudo chown -R \$(whoami):\$(whoami) $REMOTE_DIR"

echo "Transferring Nginx configuration files to the server..."
# Create a temporary directory for the files
TMP_DIR=$(mktemp -d)
cp ./nginx/conf/default.conf $TMP_DIR/
cp ./nginx/conf/ssl.conf $TMP_DIR/
if [ -f "./nginx/conf/.htpasswd" ]; then
  cp ./nginx/conf/.htpasswd $TMP_DIR/
fi

# Transfer the files to the server
gcloud compute scp --zone $ZONE --project $PROJECT --recurse $TMP_DIR/* $SERVER:$REMOTE_DIR/nginx/conf/

# Clean up the temporary directory
rm -rf $TMP_DIR

# Transfer the docker-compose.prod.yml file
gcloud compute scp --zone $ZONE --project $PROJECT ./docker-compose.prod.yml $SERVER:$REMOTE_DIR/

# Transfer the scripts
gcloud compute scp --zone $ZONE --project $PROJECT ./init-letsencrypt.sh $SERVER:$REMOTE_DIR/
gcloud compute scp --zone $ZONE --project $PROJECT ./check-domains.sh $SERVER:$REMOTE_DIR/
gcloud compute scp --zone $ZONE --project $PROJECT ./generate-self-signed-certs.sh $SERVER:$REMOTE_DIR/

echo "Making scripts executable..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "chmod +x $REMOTE_DIR/init-letsencrypt.sh $REMOTE_DIR/check-domains.sh $REMOTE_DIR/generate-self-signed-certs.sh"

echo "Installing required packages..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "sudo apt-get update && sudo apt-get install -y apache2-utils"

if [ "$FORCE_RENEWAL" = true ]; then
  echo "Running init-letsencrypt.sh with force renewal..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && ./init-letsencrypt.sh --force"
else
  echo "Restarting Nginx container..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml up -d nginx"

  echo "Waiting for Nginx to start..."
  sleep 5

  echo "Checking Nginx status..."
  gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker ps | grep nginx"
fi

echo "Deployment completed successfully!"
