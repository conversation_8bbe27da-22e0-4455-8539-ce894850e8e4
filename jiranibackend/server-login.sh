#!/bin/bash

# This script logs in to Docker Hub on the server

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"

# Ask for Docker Hub credentials
read -p "Enter Docker Hub username: " DOCKER_USERNAME
read -sp "Enter Docker Hub password: " DOCKER_PASSWORD
echo

# Log in to Docker Hub on the server
echo "Logging in to Docker Hub on the server..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "echo '$DOCKER_PASSWORD' | docker login --username $DOCKER_USERNAME --password-stdin"

echo "Docker Hub login completed on the server."
