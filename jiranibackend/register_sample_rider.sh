#!/bin/bash

# <PERSON>ript to register a sample rider for testing the BodaBoda feature
# This script will:
# 1. Register a new user
# 2. Register the user as a driver
# 3. Add a vehicle for the driver
# 4. Set the driver as available

API_BASE_URL="https://api.jirani.tufiked.live/api/v1"
# API_BASE_URL="http://localhost:8080/api/v1"  # Use this for local testing

echo "🚀 Registering Sample BodaBoda Rider..."
echo "========================================"

# Sample rider data
RIDER_EMAIL="<EMAIL>"
RIDER_PASSWORD="SecurePass123!"
RIDER_FULL_NAME="<PERSON>"
RIDER_PHONE="+************"
RIDER_LICENSE="DL9********"
RIDER_ID_NUMBER="********"

# Vehicle data
VEHICLE_TYPE="motorcycle"
VEHICLE_MAKE="Honda"
VEHICLE_MODEL="CB150R"
VEHICLE_YEAR=2022
VEHICLE_COLOR="Red"
VEHICLE_PLATE="KCB456D"

echo "📝 Step 1: Registering user account..."
REGISTER_RESPONSE=$(curl -s -X POST "$API_BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$RIDER_EMAIL\",
    \"password\": \"$RIDER_PASSWORD\",
    \"full_name\": \"$RIDER_FULL_NAME\",
    \"phone_number\": \"$RIDER_PHONE\"
  }")

echo "Register Response: $REGISTER_RESPONSE"

# Check if registration was successful
if echo "$REGISTER_RESPONSE" | grep -q "error"; then
  echo "❌ Registration failed. Trying to login instead..."
else
  echo "✅ User registered successfully!"
fi

echo ""
echo "🔐 Step 2: Logging in to get access token..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$RIDER_EMAIL\",
    \"password\": \"$RIDER_PASSWORD\"
  }")

echo "Login Response: $LOGIN_RESPONSE"

# Extract access token
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
  echo "❌ Failed to get access token. Login failed."
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Login successful! Access token obtained."
echo ""

echo "🏍️ Step 3: Registering user as a driver..."
DRIVER_RESPONSE=$(curl -s -X POST "$API_BASE_URL/driver/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d "{
    \"license_number\": \"$RIDER_LICENSE\",
    \"id_number\": \"$RIDER_ID_NUMBER\"
  }")

echo "Driver Registration Response: $DRIVER_RESPONSE"

if echo "$DRIVER_RESPONSE" | grep -q "error"; then
  echo "❌ Driver registration failed."
  echo "Response: $DRIVER_RESPONSE"
  # Continue anyway in case driver already exists
else
  echo "✅ Driver registered successfully!"
fi

echo ""
echo "🚗 Step 4: Adding vehicle for the driver..."
VEHICLE_RESPONSE=$(curl -s -X POST "$API_BASE_URL/driver/vehicles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d "{
    \"type\": \"$VEHICLE_TYPE\",
    \"make\": \"$VEHICLE_MAKE\",
    \"model\": \"$VEHICLE_MODEL\",
    \"year\": $VEHICLE_YEAR,
    \"color\": \"$VEHICLE_COLOR\",
    \"license_plate\": \"$VEHICLE_PLATE\"
  }")

echo "Vehicle Registration Response: $VEHICLE_RESPONSE"

if echo "$VEHICLE_RESPONSE" | grep -q "error"; then
  echo "❌ Vehicle registration failed."
  echo "Response: $VEHICLE_RESPONSE"
else
  echo "✅ Vehicle registered successfully!"
fi

echo ""
echo "📍 Step 5: Setting driver location (Nairobi CBD)..."
LOCATION_RESPONSE=$(curl -s -X PUT "$API_BASE_URL/driver/location" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d "{
    \"latitude\": -1.2864,
    \"longitude\": 36.8172
  }")

echo "Location Update Response: $LOCATION_RESPONSE"

if echo "$LOCATION_RESPONSE" | grep -q "error"; then
  echo "❌ Location update failed."
  echo "Response: $LOCATION_RESPONSE"
else
  echo "✅ Location updated successfully!"
fi

echo ""
echo "🟢 Step 6: Setting driver as available..."
STATUS_RESPONSE=$(curl -s -X PUT "$API_BASE_URL/driver/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d "{
    \"is_available\": true
  }")

echo "Status Update Response: $STATUS_RESPONSE"

if echo "$STATUS_RESPONSE" | grep -q "error"; then
  echo "❌ Status update failed (driver might need verification)."
  echo "Response: $STATUS_RESPONSE"
else
  echo "✅ Driver status updated successfully!"
fi

echo ""
echo "🔍 Step 7: Getting driver profile..."
PROFILE_RESPONSE=$(curl -s -X GET "$API_BASE_URL/driver/profile" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "Driver Profile: $PROFILE_RESPONSE"

echo ""
echo "📋 Summary:"
echo "==========="
echo "Email: $RIDER_EMAIL"
echo "Password: $RIDER_PASSWORD"
echo "Full Name: $RIDER_FULL_NAME"
echo "Phone: $RIDER_PHONE"
echo "License: $RIDER_LICENSE"
echo "ID Number: $RIDER_ID_NUMBER"
echo "Vehicle: $VEHICLE_YEAR $VEHICLE_MAKE $VEHICLE_MODEL ($VEHICLE_COLOR)"
echo "License Plate: $VEHICLE_PLATE"
echo "Location: Nairobi CBD (-1.2864, 36.8172)"
echo ""
echo "Access Token: $ACCESS_TOKEN"
echo ""
echo "🎉 Sample rider registration complete!"
echo "You can now use this rider for testing the BodaBoda feature."
