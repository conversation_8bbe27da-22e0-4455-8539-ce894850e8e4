{"time":"2025-05-20T20:56:07.199921366Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"","caller":"main","file":"/home/<USER>/CODES/jirani-app/jiranibackend/cmd/api/main.go","line":26,"data":{"environment":"","version":"1.0.0"}}
{"time":"2025-05-20T20:56:07.201363096Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"","caller":"main","file":"/home/<USER>/CODES/jirani-app/jiranibackend/cmd/api/main.go","line":38}
{"time":"2025-05-20T20:56:07.201552178Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"","caller":"main","file":"/home/<USER>/CODES/jirani-app/jiranibackend/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-20T20:56:07.203467048Z","level":"FATAL","message":"Failed to connect to database","service":"jirani-api","environment":"","caller":"main","file":"/home/<USER>/CODES/jirani-app/jiranibackend/cmd/api/main.go","line":51,"data":{"error":"failed to connect to database: failed to connect to `host=postgres user=jirani database=jiranidb`: hostname resolving error (lookup postgres on **********:53: server misbehaving)"}}
