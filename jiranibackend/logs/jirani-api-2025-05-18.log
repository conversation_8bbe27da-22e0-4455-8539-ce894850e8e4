{"time":"2025-05-18T20:27:24.065315554Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":26,"data":{"environment":"production","version":"1.0.0"}}
{"time":"2025-05-18T20:27:24.065815826Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":38}
{"time":"2025-05-18T20:27:24.067253576Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-18T20:27:24.100520756Z","level":"FATAL","message":"Failed to connect to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":51,"data":{"error":"failed to connect to database: failed to connect to `host=postgres user=jirani database=jiranidb`: dial error (dial tcp **********:5432: connect: connection refused)"}}
{"time":"2025-05-18T20:27:25.93309236Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":26,"data":{"environment":"production","version":"1.0.0"}}
{"time":"2025-05-18T20:27:25.938391463Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":38}
{"time":"2025-05-18T20:27:25.939183977Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-18T20:27:25.977435114Z","level":"FATAL","message":"Failed to connect to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":51,"data":{"error":"failed to connect to database: failed to connect to `host=postgres user=jirani database=jiranidb`: dial error (dial tcp **********:5432: connect: connection refused)"}}
{"time":"2025-05-18T20:27:27.279984448Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":26,"data":{"environment":"production","version":"1.0.0"}}
{"time":"2025-05-18T20:27:27.280610672Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":38}
{"time":"2025-05-18T20:27:27.282565543Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-18T20:27:27.298142555Z","level":"FATAL","message":"Failed to connect to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":51,"data":{"error":"failed to connect to database: failed to connect to `host=postgres user=jirani database=jiranidb`: dial error (dial tcp **********:5432: connect: connection refused)"}}
{"time":"2025-05-18T20:27:28.686939553Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":26,"data":{"environment":"production","version":"1.0.0"}}
{"time":"2025-05-18T20:27:28.688584538Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":38}
{"time":"2025-05-18T20:27:28.688924632Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-18T20:27:28.705468479Z","level":"FATAL","message":"Failed to connect to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":51,"data":{"error":"failed to connect to database: failed to connect to `host=postgres user=jirani database=jiranidb`: dial error (dial tcp **********:5432: connect: connection refused)"}}
{"time":"2025-05-18T20:27:31.139583144Z","level":"INFO","message":"Starting Jirani API","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":26,"data":{"environment":"production","version":"1.0.0"}}
{"time":"2025-05-18T20:27:31.140177957Z","level":"INFO","message":"Initializing metrics","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":38}
{"time":"2025-05-18T20:27:31.148488242Z","level":"INFO","message":"Connecting to database","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":45,"data":{"host":"postgres","name":"jiranidb","port":"5432"}}
{"time":"2025-05-18T20:27:31.204237295Z","level":"INFO","message":"Database connection established","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":53}
{"time":"2025-05-18T20:27:31.204309087Z","level":"INFO","message":"Creating Fiber app","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":56}
{"time":"2025-05-18T20:27:31.20434207Z","level":"INFO","message":"Setting up middleware","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":81}
{"time":"2025-05-18T20:27:31.204436264Z","level":"INFO","message":"Setting up metrics middleware","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":104}
{"time":"2025-05-18T20:27:31.204551333Z","level":"INFO","message":"Setting up metrics endpoint","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":109}
{"time":"2025-05-18T20:27:31.204641197Z","level":"INFO","message":"Setting up routes","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":120}
{"time":"2025-05-18T20:27:31.204851463Z","level":"INFO","message":"Server starting","service":"jirani-api","environment":"production","caller":"main","file":"/app/cmd/api/main.go","line":129,"data":{"port":"8080"}}
