package main

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
)

func main() {
	// Check if a file path was provided
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run test_upload.go <image_file_path> [auth_token]")
		fmt.Println("If auth_token is not provided, the script will try to login first")
		os.Exit(1)
	}

	// Get the file path from command line arguments
	filePath := os.Args[1]

	// Get the auth token if provided
	var authToken string
	if len(os.Args) >= 3 {
		authToken = os.Args[2]
	} else {
		// If no auth token is provided, try to login
		authToken = getAuthToken()
	}

	// API endpoint for file upload
	apiURL := "https://api.jirani.tufiked.live/api/v1/storage/upload"

	// Create a buffer to store the request body
	var requestBody bytes.Buffer

	// Create a multipart writer
	multipartWriter := multipart.NewWriter(&requestBody)

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()

	// Create a form file field
	fileWriter, err := multipartWriter.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		fmt.Printf("Error creating form file: %v\n", err)
		os.Exit(1)
	}

	// Copy the file content to the form field
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		fmt.Printf("Error copying file content: %v\n", err)
		os.Exit(1)
	}

	// Close the multipart writer
	multipartWriter.Close()

	// Create a new HTTP request
	req, err := http.NewRequest("POST", apiURL, &requestBody)
	if err != nil {
		fmt.Printf("Error creating request: %v\n", err)
		os.Exit(1)
	}

	// Set the content type header
	req.Header.Set("Content-Type", multipartWriter.FormDataContentType())

	// Create an HTTP client that skips TLS verification (for testing only)
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// Send the request
	fmt.Println("Uploading file to", apiURL)
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error sending request: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	// Read the response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		os.Exit(1)
	}

	// Print the response
	fmt.Printf("Status: %s\n", resp.Status)
	fmt.Printf("Response: %s\n", string(respBody))
}
