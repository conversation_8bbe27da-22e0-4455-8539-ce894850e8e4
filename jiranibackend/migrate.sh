#!/bin/bash

# Build the migration binary
echo "Building migration binary..."
go build -o migrate ./cmd/migrate/main.go

# Build Docker image for migration
echo "Building migration Docker image..."
cat > Dockerfile.migrate << 'EOF'
FROM alpine:3.18

WORKDIR /app

# Install ca-certificates for HTTPS requests
RUN apk add --no-cache ca-certificates tzdata && \
    update-ca-certificates

# Copy the migration binary
COPY migrate /app/migrate

# Make it executable
RUN chmod +x /app/migrate

# Run the migration
CMD ["/app/migrate"]
EOF

# Build the migration image
docker build -f Dockerfile.migrate -t jirani-migrate .

# Push to server and run migration
echo "Running migration on server..."
ssh -o StrictHostKeyChecking=no root@************ << 'ENDSSH'
cd /root/jirani

# Pull the latest API image to get the latest models
docker pull kimathinrian/jirani-api:latest

# Run migration using the API container with a custom command
docker run --rm \
  --network jirani_default \
  -e DB_HOST=jirani-postgres \
  -e DB_PORT=5432 \
  -e DB_USER=jirani \
  -e DB_PASSWORD=jirani123 \
  -e DB_NAME=jiranidb \
  -e DB_SSL_MODE=disable \
  kimathinrian/jirani-api:latest \
  sh -c "echo 'Running migrations...' && /app/jirani-api migrate"

echo "Migration completed!"
ENDSSH

# Clean up
rm -f Dockerfile.migrate

echo "Migration deployment completed!"
