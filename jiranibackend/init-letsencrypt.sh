#!/bin/bash

# This script will obtain SSL certificates from Let's Encrypt for the domains
# specified in the domains array. It will also create a self-signed certificate
# for use during the initial setup.

if ! [ -x "$(command -v docker-compose)" ]; then
  echo 'Error: docker-compose is not installed.' >&2
  exit 1
fi

# Define domains and settings
domains=(api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live)
rsa_key_size=4096
data_path="./nginx/certbot"
email="<EMAIL>" # Adding a valid address is strongly recommended
staging=0 # Set to 1 if you're testing your setup to avoid hitting request limits

# Check if we should use staging environment for testing
if [ "$1" = "--staging" ]; then
  staging=1
  echo "Using Let's Encrypt staging environment"
fi

# Check if we should force renewal
force_renewal=0
if [ "$1" = "--force" ] || [ "$2" = "--force" ]; then
  force_renewal=1
  echo "Forcing certificate renewal"
fi

if [ -d "$data_path" ]; then
  read -p "Existing data found for $domains. Continue and replace existing certificate? (y/N) " decision
  if [ "$decision" != "Y" ] && [ "$decision" != "y" ]; then
    exit
  fi
fi

if [ ! -e "$data_path/conf/options-ssl-nginx.conf" ] || [ ! -e "$data_path/conf/ssl-dhparams.pem" ]; then
  echo "### Downloading recommended TLS parameters ..."
  mkdir -p "$data_path/conf"
  curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot-nginx/certbot_nginx/_internal/tls_configs/options-ssl-nginx.conf > "$data_path/conf/options-ssl-nginx.conf"
  curl -s https://raw.githubusercontent.com/certbot/certbot/master/certbot/certbot/ssl-dhparams.pem > "$data_path/conf/ssl-dhparams.pem"
  echo
fi

# Create .htpasswd file for basic authentication
echo "### Creating .htpasswd file for basic authentication ..."
mkdir -p "./nginx/conf"
htpasswd -bc "./nginx/conf/.htpasswd" "admin" "admin123"
echo

# Clean up existing certificates and create fresh directories
echo "### Cleaning up existing certificates ..."
rm -rf "$data_path/conf"
rm -rf "$data_path/www"

echo "### Creating fresh directories for Let's Encrypt certificates ..."
mkdir -p "$data_path/conf/live"
mkdir -p "$data_path/www"

echo "### Generating self-signed certificates ..."
./generate-self-signed-certs.sh
echo

echo "### Stopping any running containers ..."
docker-compose -f docker-compose.prod.yml down
echo

echo "### Starting services without Nginx ..."
docker-compose -f docker-compose.prod.yml up -d postgres redis rabbitmq minio api
echo

# Wait for services to start
echo "### Waiting for services to start ..."
sleep 10

for domain in "${domains[@]}"; do
  echo "### Requesting Let's Encrypt certificate for $domain ..."

  # Join $domains to -d args
  domain_args="-d $domain"

  # Select appropriate email arg
  case "$email" in
    "") email_arg="--register-unsafely-without-email" ;;
    *) email_arg="--email $email" ;;
  esac

  # Enable staging mode if needed
  if [ $staging != "0" ]; then staging_arg="--staging"; fi

  # Add force renewal if requested
  force_arg=""
  if [ $force_renewal != "0" ]; then force_arg="--force-renewal"; fi

  # Try multiple times with increasing timeouts
  for attempt in 1 2 3; do
    echo "Attempt $attempt for $domain..."

    # Increase timeout for each attempt
    timeout=$((30 * attempt))

    docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
      certbot certonly --standalone \
        $staging_arg \
        $email_arg \
        $domain_args \
        --rsa-key-size $rsa_key_size \
        --agree-tos \
        $force_arg \
        --preferred-challenges http \
        --http-01-port 80 \
        --keep-until-expiring" certbot

    # Check if certificate was obtained successfully
    if docker-compose -f docker-compose.prod.yml run --rm --entrypoint "\
      certbot certificates --cert-name $domain" certbot | grep -q "VALID:"; then
      echo "Certificate for $domain obtained successfully!"
      break
    else
      echo "Failed to obtain certificate for $domain on attempt $attempt"
      if [ $attempt -lt 3 ]; then
        echo "Waiting $timeout seconds before next attempt..."
        sleep $timeout
      else
        echo "All attempts failed for $domain. Using self-signed certificate."
      fi
    fi
  done

  echo
done

echo "### Starting Nginx with SSL certificates ..."
docker-compose -f docker-compose.prod.yml up -d nginx

echo "### Waiting for Nginx to start ..."
sleep 5

echo "### Reloading Nginx ..."
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload

echo "### All done! Your services should now be accessible with proper SSL certificates."
