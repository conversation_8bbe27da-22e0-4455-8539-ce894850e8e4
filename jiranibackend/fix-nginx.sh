#!/bin/bash

# This script fixes the Nginx configuration on the server

# Set variables
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"

echo "Creating SSL configuration file on the server..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "sudo bash -c 'mkdir -p $REMOTE_DIR/nginx/conf/ssl && cat > $REMOTE_DIR/nginx/conf/ssl.conf << EOF
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
add_header Strict-Transport-Security \"max-age=63072000; includeSubDomains; preload\" always;
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection \"1; mode=block\";
add_header Referrer-Policy strict-origin-when-cross-origin;
EOF'"

echo "Creating self-signed certificates on the server..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && bash -c '
mkdir -p ./nginx/conf/ssl
for domain in api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live; do
  echo \"Generating self-signed certificate for \$domain...\"
  openssl genrsa -out \"./nginx/conf/ssl/\$domain.key\" 2048
  openssl req -new -key \"./nginx/conf/ssl/\$domain.key\" -out \"./nginx/conf/ssl/\$domain.csr\" -subj \"/CN=\$domain\"
  openssl x509 -req -days 365 -in \"./nginx/conf/ssl/\$domain.csr\" -signkey \"./nginx/conf/ssl/\$domain.key\" -out \"./nginx/conf/ssl/\$domain.crt\"
  rm \"./nginx/conf/ssl/\$domain.csr\"
  echo \"Self-signed certificate for \$domain generated successfully!\"
done'"

echo "Updating docker-compose.prod.yml to mount the SSL directory..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && sed -i 's|      - ./nginx/conf:/etc/nginx/conf.d|      - ./nginx/conf:/etc/nginx/conf.d\n      - ./nginx/conf/ssl:/etc/nginx/conf.d/ssl|g' docker-compose.prod.yml"

echo "Restarting the Nginx container..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "cd $REMOTE_DIR && docker-compose -f docker-compose.prod.yml restart nginx"

echo "Checking if the Nginx container is running..."
gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "docker ps | grep nginx"

echo "All done! Your services should now be accessible with self-signed SSL certificates."
