package main

import (
	"log"
	"os"

	"github.com/jirani/backend/internal/config"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Initialize database connection
	if err := database.Connect(cfg); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	log.Println("Running database migrations...")

	// Run migrations
	err = database.DB.AutoMigrate(
		// User models
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.Location{},

		// Restaurant models
		&models.Restaurant{},
		&models.MenuItem{},

		// Order models
		&models.Order{},
		&models.OrderItem{},

		// Boda Boda models
		&models.Driver{},
		&models.Vehicle{},
		&models.Ride{},

		// Payment models
		&models.Payment{},

		// Rating models
		&models.RideRating{},
		&models.DriverRating{},
		&models.RiderRating{},

		// Notification models
		&models.Notification{},
		&models.DeviceToken{},
	)

	if err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	log.Println("Migrations completed successfully!")
	os.Exit(0)
}
