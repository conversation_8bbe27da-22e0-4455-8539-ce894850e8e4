package main

import (
	"log"
	"os"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/jirani/backend/internal/config"
	"github.com/jirani/backend/internal/database"
	customLogger "github.com/jirani/backend/internal/logger"
	"github.com/jirani/backend/internal/metrics"
	customMiddleware "github.com/jirani/backend/internal/middleware"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/routes"
	"github.com/jirani/backend/internal/websocket"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/valyala/fasthttp/fasthttpadaptor"
)

func main() {
	// Check if we're running migrations
	if len(os.Args) > 1 && os.Args[1] == "migrate" {
		runMigrations()
		return
	}

	// Initialize structured logger
	appLogger := customLogger.New(customLogger.Info, "jirani-api", os.Getenv("ENVIRONMENT"))

	// Log startup
	appLogger.Info("Starting Jirani API", map[string]interface{}{
		"version":     "1.0.0",
		"environment": os.Getenv("ENVIRONMENT"),
	})

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		appLogger.Fatal("Failed to load configuration", err, nil)
	}

	// Initialize metrics
	appLogger.Info("Initializing metrics", nil)
	metricsRegistry := metrics.NewRegistry("jirani")
	metricsCollector := metrics.NewCollector(metricsRegistry, 15*time.Second)
	metricsCollector.Start()
	defer metricsCollector.Stop()

	// Initialize database
	appLogger.Info("Connecting to database", map[string]interface{}{
		"host": cfg.DBHost,
		"port": cfg.DBPort,
		"name": cfg.DBName,
	})
	if err := database.Connect(cfg); err != nil {
		appLogger.Fatal("Failed to connect to database", err, nil)
	}
	appLogger.Info("Database connection established", nil)

	// Initialize WebSocket hub
	appLogger.Info("Initializing WebSocket hub", nil)
	websocket.InitializeHub()

	// Create Fiber app
	appLogger.Info("Creating Fiber app", nil)
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}

			// Log error
			appLogger.Error("Request error", err, map[string]interface{}{
				"status":     code,
				"method":     c.Method(),
				"path":       c.Path(),
				"ip":         c.IP(),
				"user_agent": c.Get("User-Agent"),
			})

			return c.Status(code).JSON(fiber.Map{
				"error": err.Error(),
			})
		},
		BodyLimit: 10 * 1024 * 1024, // 10MB limit for file uploads
	})

	// Middleware
	appLogger.Info("Setting up middleware", nil)
	app.Use(recover.New(recover.Config{
		EnableStackTrace: true,
		StackTraceHandler: func(c *fiber.Ctx, e interface{}) {
			appLogger.Error("Panic recovered", nil, map[string]interface{}{
				"panic":  e,
				"path":   c.Path(),
				"method": c.Method(),
			})
		},
	}))
	app.Use(logger.New(logger.Config{
		Format:     "${time} | ${status} | ${latency} | ${ip} | ${method} | ${path} | ${error}\n",
		TimeFormat: "2006-01-02 15:04:05",
		TimeZone:   "Local",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, POST, PUT, DELETE, OPTIONS",
	}))

	// Add metrics middleware
	appLogger.Info("Setting up metrics middleware", nil)
	metricsMiddleware := customMiddleware.NewMetrics("jirani")
	app.Use(customMiddleware.PrometheusMiddleware(metricsMiddleware))

	// Expose Prometheus metrics endpoint
	appLogger.Info("Setting up metrics endpoint", nil)
	promHandler := fasthttpadaptor.NewFastHTTPHandler(promhttp.Handler())
	app.Get("/metrics", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).Type("text/plain").SendString("Metrics endpoint")
	})
	app.All("/metrics", func(c *fiber.Ctx) error {
		promHandler(c.Context())
		return nil
	})

	// Setup routes
	appLogger.Info("Setting up routes", nil)
	routes.SetupRoutes(app)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	appLogger.Info("Server starting", map[string]interface{}{
		"port": port,
	})
	if err := app.Listen(":" + port); err != nil {
		appLogger.Fatal("Failed to start server", err, nil)
	}
}

func runMigrations() {
	log.Println("Running database migrations...")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Initialize database connection
	if err := database.Connect(cfg); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	log.Println("Connected to database and Redis successfully")

	// Seed database with test data if in development
	if cfg.Environment == "development" {
		log.Println("Seeding database with test data...")
		if err := database.SeedData(); err != nil {
			log.Printf("Warning: Failed to seed data: %v", err)
		}
	}

	// Always run AutoMigrate to ensure all models are up to date
	// GORM's AutoMigrate is safe to run multiple times - it only adds missing tables/columns
	log.Println("Running AutoMigrate for all models...")

	// Migrate models one by one to handle errors gracefully
	modelsToMigrate := []interface{}{
		// User models
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.Location{},

		// Restaurant models
		&models.Restaurant{},
		&models.MenuItem{},

		// Order models
		&models.Order{},
		&models.OrderItem{},

		// Boda Boda models
		&models.Driver{},
		&models.Vehicle{},
		&models.Ride{},

		// Payment models
		&models.Payment{},

		// Rating models
		&models.RideRating{},
		&models.DriverRating{},
		&models.RiderRating{},

		// Notification models
		&models.Notification{},
		&models.DeviceToken{},
	}

	// Migrate each model individually to handle constraint errors
	for _, model := range modelsToMigrate {
		if err := database.DB.AutoMigrate(model); err != nil {
			// Log the error but continue with other models
			log.Printf("Warning: Failed to migrate model %T: %v", model, err)
		} else {
			log.Printf("Successfully migrated model %T", model)
		}
	}

	log.Println("Migrations completed successfully!")
}
