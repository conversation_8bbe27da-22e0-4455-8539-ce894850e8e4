# Makefile for <PERSON>rani Backend (Local Development Only)

# Variables
APP_NAME := jirani-api
MAIN_PATH := ./cmd/api/main.go
BUILD_DIR := ./build
GO := go
GOTEST := $(GO) test
GOVET := $(GO) vet
GOLINT := golint
GOFMT := gofmt
GOMOD := $(GO) mod
GOGET := $(GO) get
GOBUILD := $(GO) build
GORUN := $(GO) run
DOCKER_REPO := kimathinrian
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")

# Server variables
SERVER := instance-20250518-210238
ZONE := us-central1-c
PROJECT := gen-lang-client-0728840371
REMOTE_DIR := /home/<USER>/jirani-backend

# Check if we're running on the development machine
# This Makefile is intended for local development only
IS_LOCAL := $(shell if [ -f /etc/hostname ] && grep -q "$(shell hostname)" /etc/hostname; then echo "true"; else echo "false"; fi)

# Default target
.PHONY: all
all: check-local clean build

# Check if we're running locally
.PHONY: check-local
check-local:
	@if [ "$(IS_LOCAL)" != "true" ]; then \
		echo "ERROR: This Makefile is intended for local development only."; \
		echo "Do not run these commands on the production server."; \
		exit 1; \
	fi

# Build the application
.PHONY: build
build: check-local
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(APP_NAME) $(MAIN_PATH)
	@echo "Build complete!"

# Build the application for multiple architectures
.PHONY: build-multi
build-multi: check-local
	@echo "Building $(APP_NAME) for multiple architectures..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(APP_NAME) $(MAIN_PATH)
	@echo "Build complete!"

# Run the application
.PHONY: run
run: check-local
	@echo "Running $(APP_NAME)..."
	$(GORUN) $(MAIN_PATH)

# Clean build artifacts
.PHONY: clean
clean: check-local
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@echo "Clean complete!"

# Test the application
.PHONY: test
test: check-local
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Test with coverage
.PHONY: test-coverage
test-coverage: check-local
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html

# Format code
.PHONY: fmt
fmt: check-local
	@echo "Formatting code..."
	$(GOFMT) -s -w .

# Vet code
.PHONY: vet
vet: check-local
	@echo "Vetting code..."
	$(GOVET) ./...

# Lint code (requires golangci-lint)
.PHONY: lint
lint: check-local
	@echo "Linting code..."
	golangci-lint run

# Tidy dependencies
.PHONY: tidy
tidy: check-local
	@echo "Tidying dependencies..."
	$(GOMOD) tidy

# Download dependencies
.PHONY: deps
deps: check-local
	@echo "Downloading dependencies..."
	$(GOMOD) download

# Build Docker image
.PHONY: docker-build
docker-build: check-local build
	@echo "Building Docker image..."
	docker build -t $(DOCKER_REPO)/$(APP_NAME):$(VERSION) .
	docker tag $(DOCKER_REPO)/$(APP_NAME):$(VERSION) $(DOCKER_REPO)/$(APP_NAME):latest

# Push Docker image
.PHONY: docker-push
docker-push: check-local docker-build
	@echo "Pushing Docker image..."
	docker push $(DOCKER_REPO)/$(APP_NAME):$(VERSION)
	docker push $(DOCKER_REPO)/$(APP_NAME):latest

# BodaBoda Development - Fast deployment
.PHONY: fast-deploy
fast-deploy: check-local
	@echo "🚀 Fast BodaBoda deployment..."
	./fast_deploy.sh deploy

# BodaBoda Development - Interactive deployment
.PHONY: fast-deploy-interactive
fast-deploy-interactive: check-local
	@echo "🚀 Interactive BodaBoda deployment..."
	./fast_deploy.sh

# BodaBoda Development - Full deployment with validation
.PHONY: deploy
deploy: check-local
	@echo "🚀 Full BodaBoda deployment with Context Engineering validation..."
	./optimized_deployment.sh

# BodaBoda Development - Test endpoints
.PHONY: test-endpoints
test-endpoints: check-local
	@echo "🧪 Testing BodaBoda endpoints..."
	./fast_deploy.sh test

# BodaBoda Development - Show logs
.PHONY: logs
logs: check-local
	@echo "📋 Showing API logs..."
	./fast_deploy.sh logs

# BodaBoda Development - Restart API
.PHONY: restart-api
restart-api: check-local
	@echo "🔄 Restarting API service..."
	./fast_deploy.sh restart

# BodaBoda Development - Check status
.PHONY: status
status: check-local
	@echo "📊 Checking service status..."
	./fast_deploy.sh status

# Help
.PHONY: help
help:
	@echo "🚀 BodaBoda Development Commands (Context Engineering Optimized)"
	@echo "================================================================"
	@echo ""
	@echo "📦 Build & Test:"
	@echo "  build              - Build the application"
	@echo "  run                - Run the application locally"
	@echo "  test               - Run tests"
	@echo "  test-coverage      - Run tests with coverage"
	@echo "  clean              - Clean build artifacts"
	@echo "  fmt                - Format code"
	@echo "  vet                - Vet code"
	@echo "  lint               - Lint code"
	@echo "  tidy               - Tidy dependencies"
	@echo "  deps               - Download dependencies"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  docker-build       - Build Docker image"
	@echo "  docker-push        - Push Docker image to registry"
	@echo ""
	@echo "🚀 BodaBoda Development (Remote Deployment):"
	@echo "  fast-deploy        - Quick deployment (~2 minutes)"
	@echo "  fast-deploy-interactive - Interactive deployment menu"
	@echo "  deploy             - Full Context Engineering compliant deployment"
	@echo "  test-endpoints     - Test BodaBoda endpoints"
	@echo "  logs               - Show API logs"
	@echo "  restart-api        - Restart API service only"
	@echo "  status             - Check service status"
	@echo ""
	@echo "💡 Quick Start for BodaBoda Development:"
	@echo "  1. make fast-deploy        # Quick deployment"
	@echo "  2. make test-endpoints     # Test BodaBoda functionality"
	@echo "  3. make logs               # Check for any issues"
	@echo ""
	@echo "📚 Context Engineering Documentation:"
	@echo "  See: ../bodaboda-context-engineering/documentation/remote_development_workflow.md"
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage: check-local
	@echo "Running tests with coverage..."
	$(GOTEST) -cover -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html

# Lint the code
.PHONY: lint
lint: check-local
	@echo "Linting code..."
	$(GOVET) ./...
	@if command -v $(GOLINT) > /dev/null; then \
		$(GOLINT) ./...; \
	else \
		echo "golint not installed. Run: go install golang.org/x/lint/golint@latest"; \
	fi

# Format the code
.PHONY: fmt
fmt: check-local
	@echo "Formatting code..."
	$(GOFMT) -w .

# Update dependencies
.PHONY: deps
deps: check-local
	@echo "Updating dependencies..."
	$(GOMOD) tidy
	$(GOGET) -u ./...

# Docker commands
.PHONY: docker-build
docker-build: check-local
	@echo "Building Docker image locally..."
	docker build -t $(DOCKER_REPO)/$(APP_NAME):$(VERSION) -t $(DOCKER_REPO)/$(APP_NAME):latest .

.PHONY: docker-push
docker-push: check-local
	@echo "Pushing Docker image to registry..."
	docker push $(DOCKER_REPO)/$(APP_NAME):$(VERSION)
	docker push $(DOCKER_REPO)/$(APP_NAME):latest

.PHONY: docker-login-server
docker-login-server: check-local
	@echo "Logging in to Docker Hub on the server..."
	gcloud compute ssh --zone $(ZONE) $(SERVER) --project $(PROJECT) -- "docker login"

.PHONY: update-compose
update-compose: check-local
	@echo "Updating docker-compose.prod.yml on server..."
	gcloud compute scp --zone $(ZONE) --project $(PROJECT) docker-compose.prod.yml $(SERVER):$(REMOTE_DIR)/

.PHONY: restart-api
restart-api: check-local
	@echo "Restarting API service on server..."
	gcloud compute ssh --zone $(ZONE) $(SERVER) --project $(PROJECT) -- "cd $(REMOTE_DIR) && docker-compose -f docker-compose.prod.yml pull api && docker-compose -f docker-compose.prod.yml up -d api"

# One-step deploy command
.PHONY: deploy-api
deploy-api: check-local build docker-build docker-push update-compose restart-api
	@echo "API deployment completed successfully!"

.PHONY: docker-up
docker-up: check-local
	@echo "Starting Docker containers locally..."
	docker-compose up -d

.PHONY: docker-down
docker-down: check-local
	@echo "Stopping Docker containers locally..."
	docker-compose down

.PHONY: docker-logs
docker-logs: check-local
	@echo "Showing Docker logs..."
	docker-compose logs -f

.PHONY: docker-rebuild-api
docker-rebuild-api: check-local
	@echo "Rebuilding and restarting API container..."
	./rebuild-api.sh

# Local development environment (without SSL)
.PHONY: local-up
local-up: check-local
	@echo "Starting local development environment (without SSL)..."
	./start-local.sh

.PHONY: local-down
local-down: check-local
	@echo "Stopping local development environment..."
	./stop-local.sh

.PHONY: local-logs
local-logs: check-local
	@echo "Showing local development logs..."
	docker-compose -f docker-compose.local.yml logs -f

# Database migrations (local development only)
.PHONY: migrate-up
migrate-up: check-local
	@echo "Running database migrations locally..."
	@if [ -d "./migrations" ]; then \
		echo "Using migrate command..."; \
		migrate -path ./migrations -database "postgres://jirani:jirani123@localhost:5432/jiranidb?sslmode=disable" up; \
	else \
		echo "Migrations directory not found"; \
	fi

.PHONY: migrate-down
migrate-down: check-local
	@echo "Rolling back database migrations locally..."
	@if [ -d "./migrations" ]; then \
		echo "Using migrate command..."; \
		migrate -path ./migrations -database "postgres://jirani:jirani123@localhost:5432/jiranidb?sslmode=disable" down 1; \
	else \
		echo "Migrations directory not found"; \
	fi

# Generate API documentation
.PHONY: swagger
swagger: check-local
	@echo "Generating Swagger documentation..."
	@if command -v swag > /dev/null; then \
		swag init -g $(MAIN_PATH) -o ./api/swagger; \
	else \
		echo "swag not installed. Run: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# Help command
.PHONY: help
help:
	@echo "Jirani Backend - Local Development Makefile"
	@echo "==========================================="
	@echo "NOTE: This Makefile is intended for local development only."
	@echo "Do not run these commands on the production server."
	@echo ""
	@echo "Available commands:"
	@echo "  make build          - Build the application"
	@echo "  make build-multi    - Build for multiple architectures"
	@echo "  make run            - Run the application"
	@echo "  make clean          - Clean build artifacts"
	@echo "  make test           - Run tests"
	@echo "  make test-coverage  - Run tests with coverage"
	@echo "  make lint           - Lint the code"
	@echo "  make fmt            - Format the code"
	@echo "  make deps           - Update dependencies"
	@echo ""
	@echo "Docker commands:"
	@echo "  make docker-build       - Build Docker image locally"
	@echo "  make docker-push        - Push Docker image to registry"
	@echo "  make docker-up          - Start Docker containers locally"
	@echo "  make docker-down        - Stop Docker containers locally"
	@echo "  make docker-logs        - Show Docker logs"
	@echo "  make docker-rebuild-api - Rebuild and restart API container"
	@echo ""
	@echo "Deployment commands:"
	@echo "  make deploy-api         - One-step API deployment (build, push, deploy)"
	@echo "  make docker-login-server - Login to Docker Hub on the server"
	@echo "  make update-compose     - Update docker-compose.prod.yml on server"
	@echo "  make restart-api        - Restart API service on server"
	@echo ""
	@echo "Local development (without SSL):"
	@echo "  make local-up       - Start local development environment"
	@echo "  make local-down     - Stop local development environment"
	@echo "  make local-logs     - Show local development logs"
	@echo ""
	@echo "Database commands:"
	@echo "  make migrate-up     - Run database migrations locally"
	@echo "  make migrate-down   - Roll back database migrations locally"
	@echo ""
	@echo "Documentation:"
	@echo "  make swagger        - Generate Swagger documentation"
	@echo "  make help           - Show this help message"
