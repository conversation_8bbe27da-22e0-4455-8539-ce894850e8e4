# Jirani Production Environment Variables
# IMPORTANT: Copy this file to .env.prod and fill in your production secrets
# DO NOT commit the actual .env.prod file to version control

# Application Version
APP_VERSION=1.0.0

# PostgreSQL
POSTGRES_USER=jirani_prod
POSTGRES_PASSWORD=<strong-random-password>
POSTGRES_DB=jiranidb_prod

# Redis
REDIS_PASSWORD=<strong-random-password>

# RabbitMQ
RABBITMQ_USER=jirani_prod
RABBITMQ_PASS=<strong-random-password>

# MinIO
MINIO_ACCESS_KEY=jirani_prod
MINIO_SECRET_KEY=<strong-random-password>
MINIO_BUCKET=jirani-prod

# JWT
JWT_SECRET=<strong-random-jwt-secret-key>
JWT_EXPIRY=86400

# Mapbox
MAPBOX_TOKEN=<your-mapbox-token>

# Traefik
ACME_EMAIL=<EMAIL>
TRAEFIK_DASHBOARD_AUTH=admin:<hashed-password>

# <PERSON>ana
GRAFANA_ADMIN_USER=admin
GRA<PERSON>NA_ADMIN_PASSWORD=<strong-random-password>

# Email
EMAIL_PASSWORD=<your-email-password>
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_FROM=<EMAIL>

# Domain Configuration
DOMAIN=jirani.tufiked.live
API_DOMAIN=api.jirani.tufiked.live
STORAGE_DOMAIN=storage.jirani.tufiked.live
STORAGE_CONSOLE_DOMAIN=storage-console.jirani.tufiked.live
RABBITMQ_DOMAIN=rabbitmq.jirani.tufiked.live
TRAEFIK_DOMAIN=traefik.jirani.tufiked.live
