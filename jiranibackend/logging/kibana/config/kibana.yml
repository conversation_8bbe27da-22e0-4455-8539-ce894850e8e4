server.name: kibana.jirani.tufiked.live
server.host: "0.0.0.0"
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.requestTimeout: 90000
monitoring.ui.container.elasticsearch.enabled: true

# Default Kibana configuration
elasticsearch.username: ""
elasticsearch.password: ""
xpack.security.enabled: false
xpack.reporting.enabled: true
xpack.monitoring.enabled: true

# Index patterns
kibana.defaultAppId: "discover"
kibana.autocompleteTimeout: 1000
kibana.autocompleteTerminateAfter: 100000

# Logging configuration
logging.verbose: true
logging.json: true
logging.dest: stdout

# Saved objects configuration
savedObjects.maxImportExportSize: 10000
