# Receive logs from Docker containers
<source>
  @type forward
  port 24224
  bind 0.0.0.0
  tag docker
</source>

# Receive logs from files
<source>
  @type tail
  path /fluentd/logs/*.log
  pos_file /fluentd/logs/app.log.pos
  tag app
  <parse>
    @type json
    time_key time
    time_format %Y-%m-%dT%H:%M:%S.%NZ
  </parse>
</source>

# Filter and process API logs
<filter docker.jirani.api.**>
  @type parser
  key_name log
  reserve_data true
  <parse>
    @type json
    time_key time
    time_format %Y-%m-%dT%H:%M:%S.%NZ
  </parse>
</filter>

# Add Kubernetes-style metadata
<filter docker.**>
  @type record_transformer
  <record>
    kubernetes_container_name ${tag_parts[1]}
    kubernetes_namespace jirani
    source "docker"
  </record>
</filter>

# Add hostname and timestamp
<filter **>
  @type record_transformer
  <record>
    hostname "#{Socket.gethostname}"
    timestamp ${time}
  </record>
</filter>

# Route logs to different indices based on type
<match docker.jirani.api.**>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch
    port 9200
    logstash_format true
    logstash_prefix jirani-api
    logstash_dateformat %Y%m%d
    include_tag_key true
    tag_key @log_name
    flush_interval 5s
    retry_limit 20
    retry_wait 5s
    max_retry_wait 30s
    reconnect_on_error true
    reload_connections false
    request_timeout 15s
  </store>
  <store>
    @type stdout
  </store>
</match>

# Route database logs
<match docker.**.*postgres*.**>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch
    port 9200
    logstash_format true
    logstash_prefix jirani-db
    logstash_dateformat %Y%m%d
    include_tag_key true
    tag_key @log_name
    flush_interval 5s
  </store>
</match>

# Route Redis logs
<match docker.**.*redis*.**>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch
    port 9200
    logstash_format true
    logstash_prefix jirani-redis
    logstash_dateformat %Y%m%d
    include_tag_key true
    tag_key @log_name
    flush_interval 5s
  </store>
</match>

# Route file logs
<match app.**>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch
    port 9200
    logstash_format true
    logstash_prefix jirani-app
    logstash_dateformat %Y%m%d
    include_tag_key true
    tag_key @log_name
    flush_interval 5s
  </store>
  <store>
    @type stdout
  </store>
</match>

# Catch-all for other logs
<match **>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch
    port 9200
    logstash_format true
    logstash_prefix jirani-other
    logstash_dateformat %Y%m%d
    include_tag_key true
    tag_key @log_name
    flush_interval 5s
  </store>
  <store>
    @type stdout
  </store>
</match>
