cluster.name: "jirani-elasticsearch"
node.name: "jirani-es-node"
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node

# Memory settings
bootstrap.memory_lock: false

# Path settings
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# Security settings
xpack.security.enabled: false
xpack.security.transport.ssl.enabled: false

# Monitoring settings
xpack.monitoring.collection.enabled: true

# Index settings
action.auto_create_index: .monitoring*,.watches,.triggered_watches,.watcher-history*,.ml*

# Performance settings
thread_pool.write.queue_size: 1000
thread_pool.search.queue_size: 1000

# Index lifecycle management
xpack.ilm.enabled: true

# Index templates
index.number_of_shards: 1
index.number_of_replicas: 0

# Logging settings
logger.level: INFO
