# Jirani Backend

A robust backend for the Jirani application, built with Go Fiber and Docker.

## Architecture

The Jirani backend is built with a modern, containerized architecture:

- **API Service**: Go Fiber for fast, efficient API endpoints
- **Database**: PostgreSQL for reliable data storage
- **Cache**: Redis for high-performance caching
- **Message Queue**: RabbitMQ for asynchronous processing
- **Search Engine**: Elasticsearch for powerful search capabilities
- **File Storage**: MinIO for object storage
- **Reverse Proxy**: Traefik for routing and SSL termination
- **Monitoring**: Prometheus and Grafana for metrics and visualization
- **Logging**: Fluentd, Elasticsearch, and Kibana for centralized logging

## Project Structure

```
jiranibackend/
├── internal/                  # Internal packages
│   ├── config/                # Configuration
│   ├── controllers/           # HTTP request handlers
│   ├── database/              # Database connection and migrations
│   ├── middleware/            # HTTP middleware
│   ├── models/                # Database models
│   ├── routes/                # API routes
│   ├── services/              # Business logic
│   └── utils/                 # Utility functions
├── db/                        # Database initialization scripts
├── elasticsearch/             # Elasticsearch configuration
├── minio/                     # MinIO configuration
├── traefik/                   # Traefik configuration
├── monitoring/                # Monitoring configuration
├── logging/                   # Logging configuration
├── main.go                    # Application entry point
├── go.mod                     # Go module definition
├── go.sum                     # Go module checksums
├── Dockerfile                 # Docker build instructions
├── docker-compose.yml         # Docker Compose configuration
├── .env                       # Environment variables
└── README.md                  # Documentation
```

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Make (optional, for using the Makefile)

### Environment Setup

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration values.

### Running the Application

Start all services:

```bash
docker-compose up -d
```

Or use the Makefile:

```bash
make up
```

### Accessing Services

- API: https://api.jirani.tufiked.live
- Traefik Dashboard: https://traefik.jirani.tufiked.live
- Grafana: https://dashboard.jirani.tufiked.live
- Kibana: https://logs.jirani.tufiked.live
- MinIO Console: https://storage-console.jirani.tufiked.live

## API Documentation

The API documentation is available at https://api.jirani.tufiked.live/docs

## Features

### Authentication
- User registration and login
- JWT-based authentication
- Role-based access control

### User Management
- User profiles
- Location management

### Food Delivery
- Restaurant listings
- Menu items
- Order management

### Boda Boda Service
- Driver registration and verification
- Vehicle management
- Ride requests and tracking
- Real-time location updates

### Payment Processing
- Multiple payment methods
- Transaction history

## Development

### Adding New Features

1. Define models in the `internal/models/` directory
2. Create controllers in the `internal/controllers/` directory
3. Implement business logic in the `internal/services/` directory
4. Add routes in the `internal/routes/` directory
5. Update database migrations if necessary

## Deployment

The application is designed to be deployed using Docker Compose or Kubernetes.

### Optimized Deployment Process

We provide several optimized deployment scripts to streamline the deployment process:

#### Full Deployment

For a complete deployment (build, push, and deploy):

```bash
./deploy.sh
```

Options:
- `--skip-build`: Skip building the application and Docker image
- `--skip-push`: Skip pushing the Docker image to the registry
- `--skip-compose`: Skip updating the docker-compose.prod.yml file
- `--force-login`: Force Docker login on the server
- `--restart-nginx`: Restart Nginx after deployment

#### Code-Only Updates

For quick updates to the code without rebuilding the Docker image:

```bash
./update-code.sh
```

Options:
- `--skip-build`: Skip building the application
- `--no-restart`: Don't restart the API service

#### Using the Makefile

The Makefile provides several targets for deployment:

```bash
# One-step API deployment
make deploy-api

# Individual steps
make build
make docker-build
make docker-push
make update-compose
make restart-api
```

### Docker Compose Deployment

For manual deployment using Docker Compose:

```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

Kubernetes manifests are available in the `k8s/` directory.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
