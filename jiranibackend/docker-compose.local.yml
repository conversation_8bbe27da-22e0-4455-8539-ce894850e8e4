version: '3.8'

services:
  # API Service (Go Fiber)
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jiranibackend_api
    restart: unless-stopped
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_NAME=${POSTGRES_DB}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=${RABBITMQ_USER}
      - RABBITMQ_PASS=${RABBITMQ_PASS}
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=${MINIO_BUCKET}
      - JWT_SECRET=${JWT_SECRET}
      - MAPBOX_TOKEN=${MAPBOX_TOKEN}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_FROM=${EMAIL_FROM}
    volumes:
      - ./internal:/app/internal
      - ./cmd:/app/cmd
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - minio
    networks:
      - jirani-network-local
    ports:
      - "8080:8080"

  # Database (PostgreSQL)
  postgres:
    image: postgres:15-alpine
    container_name: jirani-postgres-local
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres-data-local:/var/lib/postgresql/data
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - jirani-network-local
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache (Redis)
  redis:
    image: redis:7-alpine
    container_name: jirani-redis-local
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data-local:/data
    ports:
      - "6379:6379"
    networks:
      - jirani-network-local
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Message Queue (RabbitMQ)
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: jirani-rabbitmq-local
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS}
    volumes:
      - rabbitmq-data-local:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - jirani-network-local
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5

  # File Storage (MinIO)
  minio:
    image: minio/minio
    container_name: jirani-minio-local
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    volumes:
      - minio-data-local:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - jirani-network-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Reverse Proxy (Nginx)
  nginx:
    image: nginx:alpine
    container_name: jirani-nginx-local
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/jirani-local.conf:/etc/nginx/conf.d/default.conf:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - jirani-network-local
    depends_on:
      - api
      - prometheus
      - grafana
      - kibana
      - minio
      - elasticsearch
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring: Prometheus (simplified for local)
  prometheus:
    image: prom/prometheus
    container_name: jirani-prometheus-local
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus-data-local:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    ports:
      - "9090:9090"
    networks:
      - jirani-network-local

  # Monitoring: Grafana (simplified for local)
  grafana:
    image: grafana/grafana
    container_name: jirani-grafana-local
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - ./monitoring/grafana:/etc/grafana/provisioning
      - grafana-data-local:/var/lib/grafana
    ports:
      - "3000:3000"
    networks:
      - jirani-network-local
    depends_on:
      - prometheus

  # Elasticsearch (simplified for local)
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: jirani-elasticsearch-local
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data-local:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - jirani-network-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana (simplified for local)
  kibana:
    image: kibana:8.8.0
    container_name: jirani-kibana-local
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - jirani-network-local
    depends_on:
      - elasticsearch

networks:
  jirani-network-local:
    driver: bridge

volumes:
  postgres-data-local:
  redis-data-local:
  rabbitmq-data-local:
  elasticsearch-data-local:
  minio-data-local:
  prometheus-data-local:
  grafana-data-local:
