# Jirani Production Deployment Guide

This guide provides instructions for deploying the Jirani application in a production environment.

## Prerequisites

- A server with <PERSON><PERSON> and <PERSON>er Compose installed
- Domain names configured to point to your server's IP address
- Basic knowledge of Docker and Linux server administration

## Domain Configuration

The production setup uses the following domains:

- `jirani.tufiked.live` - Main application domain
- `api.jirani.tufiked.live` - API service
- `storage.jirani.tufiked.live` - MinIO storage API
- `storage-console.jirani.tufiked.live` - MinIO web console
- `rabbitmq.jirani.tufiked.live` - RabbitMQ management interface
- `traefik.jirani.tufiked.live` - Traefik dashboard

Ensure all these domains are configured in your DNS provider to point to your server's IP address.

## Server Requirements

Minimum recommended specifications:

- 4 CPU cores
- 8GB RAM
- 50GB SSD storage
- Ubuntu 20.04 LTS or newer

## Deployment Steps

### 1. Prepare the Environment

1. Clone the repository to your server:
   ```bash
   git clone https://github.com/yourusername/jirani-app.git
   cd jirani-app/jiranibackend
   ```

2. Create a production environment file:
   ```bash
   cp .env.prod.example .env.prod
   ```

3. Edit the `.env.prod` file with your production secrets:
   ```bash
   nano .env.prod
   ```

   Make sure to replace all placeholder values with strong, unique passwords and appropriate configuration values.

### 2. Generate Traefik Dashboard Password

Generate a hashed password for the Traefik dashboard:

```bash
htpasswd -nb admin your_secure_password
```

Copy the output (including the `admin:` prefix) and paste it as the value for `TRAEFIK_DASHBOARD_AUTH` in your `.env.prod` file.

### 3. Deploy the Application

Run the deployment script:

```bash
./deploy-production.sh
```

This script will:
- Create necessary directories
- Set appropriate permissions
- Create required Docker networks
- Pull the latest Docker images
- Build and start all services
- Check service health

### 4. Verify Deployment

After deployment, verify that all services are running:

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod ps
```

All services should be in the "Up" state.

### 5. Access Services

You can now access your services using the configured domains:

- API: `https://api.jirani.tufiked.live`
- Storage API: `https://storage.jirani.tufiked.live`
- Storage Console: `https://storage-console.jirani.tufiked.live`
- RabbitMQ Management: `https://rabbitmq.jirani.tufiked.live`
- Traefik Dashboard: `https://traefik.jirani.tufiked.live`

## Maintenance

### Viewing Logs

To view logs for all services:

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod logs -f
```

To view logs for a specific service:

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod logs -f api
```

### Updating Services

To update services with the latest changes:

1. Pull the latest code:
   ```bash
   git pull
   ```

2. Run the deployment script again:
   ```bash
   ./deploy-production.sh
   ```

### Stopping Services

To stop all services:

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod down
```

To stop all services and remove volumes (WARNING: This will delete all data):

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod down -v
```

## Backup Strategy

### Database Backup

Create a script to regularly backup the PostgreSQL database:

```bash
#!/bin/bash
BACKUP_DIR="/path/to/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/jirani_db_$TIMESTAMP.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Backup the database
docker exec jirani-postgres pg_dump -U jirani_prod jiranidb_prod > $BACKUP_FILE

# Compress the backup
gzip $BACKUP_FILE

# Keep only the last 7 backups
ls -tp $BACKUP_DIR/jirani_db_*.sql.gz | grep -v '/$' | tail -n +8 | xargs -I {} rm -- {}
```

Add this script to a cron job to run daily:

```bash
0 2 * * * /path/to/backup_script.sh
```

### MinIO Backup

For MinIO data, you can use the MinIO Client (mc) to sync data to another storage location:

```bash
mc config host add jirani-minio https://storage.jirani.tufiked.live MINIO_ACCESS_KEY MINIO_SECRET_KEY
mc mirror --watch jirani-minio/jirani-prod /path/to/backup/minio
```

## Security Considerations

1. Regularly update all Docker images to get the latest security patches
2. Monitor logs for suspicious activity
3. Implement a firewall to restrict access to only necessary ports
4. Set up monitoring and alerting for system resources and service health
5. Regularly rotate passwords and access keys

## Troubleshooting

### Service Won't Start

Check the logs for the specific service:

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod logs api
```

### SSL Certificate Issues

If you're having issues with SSL certificates:

1. Check that your domains are correctly pointing to your server
2. Ensure ports 80 and 443 are open on your firewall
3. Check Traefik logs for certificate issuance errors:
   ```bash
   docker-compose -f docker-compose.prod.yml --env-file .env.prod logs traefik
   ```

### Database Connection Issues

If the API can't connect to the database:

1. Check that the PostgreSQL service is running
2. Verify the database credentials in `.env.prod`
3. Check the API logs for specific error messages
