# Jirani App Monitoring and Observability Stack

This document describes the monitoring and observability stack for the Jirani App backend.

## Overview

The Jirani App backend uses a comprehensive monitoring and observability stack consisting of:

1. **Prometheus**: For metrics collection and alerting
2. **Grafana**: For metrics visualization and dashboards
3. **Elasticsearch**: For log storage and search
4. **Fluentd**: For log collection and processing
5. **Kibana**: For log visualization and analysis

## Metrics Collection

### Application Metrics

The application exposes the following metrics:

#### HTTP Metrics
- `jirani_http_requests_total`: Total number of HTTP requests (labels: method, path, status)
- `jirani_http_request_duration_seconds`: HTTP request duration in seconds (labels: method, path, status)
- `jirani_http_requests_in_progress`: Number of HTTP requests currently in progress (labels: method, path)
- `jirani_http_request_size_bytes`: HTTP request size in bytes (labels: method, path)
- `jirani_http_response_size_bytes`: HTTP response size in bytes (labels: method, path, status)
- `jirani_http_errors_total`: Total number of HTTP errors (labels: method, path, status)

#### Business Metrics
- `jirani_users_registrations_total`: Total number of user registrations (labels: source)
- `jirani_users_logins_total`: Total number of user logins (labels: status)
- `jirani_users_active`: Number of active users (labels: timeframe)
- `jirani_orders_total`: Total number of orders (labels: status, payment_method)
- `jirani_orders_in_progress`: Number of orders currently in progress (labels: status)
- `jirani_orders_preparation_time_seconds`: Time taken to prepare an order in seconds (labels: restaurant_id)
- `jirani_orders_delivery_time_seconds`: Time taken to deliver an order in seconds (labels: restaurant_id)
- `jirani_orders_cancellations_total`: Total number of order cancellations (labels: reason, stage)
- `jirani_orders_value_ksh`: Order value in KSH (labels: restaurant_id)
- `jirani_restaurants_total`: Total number of restaurants (labels: status)
- `jirani_restaurants_orders_total`: Total number of orders per restaurant (labels: restaurant_id)
- `jirani_restaurants_ratings`: Restaurant ratings (labels: restaurant_id)
- `jirani_drivers_total`: Total number of drivers (labels: status)
- `jirani_drivers_active`: Number of active drivers
- `jirani_drivers_deliveries_total`: Total number of deliveries per driver (labels: driver_id)
- `jirani_drivers_ratings`: Driver ratings (labels: driver_id)
- `jirani_drivers_delivery_time_seconds`: Time taken by driver to deliver an order in seconds (labels: driver_id)
- `jirani_payments_total`: Total number of payments (labels: method, status)
- `jirani_payments_amount_ksh`: Payment amount in KSH (labels: method)
- `jirani_payments_failures_total`: Total number of payment failures (labels: method, reason)

#### Database Metrics
- `jirani_db_connections_open`: Number of open database connections (labels: db_name)
- `jirani_db_connections_idle`: Number of idle database connections (labels: db_name)
- `jirani_db_connections_in_use`: Number of database connections in use (labels: db_name)
- `jirani_db_connections_waiting`: Number of connections waiting for a database connection (labels: db_name)
- `jirani_db_query_duration_seconds`: Duration of database queries in seconds (labels: db_name, query_type)
- `jirani_db_queries_total`: Total number of database queries (labels: db_name, query_type)
- `jirani_db_query_errors_total`: Total number of database query errors (labels: db_name, query_type, error_type)
- `jirani_db_transactions_total`: Total number of database transactions (labels: db_name, status)
- `jirani_db_transaction_errors_total`: Total number of database transaction errors (labels: db_name, error_type)
- `jirani_db_transaction_duration_seconds`: Duration of database transactions in seconds (labels: db_name)
- `jirani_db_connection_acquire_seconds`: Time to acquire a database connection in seconds (labels: db_name)

#### System Metrics
- `jirani_system_memory_usage_bytes`: Memory usage in bytes (labels: type)
- `jirani_system_cpu_usage_percent`: CPU usage in percent (labels: mode)
- `jirani_system_goroutines_count`: Number of goroutines
- `jirani_system_gc_pause_seconds`: GC pause time in seconds
- `jirani_system_heap_objects`: Number of allocated heap objects
- `jirani_system_heap_alloc_bytes`: Heap allocation in bytes
- `jirani_system_threads_created`: Number of OS threads created
- `jirani_system_mutex_contentions_total`: Total number of mutex contentions

### Infrastructure Metrics

The following infrastructure metrics are collected:

- **Node Metrics**: CPU, memory, disk, and network metrics from the host machine
- **Container Metrics**: CPU, memory, and network metrics from Docker containers
- **PostgreSQL Metrics**: Connection pool stats, query stats, and table stats
- **Redis Metrics**: Memory usage, connections, and command stats

## Logging

The application uses structured JSON logging with the following fields:

- `time`: Timestamp in RFC3339Nano format
- `level`: Log level (DEBUG, INFO, WARN, ERROR, FATAL)
- `message`: Log message
- `service`: Service name
- `environment`: Environment name
- `caller`: Caller function name
- `file`: Source file path
- `line`: Source line number
- `data`: Additional data specific to the log entry

Logs are collected by Fluentd and stored in Elasticsearch with the following indices:

- `jirani-api-YYYYMMDD`: API logs
- `jirani-db-YYYYMMDD`: Database logs
- `jirani-redis-YYYYMMDD`: Redis logs
- `jirani-app-YYYYMMDD`: Application logs
- `jirani-other-YYYYMMDD`: Other logs

## Dashboards

### Grafana Dashboards

The following Grafana dashboards are available:

1. **API Performance Dashboard**: Shows API response times, error rates, and request counts
2. **Database Performance Dashboard**: Shows query times, connection pool stats, and error rates
3. **System Resources Dashboard**: Shows CPU, memory, disk, and network usage
4. **Business Metrics Dashboard**: Shows orders, users, restaurants, and drivers metrics

### Kibana Dashboards

The following Kibana dashboards are available:

1. **API Logs Dashboard**: Shows API logs, error rates, and request patterns
2. **Error Logs Dashboard**: Shows error logs across all services
3. **User Activity Dashboard**: Shows user registration and login patterns
4. **Order Activity Dashboard**: Shows order creation and fulfillment patterns

## Alerting

Prometheus is configured with the following alerting rules:

1. **High Error Rate**: Alerts when the error rate is above 5% for 5 minutes
2. **High Response Time**: Alerts when the 95th percentile of response time is above 1 second for 5 minutes
3. **High Database Query Time**: Alerts when the 95th percentile of database query time is above 0.5 seconds for 5 minutes
4. **High Database Error Rate**: Alerts when the database error rate is above 5% for 5 minutes
5. **High Memory Usage**: Alerts when memory usage is above 500MB for 5 minutes
6. **High Goroutine Count**: Alerts when the goroutine count is above 1000 for 5 minutes
7. **High Order Cancellation Rate**: Alerts when the order cancellation rate is above 10% for 1 hour
8. **Low Driver Availability**: Alerts when less than 50% of active drivers are available for 15 minutes
9. **High Payment Failure Rate**: Alerts when the payment failure rate is above 5% for 1 hour
10. **Long Order Delivery Time**: Alerts when the 95th percentile of order delivery time is above 1 hour for 1 hour

## Access

- **Prometheus**: https://metrics.jirani.tufiked.live
- **Grafana**: https://dashboard.jirani.tufiked.live
- **Kibana**: https://logs.jirani.tufiked.live
- **Elasticsearch**: https://es.jirani.tufiked.live
