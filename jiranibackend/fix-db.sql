-- Fix the constraint issue by creating a new constraint with the correct name
D<PERSON> $$
BEGIN
    -- Check if the constraint exists
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'idx_users_email'
    ) THEN
        -- Add the constraint with the correct name
        ALTER TABLE users ADD CONSTRAINT idx_users_email UNIQUE (email);
    END IF;

    -- Check if the constraint exists
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'idx_users_phone'
    ) THEN
        -- Add the constraint with the correct name
        ALTER TABLE users ADD CONSTRAINT idx_users_phone UNIQUE (phone_number);
    END IF;
END $$;

-- Add deleted_at column to users table for soft delete functionality
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'deleted_at'
    ) THEN
        -- Add the deleted_at column
        ALTER TABLE users ADD COLUMN deleted_at TIMESTAMP NULL;
    END IF;
END $$;
