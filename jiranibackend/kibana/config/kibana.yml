server.name: kibana
server.host: "0.0.0.0"
server.port: 5601

elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: "elastic"
elasticsearch.password: "changeme"

monitoring.ui.container.elasticsearch.enabled: true

# Disable security features
xpack.security.enabled: false

# Enable features
xpack.reporting.enabled: true
xpack.monitoring.enabled: true

# Disable X-Pack security features
xpack.security.audit.enabled: false

# Disable TLS
server.ssl.enabled: false

# Disable telemetry
telemetry.enabled: false

# Disable reporting
xpack.reporting.capture.browser.chromium.disableSandbox: true

# Set the Kibana index
kibana.index: ".kibana"

# Set the default app to open
kibana.defaultAppId: "discover"

# Set the timezone
dateFormat:tz: "UTC"
