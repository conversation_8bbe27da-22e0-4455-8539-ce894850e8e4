#!/bin/bash

# This script checks if the domains are properly configured to point to the server's IP address

# Set variables
SERVER_IP=$(curl -s ifconfig.me)
DOMAINS=(api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live)

echo "Checking if domains are properly configured to point to $SERVER_IP..."
echo

all_ok=true

for domain in "${DOMAINS[@]}"; do
  echo -n "Checking $domain... "
  
  # Get the IP address that the domain resolves to
  domain_ip=$(dig +short $domain)
  
  if [ -z "$domain_ip" ]; then
    echo "ERROR: Domain $domain does not resolve to any IP address."
    all_ok=false
  elif [ "$domain_ip" != "$SERVER_IP" ]; then
    echo "ERROR: Domain $domain resolves to $domain_ip, but should resolve to $SERVER_IP."
    all_ok=false
  else
    echo "OK: Domain $domain resolves to $SERVER_IP."
  fi
done

echo

if [ "$all_ok" = true ]; then
  echo "All domains are properly configured!"
  exit 0
else
  echo "Some domains are not properly configured. Please fix the DNS settings and try again."
  exit 1
fi
