-- Migration script to create BodaBoda tables
-- This script will create the missing tables and update existing ones

-- First, let's update the drivers table to match the BodaBoda model
ALTER TABLE drivers 
ADD COLUMN IF NOT EXISTS id_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS last_location_update TIMESTAMP,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP;

-- Create unique indexes for new columns if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'drivers_license_number_key') THEN
        ALTER TABLE drivers ADD CONSTRAINT drivers_license_number_key UNIQUE (license_number);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'drivers_id_number_key') THEN
        ALTER TABLE drivers ADD CONSTRAINT drivers_id_number_key UNIQUE (id_number);
    END IF;
END $$;

-- Create vehicles table if it doesn't exist
CREATE TABLE IF NOT EXISTS vehicles (
    id SERIAL PRIMARY KEY,
    driver_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    make VARCHAR(50) NOT NULL,
    model VARCHAR(50) NOT NULL,
    year INTEGER NOT NULL,
    color VARCHAR(20) NOT NULL,
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE
);

-- Create index on vehicles if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_vehicles_driver_id ON vehicles(driver_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_deleted_at ON vehicles(deleted_at);

-- Create rides table if it doesn't exist
CREATE TABLE IF NOT EXISTS rides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    driver_id UUID,
    vehicle_id INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'requested',
    pickup_address VARCHAR(255) NOT NULL,
    pickup_latitude DECIMAL(10,7) NOT NULL,
    pickup_longitude DECIMAL(10,7) NOT NULL,
    dropoff_address VARCHAR(255) NOT NULL,
    dropoff_latitude DECIMAL(10,7) NOT NULL,
    dropoff_longitude DECIMAL(10,7) NOT NULL,
    distance DECIMAL(10,2) NOT NULL,
    duration INTEGER NOT NULL,
    estimated_fare DECIMAL(10,2) NOT NULL,
    actual_fare DECIMAL(10,2),
    surge_factor DECIMAL(3,2) DEFAULT 1.0,
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    current_latitude DECIMAL(10,7),
    current_longitude DECIMAL(10,7),
    last_location_update TIMESTAMP,
    estimated_pickup_time TIMESTAMP,
    estimated_arrival_time TIMESTAMP,
    emergency_triggered BOOLEAN DEFAULT false,
    emergency_details TEXT,
    emergency_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    accepted_at TIMESTAMP,
    en_route_to_pickup_at TIMESTAMP,
    arrived_at_pickup_at TIMESTAMP,
    pickup_at TIMESTAMP,
    dropoff_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    deleted_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE SET NULL,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE SET NULL
);

-- Create indexes on rides table
CREATE INDEX IF NOT EXISTS idx_rides_user_id ON rides(user_id);
CREATE INDEX IF NOT EXISTS idx_rides_driver_id ON rides(driver_id);
CREATE INDEX IF NOT EXISTS idx_rides_vehicle_id ON rides(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_rides_status ON rides(status);
CREATE INDEX IF NOT EXISTS idx_rides_deleted_at ON rides(deleted_at);

-- Create ride_ratings table if it doesn't exist
CREATE TABLE IF NOT EXISTS ride_ratings (
    id SERIAL PRIMARY KEY,
    ride_id UUID NOT NULL,
    user_id UUID NOT NULL,
    driver_id UUID NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    FOREIGN KEY (ride_id) REFERENCES rides(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE
);

-- Create indexes on ride_ratings table
CREATE INDEX IF NOT EXISTS idx_ride_ratings_ride_id ON ride_ratings(ride_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_user_id ON ride_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_driver_id ON ride_ratings(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_deleted_at ON ride_ratings(deleted_at);

-- Create driver_ratings table if it doesn't exist
CREATE TABLE IF NOT EXISTS driver_ratings (
    id SERIAL PRIMARY KEY,
    driver_id UUID NOT NULL,
    user_id UUID NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes on driver_ratings table
CREATE INDEX IF NOT EXISTS idx_driver_ratings_driver_id ON driver_ratings(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_ratings_user_id ON driver_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_ratings_deleted_at ON driver_ratings(deleted_at);

-- Create rider_ratings table if it doesn't exist
CREATE TABLE IF NOT EXISTS rider_ratings (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    driver_id UUID NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE
);

-- Create indexes on rider_ratings table
CREATE INDEX IF NOT EXISTS idx_rider_ratings_user_id ON rider_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_rider_ratings_driver_id ON rider_ratings(driver_id);
CREATE INDEX IF NOT EXISTS idx_rider_ratings_deleted_at ON rider_ratings(deleted_at);

-- Create device_tokens table if it doesn't exist
CREATE TABLE IF NOT EXISTS device_tokens (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    token VARCHAR(255) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes on device_tokens table
CREATE INDEX IF NOT EXISTS idx_device_tokens_user_id ON device_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_device_tokens_token ON device_tokens(token);
CREATE INDEX IF NOT EXISTS idx_device_tokens_deleted_at ON device_tokens(deleted_at);

-- Update payments table to support rides if needed
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS ride_id UUID,
ADD CONSTRAINT IF NOT EXISTS payments_ride_id_fkey FOREIGN KEY (ride_id) REFERENCES rides(id) ON DELETE SET NULL;

-- Create index on payments.ride_id
CREATE INDEX IF NOT EXISTS idx_payments_ride_id ON payments(ride_id);

COMMIT;
