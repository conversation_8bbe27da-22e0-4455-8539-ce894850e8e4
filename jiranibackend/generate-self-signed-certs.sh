#!/bin/bash

# This script generates self-signed certificates for each domain

# Set variables
domains=(api.jirani.tufiked.live storage.jirani.tufiked.live admin.jirani.tufiked.live console.jirani.tufiked.live)
output_dir="./nginx/conf/ssl"

# Create output directory
mkdir -p "$output_dir"

# Generate a self-signed certificate for each domain
for domain in "${domains[@]}"; do
  echo "Generating self-signed certificate for $domain..."
  
  # Generate private key
  openssl genrsa -out "$output_dir/$domain.key" 2048
  
  # Generate CSR
  openssl req -new -key "$output_dir/$domain.key" -out "$output_dir/$domain.csr" -subj "/CN=$domain"
  
  # Generate self-signed certificate
  openssl x509 -req -days 365 -in "$output_dir/$domain.csr" -signkey "$output_dir/$domain.key" -out "$output_dir/$domain.crt"
  
  # Remove CSR
  rm "$output_dir/$domain.csr"
  
  echo "Self-signed certificate for $domain generated successfully!"
  echo
done

# Copy the SSL configuration file
cp ./nginx/conf/ssl.conf "$output_dir/"

echo "All self-signed certificates generated successfully!"
