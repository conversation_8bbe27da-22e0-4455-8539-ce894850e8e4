#!/bin/bash

# Test script for storage service

# Check if a file path was provided
if [ $# -lt 1 ]; then
  echo "Usage: $0 <image_file_path>"
  echo "Example: $0 test.png"
  exit 1
fi

# Get the file path
FILE_PATH=$1

# Run the Go test script
echo "Running upload test..."
go run test_upload.go "$FILE_PATH"

# Check if the upload was successful
if [ $? -ne 0 ]; then
  echo "Upload test failed!"
  exit 1
fi

echo ""
echo "Testing complete!"
echo "If the upload was successful, you should see a URL in the response."
echo "Try opening the URL in your browser to verify the image is accessible."
