-- Migration: Add Location History Table
-- PRP: PRP-LOCATION-ENH-001
-- Description: Create user_location_history table for storing frequently used locations
-- Date: 2025-01-03

-- Create location history table
CREATE TABLE IF NOT EXISTS user_location_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT NOT NULL,
    location_type VARCHAR(20) NOT NULL CHECK (location_type IN ('pickup', 'destination', 'favorite', 'home', 'work')),
    usage_count INTEGER DEFAULT 1,
    last_used_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Foreign key constraint (assuming users table exists)
    CONSTRAINT fk_user_location_history_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_location_history_user_id 
    ON user_location_history(user_id);

CREATE INDEX IF NOT EXISTS idx_user_location_history_usage 
    ON user_location_history(user_id, usage_count DESC, last_used_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_location_history_type 
    ON user_location_history(user_id, location_type);

CREATE INDEX IF NOT EXISTS idx_user_location_history_location 
    ON user_location_history(latitude, longitude);

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_location_history_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_location_history_updated_at
    BEFORE UPDATE ON user_location_history
    FOR EACH ROW
    EXECUTE FUNCTION update_location_history_updated_at();

-- Add comments for documentation
COMMENT ON TABLE user_location_history IS 'Stores user location history for quick selection and suggestions';
COMMENT ON COLUMN user_location_history.location_type IS 'Type of location: pickup, destination, favorite, home, work';
COMMENT ON COLUMN user_location_history.usage_count IS 'Number of times this location has been used';
COMMENT ON COLUMN user_location_history.last_used_at IS 'Last time this location was used';

-- Insert sample data for testing (optional)
-- This will be removed in production
INSERT INTO user_location_history (user_id, latitude, longitude, address, location_type, usage_count) 
VALUES 
    ('00000000-0000-0000-0000-000000000001', -1.2921, 36.8219, 'Nairobi CBD, Kenya', 'favorite', 5),
    ('00000000-0000-0000-0000-000000000001', -1.3032, 36.8856, 'Westlands, Nairobi', 'work', 10),
    ('00000000-0000-0000-0000-000000000001', -1.2634, 36.8047, 'Karen, Nairobi', 'home', 15)
ON CONFLICT DO NOTHING;
