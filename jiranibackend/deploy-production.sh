#!/bin/bash
# Production deployment script for Jirani Backend

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print with color
print_green() {
    echo -e "${GREEN}$1${NC}"
}

print_yellow() {
    echo -e "${YELLOW}$1${NC}"
}

print_red() {
    echo -e "${RED}$1${NC}"
}

# Check if .env.prod exists
if [ ! -f .env.prod ]; then
    print_red "Error: .env.prod file not found!"
    print_yellow "Please create a .env.prod file based on .env.prod.example"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_red "Error: Docker is not installed!"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_red "Error: Docker Compose is not installed!"
    exit 1
fi

# Create required directories
print_yellow "Creating required directories..."
mkdir -p traefik/logs
mkdir -p traefik/dynamic
mkdir -p traefik/acme

# Set proper permissions
print_yellow "Setting proper permissions..."
chmod 600 .env.prod
chmod +x deploy-production.sh

# Create Docker networks if they don't exist
print_yellow "Creating Docker networks..."
docker network create jirani-network 2>/dev/null || true
docker network create traefik-network 2>/dev/null || true

# Pull latest images
print_yellow "Pulling latest Docker images..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod pull

# Build and start services
print_green "Building and starting services..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d --build

# Check service health
print_yellow "Checking service health..."
sleep 10
docker-compose -f docker-compose.prod.yml --env-file .env.prod ps

print_green "Deployment completed successfully!"
print_yellow "Your services should be available at the following URLs:"
echo "- API: https://api.jirani.tufiked.live"
echo "- Storage: https://storage.jirani.tufiked.live"
echo "- Storage Console: https://storage-console.jirani.tufiked.live"
echo "- RabbitMQ Management: https://rabbitmq.jirani.tufiked.live"
echo "- Traefik Dashboard: https://traefik.jirani.tufiked.live"

print_yellow "To view logs, run:"
echo "docker-compose -f docker-compose.prod.yml --env-file .env.prod logs -f [service_name]"

print_yellow "To stop all services, run:"
echo "docker-compose -f docker-compose.prod.yml --env-file .env.prod down"

print_yellow "To update services, run this script again."
