#!/bin/bash

# <PERSON><PERSON><PERSON> to manually verify a driver and set them up for testing
# This requires admin access

API_BASE_URL="https://api.jirani.tufiked.live/api/v1"

# Admin credentials (you'll need to update these)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Kimathi@2022"

echo "🔧 Admin Driver Verification Script"
echo "==================================="

# Login as admin
echo "🔐 Logging in as admin..."
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$ADMIN_EMAIL\",
    \"password\": \"$ADMIN_PASSWORD\"
  }")

echo "Admin Login Response: $ADMIN_LOGIN_RESPONSE"

# Extract admin token
ADMIN_TOKEN=$(echo "$ADMIN_LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ADMIN_TOKEN" ]; then
  echo "❌ Failed to get admin token. Login failed."
  exit 1
fi

echo "✅ Admin login successful!"
echo ""

# Get all drivers
echo "📋 Getting all drivers..."
DRIVERS_RESPONSE=$(curl -s -X GET "$API_BASE_URL/admin/drivers" \
  -H "Authorization: Bearer $ADMIN_TOKEN")

echo "Drivers Response: $DRIVERS_RESPONSE"
echo ""

# Extract driver ID (assuming the last registered driver is the one we want)
DRIVER_ID=$(echo "$DRIVERS_RESPONSE" | grep -o '"id":"[^"]*"' | tail -1 | cut -d'"' -f4)

if [ -z "$DRIVER_ID" ]; then
  echo "❌ No driver found to verify."
  exit 1
fi

echo "🎯 Found driver ID: $DRIVER_ID"
echo ""

# Verify the driver
echo "✅ Verifying driver..."
VERIFY_RESPONSE=$(curl -s -X PUT "$API_BASE_URL/admin/drivers/$DRIVER_ID/verify" \
  -H "Authorization: Bearer $ADMIN_TOKEN")

echo "Verify Response: $VERIFY_RESPONSE"

if echo "$VERIFY_RESPONSE" | grep -q "error"; then
  echo "❌ Driver verification failed."
else
  echo "✅ Driver verified successfully!"
fi

echo ""
echo "🎉 Driver verification complete!"
echo "Driver ID: $DRIVER_ID"
echo "The driver should now be able to go online and accept rides."
