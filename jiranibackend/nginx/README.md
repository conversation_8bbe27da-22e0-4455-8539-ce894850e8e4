# Nginx Configuration for Jirani App

This directory contains the Nginx configuration files for the Jirani App project. Nginx is used as a reverse proxy to route traffic to the various services in the application.

## Directory Structure

- `nginx.conf`: Main Nginx configuration file
- `conf.d/`: Directory containing server configurations
  - `jirani.conf`: Production configuration for jirani.tufiked.live and subdomains
  - `jirani-local.conf`: Local development configuration for localhost and subdomains
- `ssl/`: Directory for SSL certificates
  - `jirani/`: Production SSL certificates
  - `localhost.crt` and `localhost.key`: Self-signed certificates for local development
- `auth/`: Directory for basic authentication files
  - `.htpasswd`: Basic authentication file for restricted access
- `logs/`: Directory for Nginx logs

## Setup

### Local Development

To set up the local development environment:

1. Run the setup script:
   ```
   ./setup-local.sh
   ```

   This script will:
   - Create necessary directories
   - Generate a self-signed SSL certificate for localhost
   - Create a .htpasswd file with default credentials (admin/admin)
   - Add localhost entries to /etc/hosts
   - Rename the Docker Compose files

2. Start the local environment:
   ```
   docker-compose -f docker-compose.local.yml up -d
   ```

3. Access the services:
   - API: http://localhost or http://api.localhost
   - Prometheus: http://metrics.localhost
   - Grafana: http://dashboard.localhost
   - Kibana: http://logs.localhost
   - MinIO API: http://storage.localhost
   - MinIO Console: http://storage-console.localhost
   - Elasticsearch: http://es.localhost

### Production

To set up the production environment:

1. Run the setup script:
   ```
   ./setup-production.sh
   ```

   This script will:
   - Create necessary directories
   - Create a .htpasswd file with default credentials (admin/admin)
   - Install certbot if not already installed
   - Obtain SSL certificates for the domains
   - Copy the certificates to the Nginx SSL directory
   - Rename the Docker Compose files

2. Start the production environment:
   ```
   docker-compose up -d
   ```

3. Access the services:
   - API: https://jirani.tufiked.live or https://api.jirani.tufiked.live
   - Prometheus: https://metrics.jirani.tufiked.live
   - Grafana: https://dashboard.jirani.tufiked.live
   - Kibana: https://logs.jirani.tufiked.live
   - MinIO API: https://storage.jirani.tufiked.live
   - MinIO Console: https://storage-console.jirani.tufiked.live
   - Elasticsearch: https://es.jirani.tufiked.live

## SSL Certificates

### Local Development

For local development, a self-signed SSL certificate is generated. You will need to accept the security warning in your browser when accessing the services.

### Production

For production, SSL certificates are obtained using Let's Encrypt. The certificates are valid for 90 days and will be automatically renewed by certbot.

## Basic Authentication

Some services (Prometheus, Kibana, Elasticsearch) are protected with basic authentication. The default credentials are:

- Username: admin
- Password: admin

You can change these credentials by running:

```
./nginx/generate-htpasswd.sh <username> <password>
```

## Troubleshooting

### SSL Certificate Issues

If you encounter SSL certificate issues in production, you can manually obtain new certificates by running:

```
./nginx/obtain-ssl-certs.sh <email>
```

### Nginx Configuration Issues

To test the Nginx configuration:

```
docker exec jirani-nginx nginx -t
```

### Logs

Nginx logs are stored in the `nginx/logs/` directory:

- `access.log`: Access logs
- `error.log`: Error logs
