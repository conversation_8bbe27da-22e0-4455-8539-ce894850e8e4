# Default server configuration
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# API Server
server {
    listen 80;
    listen [::]:80;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name api.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.jirani.tufiked.live/privkey.pem;

    # Include our custom SSL configuration
    include /etc/nginx/conf.d/ssl.conf;

    # API Proxy
    location / {
        proxy_pass http://api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
}

# Storage Server (MinIO API)
server {
    listen 80;
    listen [::]:80;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/storage.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/storage.jirani.tufiked.live/privkey.pem;

    # Include our custom SSL configuration
    include /etc/nginx/conf.d/ssl.conf;

    # MinIO API Proxy
    location / {
        proxy_pass http://minio:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
}

# Admin Server (RabbitMQ Management)
server {
    listen 80;
    listen [::]:80;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name admin.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/admin.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.jirani.tufiked.live/privkey.pem;

    # Include our custom SSL configuration
    include /etc/nginx/conf.d/ssl.conf;

    # RabbitMQ Management Proxy
    location / {
        proxy_pass http://rabbitmq:15672;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Basic Authentication
        auth_basic "Restricted Access";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}

# Console Server (MinIO Console)
server {
    listen 80;
    listen [::]:80;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # For Let's Encrypt certificate renewal
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name console.jirani.tufiked.live;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/console.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/console.jirani.tufiked.live/privkey.pem;

    # Include our custom SSL configuration
    include /etc/nginx/conf.d/ssl.conf;

    # MinIO Console Proxy
    location / {
        proxy_pass http://minio:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # MinIO requires these headers
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # Basic Authentication
        auth_basic "Restricted Access";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
