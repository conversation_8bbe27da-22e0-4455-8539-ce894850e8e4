server {
    listen 80;
    listen [::]:80;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name storage.jirani.tufiked.live;
    server_tokens off;

    ssl_certificate /etc/letsencrypt/live/storage.jirani.tufiked.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/storage.jirani.tufiked.live/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # MinIO API
    location / {
        proxy_pass http://minio:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # This is necessary for MinIO's browser upload functionality
        client_max_body_size 1000m;
    }

    # Additional security headers
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection '1; mode=block';
    add_header Referrer-Policy strict-origin-when-cross-origin;
    add_header Permissions-Policy 'camera=(), microphone=(), geolocation=()';
}
