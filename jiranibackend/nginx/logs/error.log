2025/05/18 23:50:49 [notice] 1#1: using the "epoll" event method
2025/05/18 23:50:49 [notice] 1#1: nginx/1.27.5
2025/05/18 23:50:49 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/18 23:50:49 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/18 23:50:49 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/18 23:50:49 [notice] 1#1: start worker processes
2025/05/18 23:50:49 [notice] 1#1: start worker process 21
2025/05/18 23:50:49 [notice] 1#1: start worker process 22
2025/05/18 23:50:49 [notice] 1#1: start worker process 23
2025/05/18 23:50:49 [notice] 1#1: start worker process 24
2025/05/18 23:57:37 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/05/18 23:57:37 [notice] 22#22: gracefully shutting down
2025/05/18 23:57:37 [notice] 23#23: gracefully shutting down
2025/05/18 23:57:37 [notice] 22#22: exiting
2025/05/18 23:57:37 [notice] 23#23: exiting
2025/05/18 23:57:37 [notice] 21#21: gracefully shutting down
2025/05/18 23:57:37 [notice] 21#21: exiting
2025/05/18 23:57:37 [notice] 24#24: gracefully shutting down
2025/05/18 23:57:37 [notice] 23#23: exit
2025/05/18 23:57:37 [notice] 24#24: exiting
2025/05/18 23:57:37 [notice] 21#21: exit
2025/05/18 23:57:37 [notice] 24#24: exit
2025/05/18 23:57:37 [notice] 22#22: exit
2025/05/18 23:57:37 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/05/18 23:57:37 [notice] 1#1: worker process 23 exited with code 0
2025/05/18 23:57:37 [notice] 1#1: signal 29 (SIGIO) received
2025/05/18 23:57:37 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/05/18 23:57:37 [notice] 1#1: worker process 21 exited with code 0
2025/05/18 23:57:37 [notice] 1#1: signal 29 (SIGIO) received
2025/05/18 23:57:37 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/05/18 23:57:37 [notice] 1#1: worker process 22 exited with code 0
2025/05/18 23:57:37 [notice] 1#1: signal 29 (SIGIO) received
2025/05/18 23:57:37 [notice] 1#1: signal 17 (SIGCHLD) received from 24
2025/05/18 23:57:37 [notice] 1#1: worker process 24 exited with code 0
2025/05/18 23:57:37 [notice] 1#1: exit
2025/05/18 23:57:38 [notice] 1#1: using the "epoll" event method
2025/05/18 23:57:38 [notice] 1#1: nginx/1.27.5
2025/05/18 23:57:38 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/18 23:57:38 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/18 23:57:38 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/18 23:57:38 [notice] 1#1: start worker processes
2025/05/18 23:57:38 [notice] 1#1: start worker process 22
2025/05/18 23:57:38 [notice] 1#1: start worker process 23
2025/05/18 23:57:38 [notice] 1#1: start worker process 24
2025/05/18 23:57:38 [notice] 1#1: start worker process 25
2025/05/19 00:34:18 [emerg] 454#454: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:34:53 [emerg] 460#460: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:35:28 [emerg] 466#466: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:36:03 [emerg] 472#472: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:36:39 [emerg] 478#478: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:37:14 [emerg] 484#484: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:37:49 [emerg] 490#490: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:47:56 [emerg] 609#609: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:48:32 [emerg] 615#615: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:49:08 [emerg] 620#620: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:51:03 [emerg] 626#626: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:51:38 [emerg] 632#632: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:56:44 [emerg] 691#691: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:57:19 [emerg] 697#697: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:57:54 [emerg] 703#703: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 00:58:29 [emerg] 709#709: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:08:36 [emerg] 830#830: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:09:12 [emerg] 835#835: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:09:47 [emerg] 841#841: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:10:22 [emerg] 847#847: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:10:57 [emerg] 853#853: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:11:33 [emerg] 859#859: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:12:08 [emerg] 865#865: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:12:43 [emerg] 871#871: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:13:18 [emerg] 877#877: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:13:53 [emerg] 883#883: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:14:28 [emerg] 889#889: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:15:04 [emerg] 895#895: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:15:39 [emerg] 901#901: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:18:45 [emerg] 937#937: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:19:20 [emerg] 943#943: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:19:55 [emerg] 949#949: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:20:31 [emerg] 955#955: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:21:06 [emerg] 961#961: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:21:41 [emerg] 967#967: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:24:17 [emerg] 997#997: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:24:52 [emerg] 1003#1003: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:25:27 [emerg] 1009#1009: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:26:03 [emerg] 1015#1015: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:26:38 [emerg] 1021#1021: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:30:43 [emerg] 1069#1069: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 01:31:19 [emerg] 1075#1075: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 09:13:02 [notice] 1#1: using the "epoll" event method
2025/05/19 09:13:02 [notice] 1#1: nginx/1.27.5
2025/05/19 09:13:02 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/19 09:13:02 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/19 09:13:02 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/19 09:13:02 [notice] 1#1: start worker processes
2025/05/19 09:13:02 [notice] 1#1: start worker process 21
2025/05/19 09:13:02 [notice] 1#1: start worker process 22
2025/05/19 09:13:02 [notice] 1#1: start worker process 23
2025/05/19 09:13:02 [notice] 1#1: start worker process 24
2025/05/19 10:10:57 [notice] 1#1: using the "epoll" event method
2025/05/19 10:10:57 [notice] 1#1: nginx/1.27.5
2025/05/19 10:10:57 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/19 10:10:57 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/19 10:10:57 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/19 10:10:57 [notice] 1#1: start worker processes
2025/05/19 10:10:57 [notice] 1#1: start worker process 20
2025/05/19 10:10:57 [notice] 1#1: start worker process 21
2025/05/19 10:10:57 [notice] 1#1: start worker process 22
2025/05/19 10:10:57 [notice] 1#1: start worker process 23
2025/05/19 12:49:32 [emerg] 1902#1902: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 12:50:07 [emerg] 1908#1908: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 12:50:42 [emerg] 1914#1914: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 12:51:17 [emerg] 1920#1920: host not found in upstream "api" in /etc/nginx/conf.d/default.conf:13
2025/05/19 12:51:37 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/05/19 12:51:37 [notice] 21#21: gracefully shutting down
2025/05/19 12:51:37 [notice] 20#20: gracefully shutting down
2025/05/19 12:51:37 [notice] 21#21: exiting
2025/05/19 12:51:37 [notice] 20#20: exiting
2025/05/19 12:51:37 [notice] 23#23: gracefully shutting down
2025/05/19 12:51:37 [notice] 22#22: gracefully shutting down
2025/05/19 12:51:37 [notice] 23#23: exiting
2025/05/19 12:51:37 [notice] 22#22: exiting
2025/05/19 12:51:37 [notice] 20#20: exit
2025/05/19 12:51:37 [notice] 23#23: exit
2025/05/19 12:51:37 [notice] 21#21: exit
2025/05/19 12:51:37 [notice] 22#22: exit
2025/05/19 12:51:37 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/05/19 12:51:37 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/05/19 12:51:37 [notice] 1#1: worker process 23 exited with code 0
2025/05/19 12:51:37 [notice] 1#1: signal 29 (SIGIO) received
2025/05/19 12:51:37 [notice] 1#1: signal 17 (SIGCHLD) received from 20
2025/05/19 12:51:37 [notice] 1#1: worker process 20 exited with code 0
2025/05/19 12:51:37 [notice] 1#1: signal 29 (SIGIO) received
2025/05/19 12:51:37 [notice] 1#1: signal 17 (SIGCHLD) received from 22
2025/05/19 12:51:37 [notice] 1#1: worker process 21 exited with code 0
2025/05/19 12:51:37 [notice] 1#1: worker process 22 exited with code 0
2025/05/19 12:51:37 [notice] 1#1: exit
2025/05/19 12:51:45 [emerg] 1#1: host not found in upstream "prometheus" in /etc/nginx/conf.d/default.conf:76
2025/05/19 12:51:53 [emerg] 1#1: host not found in upstream "prometheus" in /etc/nginx/conf.d/default.conf:76
2025/05/19 16:47:09 [notice] 1#1: using the "epoll" event method
2025/05/19 16:47:09 [notice] 1#1: nginx/1.27.5
2025/05/19 16:47:09 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/19 16:47:09 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/19 16:47:09 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/19 16:47:09 [notice] 1#1: start worker processes
2025/05/19 16:47:09 [notice] 1#1: start worker process 20
2025/05/19 16:47:09 [notice] 1#1: start worker process 21
2025/05/19 16:47:09 [notice] 1#1: start worker process 22
2025/05/19 16:47:09 [notice] 1#1: start worker process 23
2025/05/20 04:41:21 [notice] 1#1: using the "epoll" event method
2025/05/20 04:41:21 [notice] 1#1: nginx/1.27.5
2025/05/20 04:41:21 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/20 04:41:21 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/20 04:41:21 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/20 04:41:21 [notice] 1#1: start worker processes
2025/05/20 04:41:21 [notice] 1#1: start worker process 21
2025/05/20 04:41:21 [notice] 1#1: start worker process 22
2025/05/20 04:41:21 [notice] 1#1: start worker process 23
2025/05/20 04:41:21 [notice] 1#1: start worker process 24
2025/05/20 10:58:07 [notice] 1#1: using the "epoll" event method
2025/05/20 10:58:07 [notice] 1#1: nginx/1.27.5
2025/05/20 10:58:07 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/05/20 10:58:07 [notice] 1#1: OS: Linux 6.8.0-59-generic
2025/05/20 10:58:07 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/05/20 10:58:07 [notice] 1#1: start worker processes
2025/05/20 10:58:07 [notice] 1#1: start worker process 21
2025/05/20 10:58:07 [notice] 1#1: start worker process 22
2025/05/20 10:58:07 [notice] 1#1: start worker process 23
2025/05/20 10:58:07 [notice] 1#1: start worker process 24
2025/05/20 11:07:09 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/05/20 11:07:09 [notice] 24#24: gracefully shutting down
2025/05/20 11:07:09 [notice] 24#24: exiting
2025/05/20 11:07:09 [notice] 22#22: gracefully shutting down
2025/05/20 11:07:09 [notice] 21#21: gracefully shutting down
2025/05/20 11:07:09 [notice] 23#23: gracefully shutting down
2025/05/20 11:07:09 [notice] 21#21: exiting
2025/05/20 11:07:09 [notice] 22#22: exiting
2025/05/20 11:07:09 [notice] 23#23: exiting
2025/05/20 11:07:09 [notice] 24#24: exit
2025/05/20 11:07:09 [notice] 23#23: exit
2025/05/20 11:07:09 [notice] 21#21: exit
2025/05/20 11:07:09 [notice] 22#22: exit
2025/05/20 11:07:09 [notice] 1#1: signal 17 (SIGCHLD) received from 21
2025/05/20 11:07:09 [notice] 1#1: worker process 21 exited with code 0
2025/05/20 11:07:09 [notice] 1#1: worker process 22 exited with code 0
2025/05/20 11:07:09 [notice] 1#1: worker process 23 exited with code 0
2025/05/20 11:07:09 [notice] 1#1: worker process 24 exited with code 0
2025/05/20 11:07:09 [notice] 1#1: exit
2025/06/18 16:36:10 [notice] 1#1: using the "epoll" event method
2025/06/18 16:36:10 [notice] 1#1: nginx/1.27.5
2025/06/18 16:36:10 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/18 16:36:10 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/18 16:36:10 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/18 16:36:10 [notice] 1#1: start worker processes
2025/06/18 16:36:10 [notice] 1#1: start worker process 21
2025/06/18 16:36:10 [notice] 1#1: start worker process 22
2025/06/18 16:36:10 [notice] 1#1: start worker process 23
2025/06/18 16:36:10 [notice] 1#1: start worker process 24
2025/06/19 08:23:49 [notice] 1#1: using the "epoll" event method
2025/06/19 08:23:49 [notice] 1#1: nginx/1.27.5
2025/06/19 08:23:49 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/19 08:23:49 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/19 08:23:49 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/19 08:23:49 [notice] 1#1: start worker processes
2025/06/19 08:23:49 [notice] 1#1: start worker process 20
2025/06/19 08:23:49 [notice] 1#1: start worker process 21
2025/06/19 08:23:49 [notice] 1#1: start worker process 22
2025/06/19 08:23:49 [notice] 1#1: start worker process 23
2025/06/20 08:37:57 [notice] 1#1: using the "epoll" event method
2025/06/20 08:37:57 [notice] 1#1: nginx/1.27.5
2025/06/20 08:37:57 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/20 08:37:57 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/20 08:37:57 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/20 08:37:57 [notice] 1#1: start worker processes
2025/06/20 08:37:57 [notice] 1#1: start worker process 21
2025/06/20 08:37:57 [notice] 1#1: start worker process 22
2025/06/20 08:37:57 [notice] 1#1: start worker process 23
2025/06/20 08:37:57 [notice] 1#1: start worker process 24
2025/06/20 10:12:38 [notice] 1#1: using the "epoll" event method
2025/06/20 10:12:38 [notice] 1#1: nginx/1.27.5
2025/06/20 10:12:38 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/20 10:12:38 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/20 10:12:38 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/20 10:12:38 [notice] 1#1: start worker processes
2025/06/20 10:12:38 [notice] 1#1: start worker process 21
2025/06/20 10:12:38 [notice] 1#1: start worker process 22
2025/06/20 10:12:38 [notice] 1#1: start worker process 23
2025/06/20 10:12:38 [notice] 1#1: start worker process 24
2025/06/20 15:10:17 [notice] 1#1: using the "epoll" event method
2025/06/20 15:10:17 [notice] 1#1: nginx/1.27.5
2025/06/20 15:10:17 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/20 15:10:17 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/20 15:10:17 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/20 15:10:17 [notice] 1#1: start worker processes
2025/06/20 15:10:17 [notice] 1#1: start worker process 21
2025/06/20 15:10:17 [notice] 1#1: start worker process 22
2025/06/20 15:10:17 [notice] 1#1: start worker process 23
2025/06/20 15:10:17 [notice] 1#1: start worker process 24
2025/06/20 17:37:55 [notice] 1#1: using the "epoll" event method
2025/06/20 17:37:55 [notice] 1#1: nginx/1.27.5
2025/06/20 17:37:55 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/20 17:37:55 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/20 17:37:55 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/20 17:37:55 [notice] 1#1: start worker processes
2025/06/20 17:37:55 [notice] 1#1: start worker process 21
2025/06/20 17:37:55 [notice] 1#1: start worker process 22
2025/06/20 17:37:55 [notice] 1#1: start worker process 23
2025/06/20 17:37:55 [notice] 1#1: start worker process 24
2025/06/20 19:44:54 [notice] 1#1: using the "epoll" event method
2025/06/20 19:44:54 [notice] 1#1: nginx/1.27.5
2025/06/20 19:44:54 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/20 19:44:54 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/20 19:44:54 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/20 19:44:54 [notice] 1#1: start worker processes
2025/06/20 19:44:54 [notice] 1#1: start worker process 22
2025/06/20 19:44:54 [notice] 1#1: start worker process 23
2025/06/20 19:44:54 [notice] 1#1: start worker process 24
2025/06/20 19:44:54 [notice] 1#1: start worker process 25
2025/06/21 06:17:24 [notice] 1#1: using the "epoll" event method
2025/06/21 06:17:24 [notice] 1#1: nginx/1.27.5
2025/06/21 06:17:24 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/21 06:17:24 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/21 06:17:24 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/21 06:17:24 [notice] 1#1: start worker processes
2025/06/21 06:17:24 [notice] 1#1: start worker process 21
2025/06/21 06:17:24 [notice] 1#1: start worker process 22
2025/06/21 06:17:24 [notice] 1#1: start worker process 23
2025/06/21 06:17:24 [notice] 1#1: start worker process 24
2025/06/21 11:23:49 [notice] 1#1: using the "epoll" event method
2025/06/21 11:23:49 [notice] 1#1: nginx/1.27.5
2025/06/21 11:23:49 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/21 11:23:49 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/21 11:23:49 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/21 11:23:49 [notice] 1#1: start worker processes
2025/06/21 11:23:49 [notice] 1#1: start worker process 21
2025/06/21 11:23:49 [notice] 1#1: start worker process 22
2025/06/21 11:23:49 [notice] 1#1: start worker process 23
2025/06/21 11:23:49 [notice] 1#1: start worker process 24
2025/06/22 12:07:19 [notice] 1#1: using the "epoll" event method
2025/06/22 12:07:19 [notice] 1#1: nginx/1.27.5
2025/06/22 12:07:19 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/22 12:07:19 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/22 12:07:19 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/22 12:07:19 [notice] 1#1: start worker processes
2025/06/22 12:07:19 [notice] 1#1: start worker process 20
2025/06/22 12:07:19 [notice] 1#1: start worker process 21
2025/06/22 12:07:19 [notice] 1#1: start worker process 22
2025/06/22 12:07:19 [notice] 1#1: start worker process 23
2025/06/22 13:34:46 [notice] 1#1: using the "epoll" event method
2025/06/22 13:34:46 [notice] 1#1: nginx/1.27.5
2025/06/22 13:34:46 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/22 13:34:46 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/22 13:34:46 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/22 13:34:46 [notice] 1#1: start worker processes
2025/06/22 13:34:46 [notice] 1#1: start worker process 21
2025/06/22 13:34:46 [notice] 1#1: start worker process 22
2025/06/22 13:34:46 [notice] 1#1: start worker process 23
2025/06/22 13:34:46 [notice] 1#1: start worker process 24
2025/06/22 18:58:29 [notice] 1#1: using the "epoll" event method
2025/06/22 18:58:29 [notice] 1#1: nginx/1.27.5
2025/06/22 18:58:29 [notice] 1#1: built by gcc 14.2.0 (Alpine 14.2.0) 
2025/06/22 18:58:29 [notice] 1#1: OS: Linux 6.8.0-60-generic
2025/06/22 18:58:29 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/22 18:58:29 [notice] 1#1: start worker processes
2025/06/22 18:58:29 [notice] 1#1: start worker process 21
2025/06/22 18:58:29 [notice] 1#1: start worker process 22
2025/06/22 18:58:29 [notice] 1#1: start worker process 23
2025/06/22 18:58:29 [notice] 1#1: start worker process 24
2025/06/22 20:30:05 [notice] 1#1: signal 3 (SIGQUIT) received, shutting down
2025/06/22 20:30:05 [notice] 22#22: gracefully shutting down
2025/06/22 20:30:05 [notice] 24#24: gracefully shutting down
2025/06/22 20:30:05 [notice] 23#23: gracefully shutting down
2025/06/22 20:30:05 [notice] 21#21: gracefully shutting down
2025/06/22 20:30:05 [notice] 22#22: exiting
2025/06/22 20:30:05 [notice] 24#24: exiting
2025/06/22 20:30:05 [notice] 21#21: exiting
2025/06/22 20:30:05 [notice] 23#23: exiting
2025/06/22 20:30:05 [notice] 24#24: exit
2025/06/22 20:30:05 [notice] 21#21: exit
2025/06/22 20:30:05 [notice] 23#23: exit
2025/06/22 20:30:05 [notice] 22#22: exit
2025/06/22 20:30:05 [notice] 1#1: signal 17 (SIGCHLD) received from 23
2025/06/22 20:30:05 [notice] 1#1: worker process 21 exited with code 0
2025/06/22 20:30:05 [notice] 1#1: worker process 22 exited with code 0
2025/06/22 20:30:05 [notice] 1#1: worker process 23 exited with code 0
2025/06/22 20:30:05 [notice] 1#1: worker process 24 exited with code 0
2025/06/22 20:30:05 [notice] 1#1: exit
