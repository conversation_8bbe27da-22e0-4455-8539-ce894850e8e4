# Main API Server
server {
    listen 80;
    server_name jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # API Service
    location / {
        proxy_pass http://jirani-api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Ensure authentication headers are properly forwarded
        proxy_set_header Authorization $http_authorization;
        proxy_pass_header Authorization;

        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://jirani-api:8080/api/v1/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Ping endpoint for health checks
    location /ping {
        proxy_pass http://jirani-api:8080/ping;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# Prometheus Metrics
server {
    listen 80;
    server_name metrics.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name metrics.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    # Basic auth for security
    auth_basic "Restricted Access";
    auth_basic_user_file /etc/nginx/auth/.htpasswd;

    location / {
        proxy_pass http://jirani-prometheus:9090;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Grafana Dashboard
server {
    listen 80;
    server_name dashboard.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name dashboard.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    location / {
        proxy_pass http://jirani-grafana:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Kibana Logs
server {
    listen 80;
    server_name logs.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name logs.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    # Basic auth for security
    auth_basic "Restricted Access";
    auth_basic_user_file /etc/nginx/auth/.htpasswd;

    location / {
        proxy_pass http://jirani-kibana:5601;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# MinIO Storage API
server {
    listen 80;
    server_name storage.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name storage.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    location / {
        proxy_pass http://jirani-minio:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# MinIO Console
server {
    listen 80;
    server_name storage-console.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name storage-console.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    location / {
        proxy_pass http://jirani-minio:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Elasticsearch
server {
    listen 80;
    server_name es.jirani.tufiked.live;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name es.jirani.tufiked.live;

    ssl_certificate /etc/nginx/ssl/jirani/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/jirani/privkey.pem;

    # Basic auth for security
    auth_basic "Restricted Access";
    auth_basic_user_file /etc/nginx/auth/.htpasswd;

    location / {
        proxy_pass http://jirani-elasticsearch:9200;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
