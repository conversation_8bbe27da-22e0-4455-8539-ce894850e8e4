#!/bin/bash

# This script obtains SSL certificates using certbot

# Email for Let's Encrypt notifications
EMAIL=${1:-<EMAIL>}

# Domain names
DOMAINS=(
    "jirani.tufiked.live"
    "api.jirani.tufiked.live"
    "metrics.jirani.tufiked.live"
    "dashboard.jirani.tufiked.live"
    "logs.jirani.tufiked.live"
    "storage.jirani.tufiked.live"
    "storage-console.jirani.tufiked.live"
    "es.jirani.tufiked.live"
)

# Create the SSL directory if it doesn't exist
mkdir -p /etc/nginx/ssl/jirani

# Install certbot if not already installed
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    apt-get update
    apt-get install -y certbot python3-certbot-nginx
fi

# Build the domain parameters for certbot
DOMAIN_PARAMS=""
for domain in "${DOMAINS[@]}"; do
    DOMAIN_PARAMS="$DOMAIN_PARAMS -d $domain"
done

# Obtain the certificate
echo "Obtaining SSL certificate for domains: ${DOMAINS[*]}"
certbot certonly --nginx --agree-tos --email "$EMAIL" --non-interactive $DOMAIN_PARAMS

# Copy the certificates to the Nginx SSL directory
echo "Copying certificates to Nginx SSL directory..."
cp /etc/letsencrypt/live/${DOMAINS[0]}/fullchain.pem /etc/nginx/ssl/jirani/
cp /etc/letsencrypt/live/${DOMAINS[0]}/privkey.pem /etc/nginx/ssl/jirani/

# Set proper permissions
chmod 644 /etc/nginx/ssl/jirani/fullchain.pem
chmod 600 /etc/nginx/ssl/jirani/privkey.pem

echo "SSL certificates obtained and configured successfully!"
