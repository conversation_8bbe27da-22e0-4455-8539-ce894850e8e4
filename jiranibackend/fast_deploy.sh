#!/bin/bash
# Fast BodaBoda Development Script
# For rapid iteration during development

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SERVER="instance-20250518-210238"
ZONE="us-central1-c"
PROJECT="gen-lang-client-0728840371"
REMOTE_DIR="/home/<USER>/jirani-backend"
DOCKER_REPO="kimathinrian"
APP_NAME="jirani-api"

print_colored() {
    echo -e "${1}$2${NC}"
}

print_step() {
    print_colored "$BLUE" "⚡ $1"
}

print_success() {
    print_colored "$GREEN" "✅ $1"
}

print_error() {
    print_colored "$RED" "❌ $1"
}

# Quick build and deploy for development
quick_deploy() {
    local start_time=$(date +%s)
    
    print_step "Starting fast deployment..."
    
    # Quick build
    print_step "Building application..."
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $APP_NAME ./cmd/api/main.go
    
    # Quick Docker build
    print_step "Building Docker image..."
    docker build -t $DOCKER_REPO/$APP_NAME:latest . -q
    
    # Push image
    print_step "Pushing image..."
    docker push $DOCKER_REPO/$APP_NAME:latest > /dev/null
    
    # Deploy to server
    print_step "Deploying to server..."
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        cd $REMOTE_DIR
        docker-compose -f docker-compose.prod.yml pull api > /dev/null 2>&1
        docker-compose -f docker-compose.prod.yml up -d api
    " > /dev/null 2>&1
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_success "Fast deployment completed in ${duration}s"
    print_success "API available at: https://api.jirani.tufiked.live"
}

# Test BodaBoda endpoints quickly
test_endpoints() {
    print_step "Testing BodaBoda endpoints..."
    
    # Test drivers endpoint
    local response=$(gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        curl -s -w '%{http_code}' https://api.jirani.tufiked.live/api/boda/drivers?latitude=-1.2921&longitude=36.8219
    " 2>/dev/null)
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "401" ] || [ "$http_code" = "200" ]; then
        print_success "Drivers endpoint: OK ($http_code)"
    else
        print_error "Drivers endpoint: FAIL ($http_code)"
    fi
    
    # Test WebSocket endpoint
    local ws_response=$(gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        curl -s -w '%{http_code}' https://api.jirani.tufiked.live/ws
    " 2>/dev/null)
    
    local ws_code="${ws_response: -3}"
    
    if [ "$ws_code" = "400" ] || [ "$ws_code" = "426" ]; then
        print_success "WebSocket endpoint: OK ($ws_code)"
    else
        print_error "WebSocket endpoint: FAIL ($ws_code)"
    fi
}

# Show container logs
show_logs() {
    print_step "Showing API logs..."
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        docker logs jirani-api --tail 20
    "
}

# Restart just the API service
restart_api() {
    print_step "Restarting API service..."
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        cd $REMOTE_DIR
        docker-compose -f docker-compose.prod.yml restart api
    "
    print_success "API service restarted"
}

# Show service status
show_status() {
    print_step "Checking service status..."
    gcloud compute ssh --zone $ZONE $SERVER --project $PROJECT -- "
        cd $REMOTE_DIR
        docker-compose -f docker-compose.prod.yml ps
    "
}

# Main menu
show_menu() {
    echo ""
    echo "🚀 Fast BodaBoda Development Menu"
    echo "================================="
    echo "1. Quick Deploy (build + push + deploy)"
    echo "2. Test Endpoints"
    echo "3. Show Logs"
    echo "4. Restart API"
    echo "5. Show Status"
    echo "6. Exit"
    echo ""
}

# Interactive mode
interactive_mode() {
    while true; do
        show_menu
        read -p "Choose an option (1-6): " choice
        
        case $choice in
            1)
                quick_deploy
                ;;
            2)
                test_endpoints
                ;;
            3)
                show_logs
                ;;
            4)
                restart_api
                ;;
            5)
                show_status
                ;;
            6)
                print_success "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please choose 1-6."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Parse command line arguments
case "${1:-interactive}" in
    "deploy")
        quick_deploy
        ;;
    "test")
        test_endpoints
        ;;
    "logs")
        show_logs
        ;;
    "restart")
        restart_api
        ;;
    "status")
        show_status
        ;;
    "interactive"|"")
        interactive_mode
        ;;
    *)
        echo "Usage: $0 [deploy|test|logs|restart|status|interactive]"
        echo ""
        echo "Commands:"
        echo "  deploy      - Quick build and deploy"
        echo "  test        - Test BodaBoda endpoints"
        echo "  logs        - Show API logs"
        echo "  restart     - Restart API service"
        echo "  status      - Show service status"
        echo "  interactive - Interactive menu (default)"
        exit 1
        ;;
esac
