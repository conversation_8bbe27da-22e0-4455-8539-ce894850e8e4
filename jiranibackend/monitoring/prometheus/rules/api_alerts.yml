groups:
  - name: api_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(jirani_http_errors_total[5m])) / sum(rate(jirani_http_requests_total[5m])) > 0.05
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for the last 5 minutes"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, sum(rate(jirani_http_request_duration_seconds_bucket[5m])) by (le)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile of response time is above 1 second for the last 5 minutes"

      - alert: HighDatabaseQueryTime
        expr: histogram_quantile(0.95, sum(rate(jirani_db_query_duration_seconds_bucket[5m])) by (le)) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database query time detected"
          description: "95th percentile of database query time is above 0.5 seconds for the last 5 minutes"

      - alert: HighDatabaseErrorRate
        expr: sum(rate(jirani_db_query_errors_total[5m])) / sum(rate(jirani_db_queries_total[5m])) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database error rate detected"
          description: "Database error rate is above 5% for the last 5 minutes"

      - alert: HighMemoryUsage
        expr: jirani_system_memory_usage_bytes{type="heap_alloc"} / 1024 / 1024 > 500
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 500MB for the last 5 minutes"

      - alert: HighGoroutineCount
        expr: jirani_system_goroutines_count > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High goroutine count detected"
          description: "Goroutine count is above 1000 for the last 5 minutes"
