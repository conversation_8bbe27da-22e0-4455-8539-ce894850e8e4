groups:
  - name: business_alerts
    rules:
      - alert: HighOrderCancellationRate
        expr: sum(rate(jirani_orders_cancellations_total[1h])) / sum(rate(jirani_orders_total[1h])) > 0.1
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "High order cancellation rate detected"
          description: "Order cancellation rate is above 10% for the last hour"

      - alert: LowDriverAvailability
        expr: jirani_drivers_active / jirani_drivers_total{status="active"} < 0.5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low driver availability detected"
          description: "Less than 50% of active drivers are currently available"

      - alert: HighPaymentFailureRate
        expr: sum(rate(jirani_payments_failures_total[1h])) / sum(rate(jirani_payments_total[1h])) > 0.05
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "High payment failure rate detected"
          description: "Payment failure rate is above 5% for the last hour"

      - alert: LongOrderDeliveryTime
        expr: histogram_quantile(0.95, sum(rate(jirani_orders_delivery_time_seconds_bucket[1h])) by (le)) > 3600
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Long order delivery time detected"
          description: "95th percentile of order delivery time is above 1 hour for the last hour"

      - alert: LowRestaurantRating
        expr: avg(jirani_restaurants_ratings) < 3.5
        for: 1d
        labels:
          severity: warning
        annotations:
          summary: "Low restaurant rating detected"
          description: "Average restaurant rating is below 3.5 for the last day"
