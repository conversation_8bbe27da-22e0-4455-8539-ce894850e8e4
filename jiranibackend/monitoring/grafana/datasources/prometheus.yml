apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "30s"
      httpMethod: "POST"
      manageAlerts: true
      prometheusType: "Prometheus"
      prometheusVersion: "2.40.0"
      exemplarTraceIdDestinations:
        - name: traceID
          datasourceUid: tempo

  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "[jirani-api-]YYYY.MM.DD"
    isDefault: false
    editable: true
    jsonData:
      timeField: "timestamp"
      esVersion: "8.0.0"
      maxConcurrentShardRequests: 5
      logMessageField: "message"
      logLevelField: "level"
