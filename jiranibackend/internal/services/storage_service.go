package services

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jirani/backend/internal/config"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// StorageService handles file storage operations
type StorageService struct {
	client     *minio.Client
	bucketName string
	config     *config.Config
}

// NewStorageService creates a new storage service
func NewStorageService() (*StorageService, error) {
	log.Printf("Initializing storage service")

	cfg, err := config.LoadConfig()
	if err != nil {
		log.Printf("Error loading config: %v", err)
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	log.Printf("Config loaded successfully")

	// Log MinIO configuration (without credentials)
	log.Printf("MinIO endpoint: %s, bucket: %s", cfg.MinioEndpoint, cfg.MinioBucket)

	// Initialize MinIO client
	log.Printf("Creating MinIO client")
	// In production, we use HTTPS for external URLs but not for internal MinIO connections
	// MinIO in Docker is not configured with TLS
	useSecure := false // Always use HTTP for internal MinIO connections
	log.Printf("Environment: %s, Using secure connection for MinIO: %v", cfg.Environment, useSecure)

	client, err := minio.New(cfg.MinioEndpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.MinioAccessKey, cfg.MinioSecretKey, ""),
		Secure: useSecure, // Always use HTTP for internal MinIO connections
	})
	if err != nil {
		log.Printf("Error creating MinIO client: %v", err)
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}
	log.Printf("MinIO client created successfully")

	// Create bucket if it doesn't exist
	ctx := context.Background()
	log.Printf("Checking if bucket '%s' exists", cfg.MinioBucket)
	exists, err := client.BucketExists(ctx, cfg.MinioBucket)
	if err != nil {
		log.Printf("Error checking if bucket exists: %v", err)
		return nil, fmt.Errorf("failed to check if bucket exists: %w", err)
	}

	if !exists {
		log.Printf("Bucket '%s' does not exist, creating it", cfg.MinioBucket)
		err = client.MakeBucket(ctx, cfg.MinioBucket, minio.MakeBucketOptions{})
		if err != nil {
			log.Printf("Error creating bucket: %v", err)
			return nil, fmt.Errorf("failed to create bucket: %w", err)
		}
		log.Printf("Bucket created successfully")

		// Set bucket policy to allow public read access
		log.Printf("Setting bucket policy for public read access")
		policy := `{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Effect": "Allow",
					"Principal": {"AWS": ["*"]},
					"Action": ["s3:GetObject"],
					"Resource": ["arn:aws:s3:::` + cfg.MinioBucket + `/*"]
				}
			]
		}`

		err = client.SetBucketPolicy(ctx, cfg.MinioBucket, policy)
		if err != nil {
			log.Printf("Error setting bucket policy: %v", err)
			return nil, fmt.Errorf("failed to set bucket policy: %w", err)
		}
		log.Printf("Bucket policy set successfully")
	} else {
		log.Printf("Bucket '%s' already exists", cfg.MinioBucket)
	}

	log.Printf("Storage service initialized successfully")
	return &StorageService{
		client:     client,
		bucketName: cfg.MinioBucket,
		config:     cfg,
	}, nil
}

// UploadFile uploads a file to MinIO
func (s *StorageService) UploadFile(fileData []byte, fileName string) (string, error) {
	// Log the upload attempt
	log.Printf("Attempting to upload file: %s, size: %d bytes", fileName, len(fileData))

	// Generate a unique file name
	ext := filepath.Ext(fileName)
	uniqueFileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	log.Printf("Generated unique filename: %s", uniqueFileName)

	// Log MinIO connection details (without credentials)
	log.Printf("MinIO endpoint: %s, bucket: %s", s.config.MinioEndpoint, s.bucketName)

	// Upload file
	ctx := context.Background()
	contentType := getContentType(ext)
	log.Printf("Uploading file with content type: %s", contentType)

	_, err := s.client.PutObject(
		ctx,
		s.bucketName,
		uniqueFileName,
		bytes.NewReader(fileData),
		int64(len(fileData)),
		minio.PutObjectOptions{
			ContentType: contentType,
		},
	)
	if err != nil {
		log.Printf("Error uploading file to MinIO: %v", err)
		return "", fmt.Errorf("failed to upload file: %w", err)
	}
	log.Printf("File uploaded successfully to MinIO")

	// Generate URL
	// For local development, we need to use the host machine's address
	endpoint := s.config.MinioEndpoint
	protocol := "http"

	// In production, use the storage subdomain and HTTPS
	if s.config.Environment == "production" {
		log.Printf("Production environment detected, using storage.jirani.tufiked.live")
		endpoint = "storage.jirani.tufiked.live"
		protocol = "https"
	} else if strings.Contains(endpoint, "minio:9000") {
		// In development with Docker, replace container name with host address for external access
		log.Printf("Replacing container name in endpoint: %s -> storage.localhost", endpoint)
		endpoint = "storage.localhost"
	}

	url := fmt.Sprintf("%s://%s/%s/%s", protocol, endpoint, s.bucketName, uniqueFileName)
	log.Printf("Generated URL for uploaded file: %s", url)
	return url, nil
}

// DeleteFile deletes a file from MinIO
func (s *StorageService) DeleteFile(fileURL string) error {
	// Extract object name from URL
	parts := strings.Split(fileURL, "/")
	objectName := parts[len(parts)-1]

	// Delete file
	ctx := context.Background()
	err := s.client.RemoveObject(ctx, s.bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}

// GetFile gets a file from MinIO
func (s *StorageService) GetFile(objectName string) ([]byte, error) {
	ctx := context.Background()
	obj, err := s.client.GetObject(ctx, s.bucketName, objectName, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	defer obj.Close()

	data, err := io.ReadAll(obj)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	return data, nil
}

// GetPresignedURL generates a presigned URL for a file
func (s *StorageService) GetPresignedURL(objectName string, expiry time.Duration) (string, error) {
	ctx := context.Background()
	presignedURL, err := s.client.PresignedGetObject(ctx, s.bucketName, objectName, expiry, nil)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return presignedURL.String(), nil
}

// Helper function to get content type based on file extension
func getContentType(ext string) string {
	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".doc", ".docx":
		return "application/msword"
	case ".xls", ".xlsx":
		return "application/vnd.ms-excel"
	default:
		return "application/octet-stream"
	}
}
