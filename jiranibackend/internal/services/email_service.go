package services

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"path/filepath"
	"runtime"

	"github.com/jirani/backend/internal/config"
)

// EmailService handles sending emails
type EmailService struct {
	config *config.Config
}

// NewEmailService creates a new email service
func NewEmailService() (*EmailService, error) {
	cfg, err := config.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	return &EmailService{
		config: cfg,
	}, nil
}

// EmailData contains data for email templates
type EmailData struct {
	Name          string
	Email         string
	Subject       string
	ResetToken    string
	ResetLink     string
	VerifyLink    string
	WelcomeText   string
	ActionText    string
	ActionLink    string
	SupportEmail  string
	CompanyName   string
	CompanyLogo   string
	PrimaryColor  string
	SecondaryColor string
	Year          string
}

// SendWelcomeEmail sends a welcome email to a new user
func (s *EmailService) SendWelcomeEmail(name, email string) error {
	subject := "Welcome to Jirani - Your Neighborhood Companion"

	data := EmailData{
		Name:          name,
		Email:         email,
		Subject:       subject,
		WelcomeText:   "Thank you for joining <PERSON>rani! We're excited to have you as part of our community.",
		ActionText:    "Get Started",
		ActionLink:    "https://jirani.tufiked.live/app",
		SupportEmail:  "<EMAIL>",
		CompanyName:   "Jirani",
		CompanyLogo:   "https://jirani.tufiked.live/logo.png",
		PrimaryColor:  "#FF5722",
		SecondaryColor: "#4CAF50",
		Year:          "2025",
	}

	return s.sendEmail(email, subject, "welcome", data)
}

// SendPasswordResetEmail sends a password reset email
func (s *EmailService) SendPasswordResetEmail(name, email, token string) error {
	subject := "Reset Your Jirani Password"
	resetLink := fmt.Sprintf("https://jirani.tufiked.live/reset-password?token=%s", token)

	data := EmailData{
		Name:          name,
		Email:         email,
		Subject:       subject,
		ResetToken:    token,
		ResetLink:     resetLink,
		ActionText:    "Reset Password",
		ActionLink:    resetLink,
		SupportEmail:  "<EMAIL>",
		CompanyName:   "Jirani",
		CompanyLogo:   "https://jirani.tufiked.live/logo.png",
		PrimaryColor:  "#FF5722",
		SecondaryColor: "#4CAF50",
		Year:          "2025",
	}

	return s.sendEmail(email, subject, "password_reset", data)
}

// SendVerificationEmail sends an email verification email
func (s *EmailService) SendVerificationEmail(name, email, token string) error {
	subject := "Verify Your Jirani Email Address"
	verifyLink := fmt.Sprintf("https://jirani.tufiked.live/verify-email?token=%s", token)

	data := EmailData{
		Name:          name,
		Email:         email,
		Subject:       subject,
		VerifyLink:    verifyLink,
		ActionText:    "Verify Email",
		ActionLink:    verifyLink,
		SupportEmail:  "<EMAIL>",
		CompanyName:   "Jirani",
		CompanyLogo:   "https://jirani.tufiked.live/logo.png",
		PrimaryColor:  "#FF5722",
		SecondaryColor: "#4CAF50",
		Year:          "2025",
	}

	return s.sendEmail(email, subject, "email_verification", data)
}

// sendEmail sends an email using the specified template
func (s *EmailService) sendEmail(to, subject, templateName string, data EmailData) error {
	// Get the template path
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	templatePath := filepath.Join(basepath, "..", "templates", "emails", templateName+".html")

	// Parse the template
	t, err := template.ParseFiles(templatePath)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Execute the template
	var body bytes.Buffer
	if err := t.Execute(&body, data); err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Log email credentials for debugging (without showing the full password)
	fmt.Printf("Email setup: Host=%s, Port=%s, From=%s, Password=****\n", s.config.EmailHost, s.config.EmailPort, s.config.EmailFrom)

	// Set up authentication information
	// Log the credentials being used (without showing the full password)
	fmt.Printf("Using credentials: Email=%s, Password length=%d\n",
		s.config.EmailFrom,
		len(s.config.EmailPassword))

	auth := smtp.PlainAuth("", s.config.EmailFrom, s.config.EmailPassword, s.config.EmailHost)

	// Set up email headers
	headers := make(map[string]string)
	headers["From"] = fmt.Sprintf("Jirani <%s>", s.config.EmailFrom)
	headers["To"] = to
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/html; charset=UTF-8"

	// Construct message
	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body.String()

	// SMTP server address with port
	smtpAddr := fmt.Sprintf("%s:%s", s.config.EmailHost, s.config.EmailPort)

	// Log the SMTP server address
	fmt.Printf("Sending email via SMTP server: %s\n", smtpAddr)

	// Send email
	err = smtp.SendMail(smtpAddr, auth, s.config.EmailFrom, []string{to}, []byte(message))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}
