package services

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jirani/backend/internal/middleware"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/repositories"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService handles authentication business logic
type AuthService struct {
	repo        *repositories.AuthRepository
	emailService *EmailService
}

// NewAuthService creates a new authentication service
func NewAuthService() *AuthService {
	emailService, err := NewEmailService()
	if err != nil {
		// Log the error but continue without email service
		fmt.Printf("Failed to create email service: %v\n", err)
	}

	return &AuthService{
		repo:        repositories.NewAuthRepository(),
		emailService: emailService,
	}
}

// RegisterUser registers a new user
func (s *AuthService) RegisterUser(email, password, fullName, phoneNumber string) (*models.User, string, error) {
	// Check if user already exists
	existingUser, err := s.repo.FindUserByEmail(email)
	if err == nil && existingUser != nil {
		return nil, "", errors.New("user with this email already exists")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Log the error for debugging
		fmt.Printf("Error checking existing user: %v\n", err)
		return nil, "", fmt.Errorf("database error: %w", err)
	}
	// If err is gorm.ErrRecordNotFound, this is the expected case - proceed with registration

	// Check if phone number already exists
	existingUser, err = s.repo.FindUserByPhoneNumber(phoneNumber)
	if err == nil && existingUser != nil {
		return nil, "", errors.New("user with this phone number already exists")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Log the error for debugging
		fmt.Printf("Error checking existing phone number: %v\n", err)
		return nil, "", fmt.Errorf("database error: %w", err)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, "", fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := models.User{
		Email:        email,
		PasswordHash: string(hashedPassword),
		FullName:     fullName,
		PhoneNumber:  phoneNumber,
		IsActive:     true,
	}

	if err := s.repo.CreateUser(&user); err != nil {
		return nil, "", fmt.Errorf("failed to create user: %w", err)
	}

	// Assign default role
	role, err := s.repo.FindRoleByName("user")
	if err != nil {
		// Create the role if it doesn't exist
		role = &models.Role{
			Name:        "user",
			Description: "Regular user",
		}
		if err := s.repo.CreateRole(role); err != nil {
			return nil, "", fmt.Errorf("failed to create user role: %w", err)
		}
	}

	// Assign role to user
	if err := s.repo.AssignRoleToUser(user.ID, role.ID); err != nil {
		return nil, "", fmt.Errorf("failed to assign role to user: %w", err)
	}

	// Generate token
	token, err := middleware.GenerateToken(user, []string{"user"})
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate token: %w", err)
	}

	// Send welcome email
	if s.emailService != nil {
		go func() {
			if err := s.emailService.SendWelcomeEmail(user.FullName, user.Email); err != nil {
				fmt.Printf("Failed to send welcome email: %v\n", err)
			} else {
				fmt.Printf("Welcome email sent to %s\n", user.Email)
			}
		}()
	}

	return &user, token, nil
}

// LoginUser authenticates a user and returns a JWT token
func (s *AuthService) LoginUser(email, password string) (*models.User, string, error) {
	// Find user
	user, err := s.repo.FindUserByEmail(email)
	if err != nil {
		return nil, "", errors.New("invalid email or password")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, "", errors.New("your account is inactive")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, "", errors.New("invalid email or password")
	}

	// Get user roles
	userRoles, err := s.repo.GetUserRoles(user)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get user roles: %w", err)
	}

	// Extract role names
	roleNames := make([]string, len(userRoles))
	for i, role := range userRoles {
		roleNames[i] = role.Name
	}

	// Generate token
	token, err := middleware.GenerateToken(*user, roleNames)
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate token: %w", err)
	}

	return user, token, nil
}

// RefreshToken refreshes a user's JWT token
func (s *AuthService) RefreshToken(userID string) (string, error) {
	// Find user
	user, err := s.repo.FindUserByID(userID)
	if err != nil {
		return "", errors.New("user not found")
	}

	// Get user roles
	userRoles, err := s.repo.GetUserRoles(user)
	if err != nil {
		return "", fmt.Errorf("failed to get user roles: %w", err)
	}

	// Extract role names
	roleNames := make([]string, len(userRoles))
	for i, role := range userRoles {
		roleNames[i] = role.Name
	}

	// Generate token
	token, err := middleware.GenerateToken(*user, roleNames)
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return token, nil
}

// GetUserByID retrieves a user by ID
func (s *AuthService) GetUserByID(userID string) (*models.User, error) {
	return s.repo.FindUserByID(userID)
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(userID, currentPassword, newPassword string) error {
	// Find user
	user, err := s.repo.FindUserByID(userID)
	if err != nil {
		return errors.New("user not found")
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(currentPassword)); err != nil {
		return errors.New("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password
	user.PasswordHash = string(hashedPassword)
	if err := s.repo.UpdateUser(user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// RequestPasswordReset initiates a password reset process
func (s *AuthService) RequestPasswordReset(email string) error {
	// Find user
	user, err := s.repo.FindUserByEmail(email)
	if err != nil {
		// Don't reveal that the email doesn't exist
		return nil
	}

	// Generate a reset token (in a real application, this would be a secure random token)
	// For demonstration purposes, we'll use a simple token
	resetToken := fmt.Sprintf("reset-%s-%d", user.ID, time.Now().Unix())

	// In a real application, you would store this token in the database with an expiration time
	// For now, we'll just log it
	fmt.Printf("Password reset token for user ID %s: %s\n", user.ID, resetToken)

	// Send password reset email
	if s.emailService != nil {
		go func() {
			if err := s.emailService.SendPasswordResetEmail(user.FullName, user.Email, resetToken); err != nil {
				fmt.Printf("Failed to send password reset email: %v\n", err)
			} else {
				fmt.Printf("Password reset email sent to %s\n", user.Email)
			}
		}()
	}

	return nil
}

// ResetPassword resets a user's password using a reset token
func (s *AuthService) ResetPassword(token, newPassword string) error {
	// Log the token for debugging
	fmt.Printf("Password reset with token: %s\n", token)

	// Parse the token to extract the user ID
	// Format: reset-{user_id}-{timestamp}
	// The user ID may contain hyphens, so we need to handle that case
	if !strings.HasPrefix(token, "reset-") {
		return errors.New("invalid reset token format")
	}

	// Remove the "reset-" prefix
	tokenWithoutPrefix := strings.TrimPrefix(token, "reset-")

	// Find the last hyphen, which separates the user ID from the timestamp
	lastHyphenIndex := strings.LastIndex(tokenWithoutPrefix, "-")
	if lastHyphenIndex == -1 {
		return errors.New("invalid reset token format")
	}

	// Extract user ID
	userID := tokenWithoutPrefix[:lastHyphenIndex]

	// Find the user
	user, err := s.repo.FindUserByID(userID)
	if err != nil {
		return errors.New("user not found")
	}

	// Hash the new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update the user's password
	user.PasswordHash = string(hashedPassword)
	if err := s.repo.UpdateUser(user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}
