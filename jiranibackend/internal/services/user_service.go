package services

import (
	"errors"
	"fmt"

	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/repositories"
)

// UserService handles user-related business logic
type UserService struct {
	repo *repositories.UserRepository
}

// NewUserService creates a new user service
func NewUserService() *UserService {
	return &UserService{
		repo: repositories.NewUserRepository(),
	}
}

// GetUserProfile retrieves a user's profile
func (s *UserService) GetUserProfile(userID string) (*models.User, error) {
	return s.repo.FindByID(userID)
}

// UpdateUserProfile updates a user's profile
func (s *UserService) UpdateUserProfile(userID, fullName, phoneNumber, profileImage string) (*models.User, error) {
	// Find user
	user, err := s.repo.FindByID(userID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	// Check if phone number is already taken by another user
	if phoneNumber != user.PhoneNumber {
		existingUser, err := s.repo.FindByPhoneNumber(phoneNumber)
		if err == nil && existingUser.ID != userID {
			return nil, errors.New("phone number is already taken")
		}
	}

	// Update user
	user.FullName = fullName
	user.PhoneNumber = phoneNumber
	user.ProfileImage = profileImage

	if err := s.repo.Update(user); err != nil {
		return nil, fmt.Errorf("failed to update user profile: %w", err)
	}

	return user, nil
}

// AddUserLocation adds a new location for a user
func (s *UserService) AddUserLocation(userID, name, address string, latitude, longitude float64, isDefault bool) (*models.Location, error) {
	// Create location
	location := models.Location{
		UserID:    userID,
		Name:      name,
		Address:   address,
		Latitude:  latitude,
		Longitude: longitude,
		IsDefault: isDefault,
	}

	// If this location is set as default, unset other default locations
	if isDefault {
		if err := s.repo.UnsetDefaultLocations(userID); err != nil {
			return nil, fmt.Errorf("failed to update default location: %w", err)
		}
	}

	if err := s.repo.CreateLocation(&location); err != nil {
		return nil, fmt.Errorf("failed to create location: %w", err)
	}

	return &location, nil
}

// GetUserLocations retrieves all locations for a user
func (s *UserService) GetUserLocations(userID string) ([]models.Location, error) {
	return s.repo.GetUserLocations(userID)
}

// DeleteUserLocation deletes a location
func (s *UserService) DeleteUserLocation(userID string, locationID uint) error {
	// Find location
	location, err := s.repo.FindLocationByID(locationID, userID)
	if err != nil {
		return errors.New("location not found")
	}

	// Delete location
	if err := s.repo.DeleteLocation(location); err != nil {
		return fmt.Errorf("failed to delete location: %w", err)
	}

	return nil
}
