package routes

import (
	"fmt"
	"io"
	"log"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/controllers"
	"github.com/jirani/backend/internal/middleware"
	"github.com/jirani/backend/internal/websocket"
)

// SetupRoutes configures all application routes
func SetupRoutes(app *fiber.App) {
	// Debug route to test file uploads (no auth required)
	// This is placed outside the API group to avoid middleware
	app.Post("/debug/upload", func(c *fiber.Ctx) error {
		// Log the request
		log.Printf("Received debug upload request: %s %s", c.Method(), c.Path())

		// Get file from form
		file, err := c.FormFile("image")
		if err != nil {
			log.Printf("Debug upload error: %v", err)
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": fmt.Sprintf("No image provided or error processing form: %v", err),
			})
		}

		log.Printf("Debug upload received file: %s, size: %d bytes", file.Filename, file.Size)

		// Open file to verify it can be read
		fileHandle, err := file.Open()
		if err != nil {
			log.Printf("Debug upload error opening file: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": fmt.Sprintf("Failed to open uploaded file: %v", err),
			})
		}
		defer fileHandle.Close()

		// Read a few bytes to verify the file is readable
		buffer := make([]byte, 512)
		n, err := fileHandle.Read(buffer)
		if err != nil && err != io.EOF {
			log.Printf("Debug upload error reading file: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": fmt.Sprintf("Failed to read uploaded file: %v", err),
			})
		}

		log.Printf("Debug upload successfully read %d bytes from file", n)

		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "Debug upload received successfully",
			"file": fiber.Map{
				"name":       file.Filename,
				"size":       file.Size,
				"bytes_read": n,
			},
		})
	})

	// API version group
	api := app.Group("/api/v1")

	// Health check
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "Jirani API is running",
		})
	})

	// Root level ping endpoint for Traefik health checks
	app.Get("/ping", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "pong",
		})
	})

	// WebSocket route
	app.Get("/ws", websocket.HandleWebSocket)

	// Auth routes
	auth := api.Group("/auth")
	auth.Post("/register", controllers.Register)
	auth.Post("/login", controllers.Login)
	auth.Post("/forgot-password", controllers.ForgotPassword)
	auth.Post("/reset-password", controllers.ResetPassword)

	// Protected routes
	protected := api.Group("/", middleware.Authenticate)

	// Protected auth routes
	protectedAuth := protected.Group("/auth")
	protectedAuth.Post("/refresh", controllers.RefreshToken)

	// User routes
	users := protected.Group("/users")
	users.Get("/me", controllers.GetUserProfile)
	users.Put("/me", controllers.UpdateUserProfile)
	users.Put("/me/password", controllers.ChangePassword)
	users.Post("/me/profileimage", controllers.UploadProfileImage)
	users.Post("/me/locations", controllers.AddUserLocation)
	users.Get("/me/locations", controllers.GetUserLocations)
	users.Delete("/me/locations/:id", controllers.DeleteUserLocation)

	// Location history routes - PRP-LOCATION-ENH-001
	users.Post("/me/location-history", controllers.SaveLocationToHistory)
	users.Get("/me/location-history", controllers.GetLocationHistory)
	users.Delete("/me/location-history/:id", controllers.DeleteLocationFromHistory)

	// Restaurant routes
	restaurants := api.Group("/restaurants")
	restaurants.Get("/", controllers.ListRestaurants)
	restaurants.Get("/:id", controllers.GetRestaurant)
	restaurants.Get("/:id/menu", controllers.GetRestaurantMenu)

	// Protected restaurant routes
	protectedRestaurants := protected.Group("/restaurants")
	protectedRestaurants.Post("/", middleware.RequireRole("admin"), controllers.CreateRestaurant)
	protectedRestaurants.Put("/:id", middleware.RequireRole("admin"), controllers.UpdateRestaurant)
	protectedRestaurants.Delete("/:id", middleware.RequireRole("admin"), controllers.DeleteRestaurant)

	// Order routes
	orders := protected.Group("/orders")
	orders.Post("/", controllers.CreateOrder)
	orders.Get("/", controllers.GetUserOrders)
	orders.Get("/:id", controllers.GetOrderDetails)
	orders.Put("/:id/cancel", controllers.CancelOrder)

	// Boda Boda routes
	boda := protected.Group("/boda")
	boda.Get("/drivers", controllers.ListNearbyDrivers)
	boda.Post("/rides", controllers.RequestRide)
	boda.Get("/rides", controllers.GetUserRides)
	boda.Get("/rides/:id", controllers.GetRideDetails)
	boda.Put("/rides/:id/cancel", controllers.CancelRide)

	// Rating routes
	boda.Post("/rides/:id/rate", controllers.RateRide)
	boda.Get("/rides/:id/rating", controllers.GetRideRating)
	boda.Get("/ratings", controllers.GetUserRatings)

	// Emergency routes
	boda.Post("/emergency", controllers.TriggerEmergency)
	boda.Post("/emergency/:id/cancel", controllers.CancelEmergency)
	boda.Get("/emergency/:id", controllers.GetEmergencyStatus)

	// Location routes
	boda.Get("/rides/:id/location", controllers.GetRideLocation)
	boda.Get("/drivers/:id/location", controllers.GetDriverLocation)
	boda.Put("/location", controllers.UpdateDriverLocationWithRide)

	// Notification routes
	notifications := protected.Group("/notifications")
	notifications.Get("/", controllers.GetUserNotifications)
	notifications.Get("/unread/count", controllers.GetUnreadNotificationCount)
	notifications.Put("/:id/read", controllers.MarkNotificationAsRead)
	notifications.Put("/read/all", controllers.MarkAllNotificationsAsRead)
	notifications.Post("/device/register", controllers.RegisterDeviceToken)
	notifications.Delete("/device/unregister", controllers.UnregisterDeviceToken)

	// Driver routes
	driver := protected.Group("/driver")
	driver.Post("/register", controllers.RegisterAsDriver)
	driver.Get("/profile", middleware.RequireRole("driver"), controllers.GetDriverProfile)
	driver.Put("/profile", middleware.RequireRole("driver"), controllers.UpdateDriverProfile)
	driver.Post("/vehicles", middleware.RequireRole("driver"), controllers.AddDriverVehicle)
	driver.Get("/vehicles", middleware.RequireRole("driver"), controllers.GetDriverVehicles)
	driver.Put("/status", middleware.RequireRole("driver"), controllers.UpdateDriverStatus)
	driver.Put("/location", middleware.RequireRole("driver"), controllers.UpdateDriverLocation)
	driver.Get("/rides", middleware.RequireRole("driver"), controllers.GetDriverRides)
	driver.Put("/rides/:id/accept", middleware.RequireRole("driver"), controllers.AcceptRide)
	driver.Put("/rides/:id/en-route", middleware.RequireRole("driver"), controllers.EnRouteToPickup)
	driver.Put("/rides/:id/arrived", middleware.RequireRole("driver"), controllers.ArrivedAtPickup)
	driver.Put("/rides/:id/pickup", middleware.RequireRole("driver"), controllers.PickupRider)
	driver.Put("/rides/:id/complete", middleware.RequireRole("driver"), controllers.CompleteRide)
	driver.Put("/rides/:id/eta", middleware.RequireRole("driver"), controllers.UpdateRideETA)
	driver.Get("/ratings", middleware.RequireRole("driver"), controllers.GetDriverRatings)

	// Payment routes
	payments := protected.Group("/payments")
	payments.Post("/", controllers.CreatePayment)
	payments.Get("/", controllers.GetUserPayments)
	payments.Get("/:id", controllers.GetPaymentDetails)

	// Admin routes
	admin := protected.Group("/admin", middleware.RequireRole("admin"))
	admin.Get("/users", controllers.ListUsers)
	admin.Get("/orders", controllers.ListAllOrders)
	admin.Get("/rides", controllers.ListAllRides)
	admin.Get("/drivers", controllers.ListAllDrivers)
	admin.Put("/drivers/:id/verify", controllers.VerifyDriver)
}
