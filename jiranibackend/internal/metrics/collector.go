package metrics

import (
	"runtime"
	"runtime/debug"
	"time"
)

// Collector collects runtime metrics
type Collector struct {
	registry *Registry
	interval time.Duration
	stopCh   chan struct{}
}

// NewCollector creates a new metrics collector
func NewCollector(registry *Registry, interval time.Duration) *Collector {
	return &Collector{
		registry: registry,
		interval: interval,
		stopCh:   make(chan struct{}),
	}
}

// Start starts the metrics collector
func (c *Collector) Start() {
	go c.collect()
}

// Stop stops the metrics collector
func (c *Collector) Stop() {
	close(c.stopCh)
}

// collect periodically collects runtime metrics
func (c *Collector) collect() {
	ticker := time.NewTicker(c.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.collectRuntimeMetrics()
		case <-c.stopCh:
			return
		}
	}
}

// collectRuntimeMetrics collects runtime metrics
func (c *Collector) collectRuntimeMetrics() {
	// Memory stats
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("heap_alloc").Set(float64(memStats.HeapAlloc))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("heap_sys").Set(float64(memStats.HeapSys))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("heap_idle").Set(float64(memStats.HeapIdle))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("heap_inuse").Set(float64(memStats.HeapInuse))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("stack_inuse").Set(float64(memStats.StackInuse))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("stack_sys").Set(float64(memStats.StackSys))
	c.registry.SystemMetrics.MemoryUsage.WithLabelValues("sys").Set(float64(memStats.Sys))

	// Goroutines
	c.registry.SystemMetrics.GoroutinesCount.Set(float64(runtime.NumGoroutine()))

	// GC stats
	c.registry.SystemMetrics.GCPauseTime.Observe(float64(memStats.PauseTotalNs) / 1e9)
	c.registry.SystemMetrics.HeapObjects.Set(float64(memStats.HeapObjects))
	c.registry.SystemMetrics.HeapAlloc.Set(float64(memStats.HeapAlloc))

	// Thread stats
	c.registry.SystemMetrics.ThreadsCreated.Set(float64(runtime.NumCPU()))

	// Get GC stats
	gcStats := debug.GCStats{}
	debug.ReadGCStats(&gcStats)
}

// RegisterDBStats registers database stats with the collector
func (c *Collector) RegisterDBStats(dbName string, stats interface{}) {
	// This would be implemented based on the specific database driver
	// For example, for pgx:
	// stats.(*pgxpool.Pool).Stat()
}

// RecordQuery records a database query
func (c *Collector) RecordQuery(dbName, queryType string, duration time.Duration, err error) {
	c.registry.DatabaseMetrics.QueriesTotal.WithLabelValues(dbName, queryType).Inc()
	c.registry.DatabaseMetrics.QueryDuration.WithLabelValues(dbName, queryType).Observe(duration.Seconds())

	if err != nil {
		c.registry.DatabaseMetrics.QueryErrors.WithLabelValues(dbName, queryType, err.Error()).Inc()
	}
}

// RecordTransaction records a database transaction
func (c *Collector) RecordTransaction(dbName string, duration time.Duration, err error) {
	status := "success"
	if err != nil {
		status = "failure"
		c.registry.DatabaseMetrics.TransactionErrors.WithLabelValues(dbName, err.Error()).Inc()
	}

	c.registry.DatabaseMetrics.TransactionsTotal.WithLabelValues(dbName, status).Inc()
	c.registry.DatabaseMetrics.TransactionDuration.WithLabelValues(dbName).Observe(duration.Seconds())
}

// RecordConnectionAcquire records the time to acquire a database connection
func (c *Collector) RecordConnectionAcquire(dbName string, duration time.Duration) {
	c.registry.DatabaseMetrics.ConnectionAcquireTime.WithLabelValues(dbName).Observe(duration.Seconds())
}
