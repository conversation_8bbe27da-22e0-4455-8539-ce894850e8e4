package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"net/http"
)

// Registry manages all metrics for the application
type Registry struct {
	BusinessMetrics *BusinessMetrics
	DatabaseMetrics *DatabaseMetrics
	SystemMetrics   *SystemMetrics
}

// NewRegistry creates a new metrics registry
func NewRegistry(namespace string) *Registry {
	return &Registry{
		BusinessMetrics: NewBusinessMetrics(namespace),
		DatabaseMetrics: NewDatabaseMetrics(namespace),
		SystemMetrics:   NewSystemMetrics(namespace),
	}
}

// Handler returns an HTTP handler for exposing metrics
func (r *Registry) Handler() http.Handler {
	return promhttp.Handler()
}

// DatabaseMetrics contains database-specific metrics
type DatabaseMetrics struct {
	ConnectionsOpen        *prometheus.GaugeVec
	ConnectionsIdle        *prometheus.GaugeVec
	ConnectionsInUse       *prometheus.GaugeVec
	ConnectionsWaiting     *prometheus.GaugeVec
	QueryDuration          *prometheus.HistogramVec
	QueriesTotal           *prometheus.CounterVec
	QueryErrors            *prometheus.CounterVec
	TransactionsTotal      *prometheus.CounterVec
	TransactionErrors      *prometheus.CounterVec
	TransactionDuration    *prometheus.HistogramVec
	ConnectionAcquireTime  *prometheus.HistogramVec
}

// NewDatabaseMetrics creates a new DatabaseMetrics instance
func NewDatabaseMetrics(namespace string) *DatabaseMetrics {
	return &DatabaseMetrics{
		ConnectionsOpen: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "connections_open",
				Help:      "Number of open database connections",
			},
			[]string{"db_name"},
		),
		ConnectionsIdle: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "connections_idle",
				Help:      "Number of idle database connections",
			},
			[]string{"db_name"},
		),
		ConnectionsInUse: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "connections_in_use",
				Help:      "Number of database connections in use",
			},
			[]string{"db_name"},
		),
		ConnectionsWaiting: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "connections_waiting",
				Help:      "Number of connections waiting for a database connection",
			},
			[]string{"db_name"},
		),
		QueryDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "query_duration_seconds",
				Help:      "Duration of database queries in seconds",
				Buckets:   prometheus.ExponentialBuckets(0.001, 2, 10), // 1ms to ~1s
			},
			[]string{"db_name", "query_type"},
		),
		QueriesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "queries_total",
				Help:      "Total number of database queries",
			},
			[]string{"db_name", "query_type"},
		),
		QueryErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "query_errors_total",
				Help:      "Total number of database query errors",
			},
			[]string{"db_name", "query_type", "error_type"},
		),
		TransactionsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "transactions_total",
				Help:      "Total number of database transactions",
			},
			[]string{"db_name", "status"},
		),
		TransactionErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "transaction_errors_total",
				Help:      "Total number of database transaction errors",
			},
			[]string{"db_name", "error_type"},
		),
		TransactionDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "transaction_duration_seconds",
				Help:      "Duration of database transactions in seconds",
				Buckets:   prometheus.ExponentialBuckets(0.001, 2, 10), // 1ms to ~1s
			},
			[]string{"db_name"},
		),
		ConnectionAcquireTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "db",
				Name:      "connection_acquire_seconds",
				Help:      "Time to acquire a database connection in seconds",
				Buckets:   prometheus.ExponentialBuckets(0.001, 2, 10), // 1ms to ~1s
			},
			[]string{"db_name"},
		),
	}
}

// SystemMetrics contains system-specific metrics
type SystemMetrics struct {
	MemoryUsage      *prometheus.GaugeVec
	CPUUsage         *prometheus.GaugeVec
	GoroutinesCount  prometheus.Gauge
	GCPauseTime      prometheus.Histogram
	HeapObjects      prometheus.Gauge
	HeapAlloc        prometheus.Gauge
	ThreadsCreated   prometheus.Gauge
	MutexContentions prometheus.Counter
}

// NewSystemMetrics creates a new SystemMetrics instance
func NewSystemMetrics(namespace string) *SystemMetrics {
	return &SystemMetrics{
		MemoryUsage: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "memory_usage_bytes",
				Help:      "Memory usage in bytes",
			},
			[]string{"type"},
		),
		CPUUsage: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "cpu_usage_percent",
				Help:      "CPU usage in percent",
			},
			[]string{"mode"},
		),
		GoroutinesCount: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "goroutines_count",
				Help:      "Number of goroutines",
			},
		),
		GCPauseTime: prometheus.NewHistogram(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "gc_pause_seconds",
				Help:      "GC pause time in seconds",
				Buckets:   prometheus.ExponentialBuckets(0.0001, 2, 10), // 0.1ms to ~100ms
			},
		),
		HeapObjects: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "heap_objects",
				Help:      "Number of allocated heap objects",
			},
		),
		HeapAlloc: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "heap_alloc_bytes",
				Help:      "Heap allocation in bytes",
			},
		),
		ThreadsCreated: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "threads_created",
				Help:      "Number of OS threads created",
			},
		),
		MutexContentions: prometheus.NewCounter(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "system",
				Name:      "mutex_contentions_total",
				Help:      "Total number of mutex contentions",
			},
		),
	}
}
