package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// BusinessMetrics contains business-specific metrics for the food delivery application
type BusinessMetrics struct {
	// User metrics
	UserRegistrationsTotal *prometheus.CounterVec
	UserLoginsTotal        *prometheus.CounterVec
	ActiveUsers            *prometheus.GaugeVec

	// Order metrics
	OrdersTotal            *prometheus.CounterVec
	OrdersInProgress       *prometheus.GaugeVec
	OrderPreparationTime   *prometheus.HistogramVec
	OrderDeliveryTime      *prometheus.HistogramVec
	OrderCancellationsTotal *prometheus.CounterVec
	OrderValue             *prometheus.SummaryVec

	// Restaurant metrics
	RestaurantsTotal       *prometheus.GaugeVec
	RestaurantOrdersTotal  *prometheus.CounterVec
	RestaurantRatings      *prometheus.SummaryVec

	// Driver metrics
	DriversTotal           *prometheus.GaugeVec
	DriversActive          *prometheus.GaugeVec
	DriverDeliveriesTotal  *prometheus.CounterVec
	DriverRatings          *prometheus.SummaryVec
	DriverDeliveryTime     *prometheus.HistogramVec

	// Payment metrics
	PaymentsTotal          *prometheus.CounterVec
	PaymentAmount          *prometheus.SummaryVec
	PaymentFailuresTotal   *prometheus.CounterVec
}

// NewBusinessMetrics creates a new BusinessMetrics instance with all the required prometheus metrics
func NewBusinessMetrics(namespace string) *BusinessMetrics {
	return &BusinessMetrics{
		// User metrics
		UserRegistrationsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "users",
				Name:      "registrations_total",
				Help:      "Total number of user registrations",
			},
			[]string{"source"},
		),
		UserLoginsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "users",
				Name:      "logins_total",
				Help:      "Total number of user logins",
			},
			[]string{"status"},
		),
		ActiveUsers: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "users",
				Name:      "active",
				Help:      "Number of active users",
			},
			[]string{"timeframe"},
		),

		// Order metrics
		OrdersTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "total",
				Help:      "Total number of orders",
			},
			[]string{"status", "payment_method"},
		),
		OrdersInProgress: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "in_progress",
				Help:      "Number of orders currently in progress",
			},
			[]string{"status"},
		),
		OrderPreparationTime: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "preparation_time_seconds",
				Help:      "Time taken to prepare an order in seconds",
				Buckets:   prometheus.LinearBuckets(60, 60, 10), // 1min to 10min
			},
			[]string{"restaurant_id"},
		),
		OrderDeliveryTime: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "delivery_time_seconds",
				Help:      "Time taken to deliver an order in seconds",
				Buckets:   prometheus.LinearBuckets(300, 300, 10), // 5min to 50min
			},
			[]string{"restaurant_id"},
		),
		OrderCancellationsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "cancellations_total",
				Help:      "Total number of order cancellations",
			},
			[]string{"reason", "stage"},
		),
		OrderValue: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Subsystem: "orders",
				Name:      "value_ksh",
				Help:      "Order value in KSH",
				Objectives: map[float64]float64{
					0.5:  0.05,
					0.9:  0.01,
					0.99: 0.001,
				},
			},
			[]string{"restaurant_id"},
		),

		// Restaurant metrics
		RestaurantsTotal: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "restaurants",
				Name:      "total",
				Help:      "Total number of restaurants",
			},
			[]string{"status"},
		),
		RestaurantOrdersTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "restaurants",
				Name:      "orders_total",
				Help:      "Total number of orders per restaurant",
			},
			[]string{"restaurant_id"},
		),
		RestaurantRatings: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Subsystem: "restaurants",
				Name:      "ratings",
				Help:      "Restaurant ratings",
				Objectives: map[float64]float64{
					0.5:  0.05,
					0.9:  0.01,
					0.99: 0.001,
				},
			},
			[]string{"restaurant_id"},
		),

		// Driver metrics
		DriversTotal: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "drivers",
				Name:      "total",
				Help:      "Total number of drivers",
			},
			[]string{"status"},
		),
		DriversActive: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Subsystem: "drivers",
				Name:      "active",
				Help:      "Number of active drivers",
			},
			[]string{},
		),
		DriverDeliveriesTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "drivers",
				Name:      "deliveries_total",
				Help:      "Total number of deliveries per driver",
			},
			[]string{"driver_id"},
		),
		DriverRatings: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Subsystem: "drivers",
				Name:      "ratings",
				Help:      "Driver ratings",
				Objectives: map[float64]float64{
					0.5:  0.05,
					0.9:  0.01,
					0.99: 0.001,
				},
			},
			[]string{"driver_id"},
		),
		DriverDeliveryTime: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Subsystem: "drivers",
				Name:      "delivery_time_seconds",
				Help:      "Time taken by driver to deliver an order in seconds",
				Buckets:   prometheus.LinearBuckets(300, 300, 10), // 5min to 50min
			},
			[]string{"driver_id"},
		),

		// Payment metrics
		PaymentsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "payments",
				Name:      "total",
				Help:      "Total number of payments",
			},
			[]string{"method", "status"},
		),
		PaymentAmount: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Subsystem: "payments",
				Name:      "amount_ksh",
				Help:      "Payment amount in KSH",
				Objectives: map[float64]float64{
					0.5:  0.05,
					0.9:  0.01,
					0.99: 0.001,
				},
			},
			[]string{"method"},
		),
		PaymentFailuresTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Subsystem: "payments",
				Name:      "failures_total",
				Help:      "Total number of payment failures",
			},
			[]string{"method", "reason"},
		),
	}
}
