package websocket

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gofiber/websocket/v2"
)

// MessageType represents the type of WebSocket message
type MessageType string

const (
	// Ride-related messages
	MessageTypeRideRequest      MessageType = "ride_request"
	MessageTypeRideAccepted     MessageType = "ride_accepted"
	MessageTypeRideStatusUpdate MessageType = "ride_status_update"
	MessageTypeLocationUpdate   MessageType = "location_update"
	MessageTypeDriverLocation   MessageType = "driver_location"

	// Communication messages
	MessageTypeChat         MessageType = "chat"
	MessageTypeCall         MessageType = "call"
	MessageTypeNotification MessageType = "notification"

	// System messages
	MessageTypeError     MessageType = "error"
	MessageTypeHeartbeat MessageType = "heartbeat"
)

// Message represents a WebSocket message
type Message struct {
	Type      MessageType     `json:"type"`
	UserID    string          `json:"user_id"`
	RideID    string          `json:"ride_id,omitempty"`
	Data      json.RawMessage `json:"data"`
	Timestamp int64           `json:"timestamp"`
}

// Client represents a WebSocket client
type Client struct {
	ID     string
	UserID string
	Role   string // "user" or "driver"
	Conn   *websocket.Conn
	Send   chan Message
	Hub    *Hub
}

// Hub maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// User ID to client mapping for direct messaging
	userClients map[string]*Client

	// Ride ID to clients mapping for ride-specific messaging
	rideClients map[string][]*Client

	// Inbound messages from the clients
	broadcast chan Message

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Mutex for thread-safe operations
	mutex sync.RWMutex
}

// NewHub creates a new WebSocket hub
func NewHub() *Hub {
	return &Hub{
		clients:     make(map[*Client]bool),
		userClients: make(map[string]*Client),
		rideClients: make(map[string][]*Client),
		broadcast:   make(chan Message),
		register:    make(chan *Client),
		unregister:  make(chan *Client),
	}
}

// Run starts the hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)
		}
	}
}

// registerClient registers a new client
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true
	h.userClients[client.UserID] = client

	log.Printf("Client registered: %s (User: %s, Role: %s)", client.ID, client.UserID, client.Role)
}

// unregisterClient unregisters a client
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		delete(h.userClients, client.UserID)
		close(client.Send)

		// Remove from ride clients
		for rideID, clients := range h.rideClients {
			for i, c := range clients {
				if c == client {
					h.rideClients[rideID] = append(clients[:i], clients[i+1:]...)
					break
				}
			}
			// Clean up empty ride client lists
			if len(h.rideClients[rideID]) == 0 {
				delete(h.rideClients, rideID)
			}
		}

		log.Printf("Client unregistered: %s (User: %s)", client.ID, client.UserID)
	}
}

// broadcastMessage broadcasts a message to relevant clients
func (h *Hub) broadcastMessage(message Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// If message has a specific user ID, send only to that user
	if message.UserID != "" {
		if client, ok := h.userClients[message.UserID]; ok {
			select {
			case client.Send <- message:
			default:
				close(client.Send)
				delete(h.clients, client)
				delete(h.userClients, client.UserID)
			}
		}
		return
	}

	// If message has a ride ID, send to all clients in that ride
	if message.RideID != "" {
		if clients, ok := h.rideClients[message.RideID]; ok {
			for _, client := range clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(h.clients, client)
					delete(h.userClients, client.UserID)
				}
			}
		}
		return
	}

	// Broadcast to all clients
	for client := range h.clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.clients, client)
			delete(h.userClients, client.UserID)
		}
	}
}

// SendToUser sends a message to a specific user
func (h *Hub) SendToUser(userID string, messageType MessageType, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	message := Message{
		Type:      messageType,
		UserID:    userID,
		Data:      dataBytes,
		Timestamp: getCurrentTimestamp(),
	}

	h.broadcast <- message
	return nil
}

// SendToRide sends a message to all participants in a ride
func (h *Hub) SendToRide(rideID string, messageType MessageType, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	message := Message{
		Type:      messageType,
		RideID:    rideID,
		Data:      dataBytes,
		Timestamp: getCurrentTimestamp(),
	}

	h.broadcast <- message
	return nil
}

// AddClientToRide adds a client to a ride's client list
func (h *Hub) AddClientToRide(rideID string, client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.rideClients[rideID] = append(h.rideClients[rideID], client)
}

// RemoveClientFromRide removes a client from a ride's client list
func (h *Hub) RemoveClientFromRide(rideID string, client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if clients, ok := h.rideClients[rideID]; ok {
		for i, c := range clients {
			if c == client {
				h.rideClients[rideID] = append(clients[:i], clients[i+1:]...)
				break
			}
		}
		// Clean up empty ride client lists
		if len(h.rideClients[rideID]) == 0 {
			delete(h.rideClients, rideID)
		}
	}
}

// GetConnectedUsers returns the number of connected users
func (h *Hub) GetConnectedUsers() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.userClients)
}

// IsUserConnected checks if a user is connected
func (h *Hub) IsUserConnected(userID string) bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	_, ok := h.userClients[userID]
	return ok
}

// getCurrentTimestamp returns the current Unix timestamp in milliseconds
func getCurrentTimestamp() int64 {
	return time.Now().UnixMilli()
}
