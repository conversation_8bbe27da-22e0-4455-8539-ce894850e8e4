package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/gofiber/websocket/v2"
	"github.com/google/uuid"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512
)

// readPump pumps messages from the websocket connection to the hub
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		var message Message
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Set the user ID from the client
		message.UserID = c.UserID
		message.Timestamp = time.Now().UnixMilli()

		// Handle the message based on its type
		c.handleMessage(message)
	}
}

// writePump pumps messages from the hub to the websocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteJSON(message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processes incoming messages from the client
func (c *Client) handleMessage(message Message) {
	switch message.Type {
	case MessageTypeLocationUpdate:
		c.handleLocationUpdate(message)
	case MessageTypeChat:
		c.handleChatMessage(message)
	case MessageTypeHeartbeat:
		c.handleHeartbeat(message)
	default:
		log.Printf("Unknown message type: %s", message.Type)
	}
}

// handleLocationUpdate processes location updates from drivers
func (c *Client) handleLocationUpdate(message Message) {
	if c.Role != "driver" {
		return
	}

	var locationData struct {
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
		RideID    string  `json:"ride_id,omitempty"`
	}

	if err := json.Unmarshal(message.Data, &locationData); err != nil {
		log.Printf("Error parsing location data: %v", err)
		return
	}

	// Update driver location in database
	// This will be implemented with the location service

	// If there's an active ride, broadcast location to the passenger
	if locationData.RideID != "" {
		c.Hub.SendToRide(locationData.RideID, MessageTypeDriverLocation, locationData)
	}
}

// handleChatMessage processes chat messages between driver and passenger
func (c *Client) handleChatMessage(message Message) {
	var chatData struct {
		RideID  string `json:"ride_id"`
		Message string `json:"message"`
		To      string `json:"to"` // user_id of recipient
	}

	if err := json.Unmarshal(message.Data, &chatData); err != nil {
		log.Printf("Error parsing chat data: %v", err)
		return
	}

	// Store chat message in database
	// This will be implemented with the chat service

	// Forward message to recipient
	if chatData.To != "" {
		c.Hub.SendToUser(chatData.To, MessageTypeChat, map[string]interface{}{
			"ride_id":   chatData.RideID,
			"message":   chatData.Message,
			"from":      c.UserID,
			"from_role": c.Role,
		})
	}
}

// handleHeartbeat processes heartbeat messages to keep connection alive
func (c *Client) handleHeartbeat(message Message) {
	// Send heartbeat response
	response := Message{
		Type:      MessageTypeHeartbeat,
		UserID:    c.UserID,
		Data:      json.RawMessage(`{"status":"alive"}`),
		Timestamp: time.Now().UnixMilli(),
	}

	select {
	case c.Send <- response:
	default:
		close(c.Send)
	}
}

// NewClient creates a new WebSocket client
func NewClient(conn *websocket.Conn, userID, role string, hub *Hub) *Client {
	return &Client{
		ID:     uuid.New().String(),
		UserID: userID,
		Role:   role,
		Conn:   conn,
		Send:   make(chan Message, 256),
		Hub:    hub,
	}
}

// Start starts the client's read and write pumps
func (c *Client) Start() {
	go c.writePump()
	go c.readPump()
}

// SendMessage sends a message to this client
func (c *Client) SendMessage(messageType MessageType, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	message := Message{
		Type:      messageType,
		Data:      dataBytes,
		Timestamp: time.Now().UnixMilli(),
	}

	select {
	case c.Send <- message:
		return nil
	default:
		return ErrClientDisconnected
	}
}

// Close closes the client connection
func (c *Client) Close() {
	c.Conn.Close()
}

// Custom errors
var (
	ErrClientDisconnected = fmt.Errorf("client disconnected")
)
