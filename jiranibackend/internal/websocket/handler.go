package websocket

import (
	"log"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

// Global hub instance
var GlobalHub *Hub

// InitializeHub initializes the global WebSocket hub
func InitializeHub() {
	GlobalHub = NewHub()
	go GlobalHub.Run()
	log.Println("WebSocket hub initialized")
}

// HandleWebSocket handles WebSocket connections
func HandleWebSocket(c *fiber.Ctx) error {
	// Check if this is a WebSocket upgrade request
	if websocket.IsWebSocketUpgrade(c) {
		return websocket.New(func(conn *websocket.Conn) {
			// Get user information from query parameters or headers
			userID := c.Query("user_id")
			role := c.Query("role", "user") // default to "user" role

			if userID == "" {
				log.Println("WebSocket connection rejected: missing user_id")
				conn.Close()
				return
			}

			// Create new client
			client := NewClient(conn, userID, role, GlobalHub)

			// Register client with hub
			GlobalHub.register <- client

			// Start client pumps
			client.Start()
		})(c)
	}

	return fiber.ErrUpgradeRequired
}

// BroadcastRideUpdate broadcasts a ride status update to relevant clients
func BroadcastRideUpdate(rideID, userID, driverID string, data interface{}) {
	if GlobalHub == nil {
		return
	}

	// Send to passenger
	if userID != "" {
		GlobalHub.SendToUser(userID, MessageTypeRideStatusUpdate, data)
	}

	// Send to driver
	if driverID != "" {
		GlobalHub.SendToUser(driverID, MessageTypeRideStatusUpdate, data)
	}
}

// BroadcastLocationUpdate broadcasts a location update to ride participants
func BroadcastLocationUpdate(rideID string, locationData interface{}) {
	if GlobalHub == nil {
		return
	}

	GlobalHub.SendToRide(rideID, MessageTypeLocationUpdate, locationData)
}

// BroadcastRideRequest broadcasts a ride request to nearby drivers
func BroadcastRideRequest(driverIDs []string, rideData interface{}) {
	if GlobalHub == nil {
		return
	}

	for _, driverID := range driverIDs {
		GlobalHub.SendToUser(driverID, MessageTypeRideRequest, rideData)
	}
}

// BroadcastNotification broadcasts a notification to a specific user
func BroadcastNotification(userID string, notification interface{}) {
	if GlobalHub == nil {
		return
	}

	GlobalHub.SendToUser(userID, MessageTypeNotification, notification)
}

// GetConnectedUsers returns the number of connected users
func GetConnectedUsers() int {
	if GlobalHub == nil {
		return 0
	}
	return GlobalHub.GetConnectedUsers()
}

// IsUserOnline checks if a user is currently connected
func IsUserOnline(userID string) bool {
	if GlobalHub == nil {
		return false
	}
	return GlobalHub.IsUserConnected(userID)
}
