<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Subject}}</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 20px; /* Rounded corners like in the app */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            text-align: center;
            padding: 25px 0;
            border-bottom: 1px solid #eeeeee;
            background-color: #FAFAFA; /* Light background for header */
            margin: -20px -20px 0 -20px; /* Extend to edges */
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 30px 20px;
            line-height: 1.6;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #888888;
            border-top: 1px solid #eeeeee;
            background-color: #FAFAFA; /* Light background for footer */
            margin: 0 -20px -20px -20px; /* Extend to edges */
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #FF5722; /* Jirani primary color */
            color: white;
            text-decoration: none;
            border-radius: 24px; /* Rounded corners like in the app */
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #E64A19; /* Darker shade for hover */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .highlight {
            color: #FF5722; /* Jirani primary color */
            font-weight: 600;
        }
        .section {
            margin-bottom: 25px;
        }
        .alert {
            background-color: #FFF3E0; /* Light orange background */
            border-left: 4px solid #FFC107; /* Secondary color from the app */
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{.CompanyLogo}}" alt="{{.CompanyName}} Logo" class="logo">
            <h1 style="color: #FF5722; font-weight: 700; font-size: 28px;">Password Reset Request</h1>
        </div>

        <div class="content">
            <div class="section">
                <h2>Hello {{.Name}},</h2>
                <p>We received a request to reset your password for your Jirani account. If you didn't make this request, you can safely ignore this email.</p>
            </div>

            <div class="section">
                <p>To reset your password, click the button below:</p>
                <div style="text-align: center;">
                    <a href="{{.ResetLink}}" class="button">{{.ActionText}}</a>
                </div>
                <p>This link will expire in 30 minutes for security reasons.</p>
            </div>

            <div class="alert">
                <p><strong>Note:</strong> If the button above doesn't work, copy and paste the following URL into your browser:</p>
                <p style="word-break: break-all;">{{.ResetLink}}</p>
            </div>

            <div class="section">
                <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:{{.SupportEmail}}" style="color: #FF5722; text-decoration: none; font-weight: 500;">{{.SupportEmail}}</a>.</p>
            </div>
        </div>

        <div class="footer">
            <p>&copy; {{.Year}} {{.CompanyName}}. All rights reserved.</p>
            <p>You're receiving this email because a password reset was requested for your account.</p>
        </div>
    </div>
</body>
</html>
