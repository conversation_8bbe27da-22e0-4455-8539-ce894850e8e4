<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Subject}}</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 20px; /* Rounded corners like in the app */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            text-align: center;
            padding: 25px 0;
            border-bottom: 1px solid #eeeeee;
            background-color: #FAFAFA; /* Light background for header */
            margin: -20px -20px 0 -20px; /* Extend to edges */
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 30px 20px;
            line-height: 1.6;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #888888;
            border-top: 1px solid #eeeeee;
            background-color: #FAFAFA; /* Light background for footer */
            margin: 0 -20px -20px -20px; /* Extend to edges */
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #FF5722; /* Jirani primary color */
            color: white;
            text-decoration: none;
            border-radius: 24px; /* Rounded corners like in the app */
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #E64A19; /* Darker shade for hover */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .highlight {
            color: #FF5722; /* Jirani primary color */
            font-weight: 600;
        }
        .section {
            margin-bottom: 25px;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{.CompanyLogo}}" alt="{{.CompanyName}} Logo" class="logo">
            <h1 style="color: #FF5722; font-weight: 700; font-size: 28px;">Welcome to {{.CompanyName}}!</h1>
        </div>

        <div class="content">
            <div class="section">
                <h2>Hello {{.Name}},</h2>
                <p>{{.WelcomeText}}</p>
                <p>Jirani connects you with services and people in your neighborhood, making everyday life easier and more convenient.</p>
            </div>

            <div class="section">
                <h3 style="color: #FF5722; font-weight: 600;">What you can do with Jirani:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li style="margin-bottom: 10px; padding-left: 24px; position: relative;">
                        <span style="position: absolute; left: 0; color: #FF5722; font-weight: bold;">✓</span>
                        Order food from local restaurants
                    </li>
                    <li style="margin-bottom: 10px; padding-left: 24px; position: relative;">
                        <span style="position: absolute; left: 0; color: #FF5722; font-weight: bold;">✓</span>
                        Book Boda Boda rides
                    </li>
                    <li style="margin-bottom: 10px; padding-left: 24px; position: relative;">
                        <span style="position: absolute; left: 0; color: #FF5722; font-weight: bold;">✓</span>
                        Discover local services
                    </li>
                    <li style="margin-bottom: 10px; padding-left: 24px; position: relative;">
                        <span style="position: absolute; left: 0; color: #FF5722; font-weight: bold;">✓</span>
                        Connect with your community
                    </li>
                </ul>
            </div>

            <div style="text-align: center;">
                <a href="{{.ActionLink}}" class="button">{{.ActionText}}</a>
            </div>

            <div class="section">
                <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:{{.SupportEmail}}" style="color: #FF5722; text-decoration: none; font-weight: 500;">{{.SupportEmail}}</a>.</p>
                <p>Thank you for choosing Jirani!</p>
            </div>
        </div>

        <div class="footer">
            <p>&copy; {{.Year}} {{.CompanyName}}. All rights reserved.</p>
            <p>You're receiving this email because you recently created a new Jirani account.</p>
        </div>
    </div>
</body>
</html>
