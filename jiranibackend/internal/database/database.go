package database

import (
	"context"
	"fmt"
	"log"

	"github.com/jirani/backend/internal/config"
	"github.com/jirani/backend/internal/models"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB    *gorm.DB
	Redis *redis.Client
)

// Connect establishes connections to the database and Redis
func Connect(cfg *config.Config) error {
	var err error

	// Connect to PostgreSQL
	dsn := cfg.GetDSN()
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Skip auto-migration in production to avoid constraint issues
	if cfg.Environment != "production" {
		// Auto-migrate models
		if err := autoMigrate(); err != nil {
			return fmt.Errorf("failed to migrate database: %w", err)
		}
	} else {
		log.Println("Skipping auto-migration in production environment")
	}

	// Connect to Redis
	Redis = redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Password: cfg.RedisPassword,
		DB:       0,
	})

	// Test Redis connection
	ctx := context.Background()
	if _, err := Redis.Ping(ctx).Result(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Println("Connected to database and Redis successfully")

	// Seed data in development mode
	if cfg.Environment == "development" {
		if err := SeedData(); err != nil {
			log.Printf("Warning: Failed to seed data: %v", err)
		}
	}

	return nil
}

// autoMigrate automatically migrates the database schema
func autoMigrate() error {
	log.Println("Running database migrations...")

	// Migrate models one by one to handle errors gracefully
	modelsToMigrate := []interface{}{
		// User models
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.Location{},

		// Restaurant models
		&models.Restaurant{},
		&models.MenuItem{},

		// Order models
		&models.Order{},
		&models.OrderItem{},

		// Boda Boda models
		&models.Driver{},
		&models.Vehicle{},
		&models.Ride{},

		// Payment models
		&models.Payment{},

		// Rating models
		&models.RideRating{},
		&models.DriverRating{},
		&models.RiderRating{},

		// Notification models
		&models.Notification{},
		&models.DeviceToken{},
	}

	// Migrate each model individually to handle constraint errors
	for _, model := range modelsToMigrate {
		if err := DB.AutoMigrate(model); err != nil {
			// Log the error but continue with other models
			log.Printf("Warning: Failed to migrate model %T: %v", model, err)
		} else {
			log.Printf("Successfully migrated model %T", model)
		}
	}

	log.Println("Database migrations completed!")
	return nil
}
