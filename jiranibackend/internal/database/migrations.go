package database

import (
	"github.com/jirani/backend/internal/models"
	"gorm.io/gorm"
)

// RunMigrations runs all database migrations
func RunMigrations(db *gorm.DB) error {
	// Auto migrate all models
	return db.AutoMigrate(
		// User models
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.Location{},

		// Restaurant models
		&models.Restaurant{},
		&models.MenuItem{},

		// Order models
		&models.Order{},
		&models.OrderItem{},

		// Boda Boda models
		&models.Driver{},
		&models.Vehicle{},
		&models.Ride{},

		// Payment models
		&models.Payment{},

		// Rating models
		&models.RideRating{},
		&models.DriverRating{},
		&models.RiderRating{},

		// Notification models
		&models.Notification{},
		&models.DeviceToken{},
	)
}
