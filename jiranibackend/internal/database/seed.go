package database

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/google/uuid"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
	"golang.org/x/crypto/bcrypt"
)

// SeedData populates the database with test data
func SeedData() error {
	log.Println("Seeding database with test data...")

	// Check if data already exists
	var userCount int64
	if err := DB.Model(&models.User{}).Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to count users: %w", err)
	}

	if userCount > 0 {
		log.Println("Database already contains data, skipping seed")
		return nil
	}

	// Seed roles
	if err := seedRoles(); err != nil {
		return err
	}

	// Seed users
	users, err := seedUsers()
	if err != nil {
		return err
	}

	// Seed restaurants
	restaurants, err := seedRestaurants()
	if err != nil {
		return err
	}

	// Seed menu items
	menuItems, err := seedMenuItems(restaurants)
	if err != nil {
		return err
	}

	// Seed orders
	orders, err := seedOrders(users, menuItems)
	if err != nil {
		return err
	}

	// Seed drivers
	drivers, err := seedDrivers(users)
	if err != nil {
		return err
	}

	// Seed vehicles
	if err := seedVehicles(drivers); err != nil {
		return err
	}

	// Seed rides
	rides, err := seedRides(users, drivers)
	if err != nil {
		return err
	}

	// Seed payments
	if err := seedPayments(users, orders, rides); err != nil {
		return err
	}

	log.Println("Database seeded successfully")
	return nil
}

func seedRoles() error {
	roles := []models.Role{
		{Name: "user", Description: "Regular user"},
		{Name: "admin", Description: "Administrator"},
		{Name: "driver", Description: "Boda Boda driver"},
	}

	for _, role := range roles {
		if err := DB.Create(&role).Error; err != nil {
			return fmt.Errorf("failed to create role %s: %w", role.Name, err)
		}
	}

	return nil
}

func seedUsers() ([]models.User, error) {
	// Get role IDs
	var userRole, adminRole, driverRole models.Role
	if err := DB.Where("name = ?", "user").First(&userRole).Error; err != nil {
		return nil, fmt.Errorf("failed to get user role: %w", err)
	}
	if err := DB.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		return nil, fmt.Errorf("failed to get admin role: %w", err)
	}
	if err := DB.Where("name = ?", "driver").First(&driverRole).Error; err != nil {
		return nil, fmt.Errorf("failed to get driver role: %w", err)
	}

	// Create test password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	users := []models.User{
		{
			ID:           uuid.New().String(),
			Email:        "<EMAIL>",
			FullName:     "Test User",
			PhoneNumber:  "+************",
			PasswordHash: string(hashedPassword),
		},
		{
			ID:           uuid.New().String(),
			Email:        "<EMAIL>",
			FullName:     "Admin User",
			PhoneNumber:  "+************",
			PasswordHash: string(hashedPassword),
		},
		{
			ID:           uuid.New().String(),
			Email:        "<EMAIL>",
			FullName:     "Driver User",
			PhoneNumber:  "+************",
			PasswordHash: string(hashedPassword),
		},
		{
			ID:           uuid.New().String(),
			Email:        "<EMAIL>",
			FullName:     "Second User",
			PhoneNumber:  "+************",
			PasswordHash: string(hashedPassword),
		},
	}

	// Create users
	for _, user := range users {
		if err := DB.Create(&user).Error; err != nil {
			return nil, fmt.Errorf("failed to create user %s: %w", user.Email, err)
		}
	}

	// Assign roles
	userRoles := []models.UserRole{
		{UserID: users[0].ID, RoleID: userRole.ID},
		{UserID: users[1].ID, RoleID: adminRole.ID},
		{UserID: users[1].ID, RoleID: userRole.ID}, // Admin also has user role
		{UserID: users[2].ID, RoleID: driverRole.ID},
		{UserID: users[2].ID, RoleID: userRole.ID}, // Driver also has user role
		{UserID: users[3].ID, RoleID: userRole.ID},
	}

	for _, userRole := range userRoles {
		if err := DB.Create(&userRole).Error; err != nil {
			return nil, fmt.Errorf("failed to assign role to user: %w", err)
		}
	}

	return users, nil
}

func seedRestaurants() ([]models.Restaurant, error) {
	restaurants := []models.Restaurant{
		{
			Name:        "Tasty Bites",
			Description: "Delicious local cuisine",
			Address:     "123 Main St, Nairobi",
			PhoneNumber: "+************",
			Email:       "<EMAIL>",
			Logo:        "https://example.com/images/restaurant1_logo.jpg",
			CoverImage:  "https://example.com/images/restaurant1.jpg",
			Latitude:    -1.286389,
			Longitude:   36.817223,
			OpeningTime: "08:00",
			ClosingTime: "20:00",
			IsActive:    true,
		},
		{
			Name:        "Spice Garden",
			Description: "Authentic Indian cuisine",
			Address:     "456 Park Ave, Nairobi",
			PhoneNumber: "+************",
			Email:       "<EMAIL>",
			Logo:        "https://example.com/images/restaurant2_logo.jpg",
			CoverImage:  "https://example.com/images/restaurant2.jpg",
			Latitude:    -1.289389,
			Longitude:   36.820223,
			OpeningTime: "09:00",
			ClosingTime: "21:00",
			IsActive:    true,
		},
		{
			Name:        "Burger Palace",
			Description: "Best burgers in town",
			Address:     "789 Oak St, Nairobi",
			PhoneNumber: "+************",
			Email:       "<EMAIL>",
			Logo:        "https://example.com/images/restaurant3_logo.jpg",
			CoverImage:  "https://example.com/images/restaurant3.jpg",
			Latitude:    -1.292389,
			Longitude:   36.823223,
			OpeningTime: "10:00",
			ClosingTime: "22:00",
			IsActive:    true,
		},
	}

	for _, restaurant := range restaurants {
		if err := DB.Create(&restaurant).Error; err != nil {
			return nil, fmt.Errorf("failed to create restaurant %s: %w", restaurant.Name, err)
		}
	}

	return restaurants, nil
}

func seedMenuItems(restaurants []models.Restaurant) ([]models.MenuItem, error) {
	var menuItems []models.MenuItem

	// Restaurant 1 menu items
	menuItems = append(menuItems, []models.MenuItem{
		{
			RestaurantID: restaurants[0].ID,
			Name:         "Ugali and Sukuma Wiki",
			Description:  "Traditional Kenyan meal",
			Price:        250,
			Image:        "https://example.com/images/ugali.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[0].ID,
			Name:         "Nyama Choma",
			Description:  "Grilled meat with sides",
			Price:        450,
			Image:        "https://example.com/images/nyama.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[0].ID,
			Name:         "Mandazi",
			Description:  "Sweet fried bread",
			Price:        50,
			Image:        "https://example.com/images/mandazi.jpg",
			Category:     "Dessert",
			IsAvailable:  true,
		},
	}...)

	// Restaurant 2 menu items
	menuItems = append(menuItems, []models.MenuItem{
		{
			RestaurantID: restaurants[1].ID,
			Name:         "Butter Chicken",
			Description:  "Creamy tomato curry with chicken",
			Price:        550,
			Image:        "https://example.com/images/butter_chicken.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[1].ID,
			Name:         "Vegetable Biryani",
			Description:  "Fragrant rice with vegetables",
			Price:        350,
			Image:        "https://example.com/images/biryani.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[1].ID,
			Name:         "Gulab Jamun",
			Description:  "Sweet milk dumplings",
			Price:        150,
			Image:        "https://example.com/images/gulab_jamun.jpg",
			Category:     "Dessert",
			IsAvailable:  true,
		},
	}...)

	// Restaurant 3 menu items
	menuItems = append(menuItems, []models.MenuItem{
		{
			RestaurantID: restaurants[2].ID,
			Name:         "Classic Burger",
			Description:  "Beef patty with lettuce, tomato, and cheese",
			Price:        450,
			Image:        "https://example.com/images/classic_burger.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[2].ID,
			Name:         "Chicken Burger",
			Description:  "Grilled chicken with special sauce",
			Price:        400,
			Image:        "https://example.com/images/chicken_burger.jpg",
			Category:     "Main Course",
			IsAvailable:  true,
		},
		{
			RestaurantID: restaurants[2].ID,
			Name:         "French Fries",
			Description:  "Crispy potato fries",
			Price:        200,
			Image:        "https://example.com/images/fries.jpg",
			Category:     "Side",
			IsAvailable:  true,
		},
	}...)

	for _, menuItem := range menuItems {
		if err := DB.Create(&menuItem).Error; err != nil {
			return nil, fmt.Errorf("failed to create menu item %s: %w", menuItem.Name, err)
		}
	}

	return menuItems, nil
}

func seedOrders(users []models.User, menuItems []models.MenuItem) ([]models.Order, error) {
	var orders []models.Order

	// Create a few orders for different users
	for i := 0; i < 2; i++ {
		userIndex := i % len(users)
		restaurantID := menuItems[i*3].RestaurantID // Get restaurant ID from menu items

		order := models.Order{
			ID:                uuid.New().String(),
			UserID:            users[userIndex].ID,
			RestaurantID:      restaurantID,
			Status:            "completed",
			TotalAmount:       0, // Will be calculated based on items
			DeliveryFee:       100,
			DeliveryAddress:   "123 Customer St, Nairobi",
			DeliveryLatitude:  -1.286389 + float64(i)*0.001,
			DeliveryLongitude: 36.817223 + float64(i)*0.001,
			Notes:             "Please deliver quickly",
			CreatedAt:         time.Now().Add(-time.Duration(i*24) * time.Hour), // Different days
		}

		// Add 2-3 items to each order
		var orderItems []models.OrderItem
		var totalAmount float64

		for j := 0; j < 2+rand.Intn(2); j++ {
			menuItemIndex := (i*3 + j) % len(menuItems) // Use menu items from the same restaurant
			quantity := 1 + rand.Intn(3)
			unitPrice := menuItems[menuItemIndex].Price
			subtotal := float64(quantity) * unitPrice

			orderItem := models.OrderItem{
				OrderID:    order.ID,
				MenuItemID: menuItems[menuItemIndex].ID,
				Quantity:   quantity,
				UnitPrice:  unitPrice,
				Subtotal:   subtotal,
				Notes:      "No special instructions",
			}

			orderItems = append(orderItems, orderItem)
			totalAmount += subtotal
		}

		order.TotalAmount = totalAmount + order.DeliveryFee

		// Save order
		if err := DB.Create(&order).Error; err != nil {
			return nil, fmt.Errorf("failed to create order: %w", err)
		}

		// Save order items
		for _, item := range orderItems {
			if err := DB.Create(&item).Error; err != nil {
				return nil, fmt.Errorf("failed to create order item: %w", err)
			}
		}

		orders = append(orders, order)
	}

	return orders, nil
}

func seedDrivers(users []models.User) ([]models.Driver, error) {
	// Find the user with driver role
	var driverUser models.User

	if err := DB.Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("roles.name = ?", "driver").
		First(&driverUser).Error; err != nil {
		return nil, fmt.Errorf("failed to find driver user: %w", err)
	}

	drivers := []models.Driver{
		{
			ID:                 uuid.New().String(),
			UserID:             driverUser.ID,
			LicenseNumber:      "**********",
			IdNumber:           "12345678",
			Rating:             4.5,
			IsAvailable:        true,
			IsVerified:         true,
			CurrentLatitude:    -1.286389,
			CurrentLongitude:   36.817223,
			LastLocationUpdate: time.Now(),
		},
	}

	// Create another driver from a regular user
	regularUserDriver := models.Driver{
		ID:                 uuid.New().String(),
		UserID:             users[3].ID, // Using the second regular user
		LicenseNumber:      "**********",
		IdNumber:           "87654321",
		Rating:             4.0,
		IsAvailable:        false,
		IsVerified:         true,
		CurrentLatitude:    -1.289389,
		CurrentLongitude:   36.820223,
		LastLocationUpdate: time.Now().Add(-1 * time.Hour),
	}
	drivers = append(drivers, regularUserDriver)

	// Assign driver role to this user
	var driverRole models.Role
	if err := DB.Where("name = ?", "driver").First(&driverRole).Error; err != nil {
		return nil, fmt.Errorf("failed to get driver role: %w", err)
	}

	driverRoleAssignment := models.UserRole{
		UserID: users[3].ID,
		RoleID: driverRole.ID,
	}

	if err := DB.Create(&driverRoleAssignment).Error; err != nil {
		return nil, fmt.Errorf("failed to assign driver role: %w", err)
	}

	for _, driver := range drivers {
		if err := DB.Create(&driver).Error; err != nil {
			return nil, fmt.Errorf("failed to create driver: %w", err)
		}
	}

	return drivers, nil
}

func seedVehicles(drivers []models.Driver) error {
	vehicles := []models.Vehicle{
		{
			DriverID:     drivers[0].ID,
			Type:         "motorcycle",
			Make:         "Honda",
			Model:        "CG125",
			Year:         2020,
			Color:        "Red",
			LicensePlate: "KAA 123B",
			IsActive:     true,
		},
		{
			DriverID:     drivers[1].ID,
			Type:         "motorcycle",
			Make:         "Yamaha",
			Model:        "YBR125",
			Year:         2019,
			Color:        "Blue",
			LicensePlate: "KBB 456C",
			IsActive:     true,
		},
	}

	for _, vehicle := range vehicles {
		if err := DB.Create(&vehicle).Error; err != nil {
			return fmt.Errorf("failed to create vehicle: %w", err)
		}
	}

	return nil
}

func seedRides(users []models.User, drivers []models.Driver) ([]models.Ride, error) {
	var rides []models.Ride

	// Get vehicle IDs
	var vehicles []models.Vehicle
	if err := DB.Find(&vehicles).Error; err != nil {
		return nil, fmt.Errorf("failed to get vehicles: %w", err)
	}

	// Create a few rides for different users
	for i := 0; i < 2; i++ {
		userIndex := i % len(users)
		driverIndex := i % len(drivers)
		vehicleIndex := i % len(vehicles)

		// Create different statuses for rides
		statuses := []string{"completed", "in_progress", "cancelled", "requested"}
		status := statuses[i%len(statuses)]

		ride := models.Ride{
			ID:               uuid.New().String(),
			UserID:           users[userIndex].ID,
			DriverID:         &drivers[driverIndex].ID,
			VehicleID:        &vehicles[vehicleIndex].ID,
			Status:           models.RideStatus(status),
			EstimatedFare:    200 + float64(i*50),
			ActualFare:       200 + float64(i*50),
			Distance:         5.0 + float64(i),
			Duration:         15 + i*5,
			PickupAddress:    "123 Pickup St, Nairobi",
			PickupLatitude:   -1.286389 + float64(i)*0.001,
			PickupLongitude:  36.817223 + float64(i)*0.001,
			DropoffAddress:   "456 Dropoff St, Nairobi",
			DropoffLatitude:  -1.296389 + float64(i)*0.001,
			DropoffLongitude: 36.827223 + float64(i)*0.001,
			CreatedAt:        time.Now().Add(-time.Duration(i*24) * time.Hour), // Different days
		}

		if status == "completed" {
			ride.AcceptedAt = utils.TimePtr(time.Now().Add(-time.Duration(i*23) * time.Hour))
			ride.EnRouteToPickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*22)*time.Hour + 15*time.Minute))
			ride.ArrivedAtPickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*22)*time.Hour + 30*time.Minute))
			ride.PickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*22) * time.Hour))
			ride.DropoffAt = utils.TimePtr(time.Now().Add(-time.Duration(i*21) * time.Hour))
			ride.PaymentStatus = models.PaymentStatusCompleted
		} else if status == "in_progress" {
			ride.AcceptedAt = utils.TimePtr(time.Now().Add(-time.Duration(i*1) * time.Hour))
			ride.EnRouteToPickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*45) * time.Minute))
			ride.ArrivedAtPickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*35) * time.Minute))
			ride.PickupAt = utils.TimePtr(time.Now().Add(-time.Duration(i*30) * time.Minute))
			ride.PaymentStatus = models.PaymentStatusPending
		} else if status == "cancelled" {
			ride.CancelledAt = utils.TimePtr(time.Now().Add(-time.Duration(i*23) * time.Hour))
			ride.Notes = "Changed plans"
			ride.PaymentStatus = models.PaymentStatusPending
		} else {
			ride.PaymentStatus = models.PaymentStatusPending
		}

		if err := DB.Create(&ride).Error; err != nil {
			return nil, fmt.Errorf("failed to create ride: %w", err)
		}

		rides = append(rides, ride)
	}

	return rides, nil
}

func seedPayments(users []models.User, orders []models.Order, rides []models.Ride) error {
	// Create payments for orders
	for i, order := range orders {
		if order.Status == "completed" {
			payment := models.Payment{
				ID:            uuid.New().String(),
				UserID:        order.UserID,
				OrderID:       order.ID,
				Amount:        order.TotalAmount,
				Currency:      "KES",
				Status:        "completed",
				PaymentMethod: "mpesa",
				TransactionID: fmt.Sprintf("TRX%d", i+1000),
				PaymentDate:   time.Now().Add(-time.Duration(i*23) * time.Hour),
			}

			if err := DB.Create(&payment).Error; err != nil {
				return fmt.Errorf("failed to create payment for order: %w", err)
			}
		}
	}

	// Create payments for rides
	for i, ride := range rides {
		if ride.Status == models.RideStatusCompleted {
			payment := models.Payment{
				ID:            uuid.New().String(),
				UserID:        ride.UserID,
				RideID:        ride.ID,
				Amount:        ride.ActualFare,
				Currency:      "KES",
				Status:        "completed",
				PaymentMethod: "mpesa",
				TransactionID: fmt.Sprintf("TRX%d", i+2000),
				PaymentDate:   time.Now().Add(-time.Duration(i*22) * time.Hour),
			}

			if err := DB.Create(&payment).Error; err != nil {
				return fmt.Errorf("failed to create payment for ride: %w", err)
			}
		}
	}

	return nil
}
