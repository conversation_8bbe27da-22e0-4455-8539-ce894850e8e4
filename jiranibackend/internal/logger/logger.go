package logger

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// Level represents the severity level of a log message
type Level int

const (
	// Debug level for detailed information
	Debug Level = iota
	// Info level for general operational information
	Info
	// Warn level for non-critical issues
	Warn
	// Error level for errors that should be investigated
	Error
	// Fatal level for critical errors that require immediate attention
	Fatal
)

// String returns the string representation of a log level
func (l Level) String() string {
	switch l {
	case Debug:
		return "DEBUG"
	case Info:
		return "INFO"
	case Warn:
		return "WARN"
	case Error:
		return "ERROR"
	case Fatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger is a structured logger for the application
type Logger struct {
	level      Level
	output     io.Writer
	fileOutput io.Writer
	service    string
	env        string
}

// LogEntry represents a structured log entry
type LogEntry struct {
	Time       string                 `json:"time"`
	Level      string                 `json:"level"`
	Message    string                 `json:"message"`
	Service    string                 `json:"service"`
	Environment string                `json:"environment"`
	Caller     string                 `json:"caller,omitempty"`
	File       string                 `json:"file,omitempty"`
	Line       int                    `json:"line,omitempty"`
	Data       map[string]interface{} `json:"data,omitempty"`
}

// New creates a new logger
func New(level Level, service, env string) *Logger {
	// Create logs directory if it doesn't exist
	os.MkdirAll("logs", 0755)

	// Create log file with date in name
	logFileName := fmt.Sprintf("logs/%s-%s.log", service, time.Now().Format("2006-01-02"))
	logFile, err := os.OpenFile(logFileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		fmt.Printf("Failed to open log file: %v\n", err)
		logFile = nil
	}

	return &Logger{
		level:      level,
		output:     os.Stdout,
		fileOutput: logFile,
		service:    service,
		env:        env,
	}
}

// log logs a message at the specified level
func (l *Logger) log(level Level, msg string, data map[string]interface{}) {
	if level < l.level {
		return
	}

	// Get caller information
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		file = "unknown"
		line = 0
	}

	// Format caller information
	caller := filepath.Base(file)
	if idx := strings.LastIndex(caller, "."); idx > 0 {
		caller = caller[:idx]
	}

	// Create log entry
	entry := LogEntry{
		Time:        time.Now().UTC().Format(time.RFC3339Nano),
		Level:       level.String(),
		Message:     msg,
		Service:     l.service,
		Environment: l.env,
		Caller:      caller,
		File:        file,
		Line:        line,
		Data:        data,
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		fmt.Fprintf(l.output, "Error marshaling log entry: %v\n", err)
		return
	}

	// Write to stdout
	fmt.Fprintln(l.output, string(jsonData))

	// Write to file if available
	if l.fileOutput != nil {
		fmt.Fprintln(l.fileOutput, string(jsonData))
	}

	// Exit on fatal error
	if level == Fatal {
		os.Exit(1)
	}
}

// Debug logs a debug message
func (l *Logger) Debug(msg string, data map[string]interface{}) {
	l.log(Debug, msg, data)
}

// Info logs an info message
func (l *Logger) Info(msg string, data map[string]interface{}) {
	l.log(Info, msg, data)
}

// Warn logs a warning message
func (l *Logger) Warn(msg string, data map[string]interface{}) {
	l.log(Warn, msg, data)
}

// Error logs an error message
func (l *Logger) Error(msg string, err error, data map[string]interface{}) {
	if data == nil {
		data = make(map[string]interface{})
	}
	if err != nil {
		data["error"] = err.Error()
	}
	l.log(Error, msg, data)
}

// Fatal logs a fatal message and exits
func (l *Logger) Fatal(msg string, err error, data map[string]interface{}) {
	if data == nil {
		data = make(map[string]interface{})
	}
	if err != nil {
		data["error"] = err.Error()
	}
	l.log(Fatal, msg, data)
}

// WithField adds a field to the log data
func (l *Logger) WithField(key string, value interface{}) *Entry {
	return &Entry{
		logger: l,
		data:   map[string]interface{}{key: value},
	}
}

// WithFields adds multiple fields to the log data
func (l *Logger) WithFields(fields map[string]interface{}) *Entry {
	return &Entry{
		logger: l,
		data:   fields,
	}
}

// Entry represents a log entry with fields
type Entry struct {
	logger *Logger
	data   map[string]interface{}
}

// Debug logs a debug message with fields
func (e *Entry) Debug(msg string) {
	e.logger.log(Debug, msg, e.data)
}

// Info logs an info message with fields
func (e *Entry) Info(msg string) {
	e.logger.log(Info, msg, e.data)
}

// Warn logs a warning message with fields
func (e *Entry) Warn(msg string) {
	e.logger.log(Warn, msg, e.data)
}

// Error logs an error message with fields
func (e *Entry) Error(msg string, err error) {
	data := e.data
	if data == nil {
		data = make(map[string]interface{})
	}
	if err != nil {
		data["error"] = err.Error()
	}
	e.logger.log(Error, msg, data)
}

// Fatal logs a fatal message with fields and exits
func (e *Entry) Fatal(msg string, err error) {
	data := e.data
	if data == nil {
		data = make(map[string]interface{})
	}
	if err != nil {
		data["error"] = err.Error()
	}
	e.logger.log(Fatal, msg, data)
}
