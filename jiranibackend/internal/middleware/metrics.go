package middleware

import (
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics contains the prometheus metrics for the application
type Metrics struct {
	RequestsTotal      *prometheus.CounterVec
	RequestDuration    *prometheus.HistogramVec
	RequestsInProgress *prometheus.GaugeVec
	RequestSizeBytes   *prometheus.SummaryVec
	ResponseSizeBytes  *prometheus.SummaryVec
	ErrorsTotal        *prometheus.CounterVec
}

// NewMetrics creates a new Metrics instance with all the required prometheus metrics
func NewMetrics(namespace string) *Metrics {
	return &Metrics{
		RequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "http_requests_total",
				Help:      "Total number of HTTP requests",
			},
			[]string{"method", "path", "status"},
		),
		RequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Name:      "http_request_duration_seconds",
				Help:      "HTTP request duration in seconds",
				Buckets:   prometheus.DefBuckets,
			},
			[]string{"method", "path", "status"},
		),
		RequestsInProgress: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "http_requests_in_progress",
				Help:      "Number of HTTP requests currently in progress",
			},
			[]string{"method", "path"},
		),
		RequestSizeBytes: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Name:      "http_request_size_bytes",
				Help:      "HTTP request size in bytes",
			},
			[]string{"method", "path"},
		),
		ResponseSizeBytes: promauto.NewSummaryVec(
			prometheus.SummaryOpts{
				Namespace: namespace,
				Name:      "http_response_size_bytes",
				Help:      "HTTP response size in bytes",
			},
			[]string{"method", "path", "status"},
		),
		ErrorsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "http_errors_total",
				Help:      "Total number of HTTP errors",
			},
			[]string{"method", "path", "status"},
		),
	}
}

// PrometheusMiddleware returns a middleware that collects Prometheus metrics
func PrometheusMiddleware(metrics *Metrics) fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()
		method := c.Method()
		path := c.Route().Path

		// Track request in progress
		metrics.RequestsInProgress.WithLabelValues(method, path).Inc()
		defer metrics.RequestsInProgress.WithLabelValues(method, path).Dec()

		// Track request size
		metrics.RequestSizeBytes.WithLabelValues(method, path).Observe(float64(len(c.Request().Body())))

		// Process request
		err := c.Next()

		// Record metrics after request is processed
		status := strconv.Itoa(c.Response().StatusCode())
		duration := time.Since(start).Seconds()

		metrics.RequestsTotal.WithLabelValues(method, path, status).Inc()
		metrics.RequestDuration.WithLabelValues(method, path, status).Observe(duration)
		metrics.ResponseSizeBytes.WithLabelValues(method, path, status).Observe(float64(len(c.Response().Body())))

		// Track errors
		if c.Response().StatusCode() >= 400 {
			metrics.ErrorsTotal.WithLabelValues(method, path, status).Inc()
		}

		return err
	}
}
