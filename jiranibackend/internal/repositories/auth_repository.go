package repositories

import (
	"errors"

	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"gorm.io/gorm"
)

// AuthRepository handles database operations for authentication
type AuthRepository struct {
	db *gorm.DB
}

// NewAuthRepository creates a new auth repository
func NewAuthRepository() *AuthRepository {
	return &AuthRepository{
		db: database.DB,
	}
}

// FindUserByEmail finds a user by email
func (r *AuthRepository) FindUserByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// This is expected for new users during registration
			return nil, gorm.ErrRecordNotFound
		}
		return nil, err
	}
	return &user, nil
}

// FindUserByID finds a user by ID
func (r *AuthRepository) FindUserByID(id string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return &user, nil
}

// FindUserByPhoneNumber finds a user by phone number
func (r *AuthRepository) FindUserByPhoneNumber(phoneNumber string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("phone_number = ?", phoneNumber).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// This is expected for new users during registration
			return nil, gorm.ErrRecordNotFound
		}
		return nil, err
	}
	return &user, nil
}

// CreateUser creates a new user
func (r *AuthRepository) CreateUser(user *models.User) error {
	return r.db.Create(user).Error
}

// UpdateUser updates a user
func (r *AuthRepository) UpdateUser(user *models.User) error {
	return r.db.Save(user).Error
}

// GetUserRoles gets a user's roles
func (r *AuthRepository) GetUserRoles(user *models.User) ([]models.Role, error) {
	var roles []models.Role
	if err := r.db.Model(user).Association("Roles").Find(&roles); err != nil {
		return nil, err
	}
	return roles, nil
}

// FindRoleByName finds a role by name
func (r *AuthRepository) FindRoleByName(name string) (*models.Role, error) {
	var role models.Role
	if err := r.db.Where("name = ?", name).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("role not found")
		}
		return nil, err
	}
	return &role, nil
}

// CreateRole creates a new role
func (r *AuthRepository) CreateRole(role *models.Role) error {
	return r.db.Create(role).Error
}

// AssignRoleToUser assigns a role to a user
func (r *AuthRepository) AssignRoleToUser(userID string, roleID uint) error {
	userRole := models.UserRole{
		UserID: userID,
		RoleID: roleID,
	}
	return r.db.Create(&userRole).Error
}
