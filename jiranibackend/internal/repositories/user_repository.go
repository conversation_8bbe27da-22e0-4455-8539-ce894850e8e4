package repositories

import (
	"errors"

	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"gorm.io/gorm"
)

// UserRepository handles database operations for users
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository() *UserRepository {
	return &UserRepository{
		db: database.DB,
	}
}

// FindByID finds a user by ID
func (r *UserRepository) FindByID(id string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return &user, nil
}

// FindByEmail finds a user by email
func (r *UserRepository) FindByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return &user, nil
}

// FindByPhoneNumber finds a user by phone number
func (r *UserRepository) FindByPhoneNumber(phoneNumber string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("phone_number = ?", phoneNumber).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return &user, nil
}

// Create creates a new user
func (r *UserRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

// Update updates a user
func (r *UserRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

// GetUserRoles gets a user's roles
func (r *UserRepository) GetUserRoles(user *models.User) ([]models.Role, error) {
	var roles []models.Role
	if err := r.db.Model(user).Association("Roles").Find(&roles); err != nil {
		return nil, err
	}
	return roles, nil
}

// AssignRole assigns a role to a user
func (r *UserRepository) AssignRole(userID string, roleID uint) error {
	userRole := models.UserRole{
		UserID: userID,
		RoleID: roleID,
	}
	return r.db.Create(&userRole).Error
}

// FindRoleByName finds a role by name
func (r *UserRepository) FindRoleByName(name string) (*models.Role, error) {
	var role models.Role
	if err := r.db.Where("name = ?", name).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("role not found")
		}
		return nil, err
	}
	return &role, nil
}

// CreateRole creates a new role
func (r *UserRepository) CreateRole(role *models.Role) error {
	return r.db.Create(role).Error
}

// FindUserRoleByUserIDAndRoleID finds a user role by user ID and role ID
func (r *UserRepository) FindUserRoleByUserIDAndRoleID(userID string, roleID uint) (*models.UserRole, error) {
	var userRole models.UserRole
	if err := r.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user role not found")
		}
		return nil, err
	}
	return &userRole, nil
}

// GetUserLocations gets a user's locations
func (r *UserRepository) GetUserLocations(userID string) ([]models.Location, error) {
	var locations []models.Location
	if err := r.db.Where("user_id = ?", userID).Find(&locations).Error; err != nil {
		return nil, err
	}
	return locations, nil
}

// FindLocationByID finds a location by ID
func (r *UserRepository) FindLocationByID(id uint, userID string) (*models.Location, error) {
	var location models.Location
	if err := r.db.Where("id = ? AND user_id = ?", id, userID).First(&location).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("location not found")
		}
		return nil, err
	}
	return &location, nil
}

// CreateLocation creates a new location
func (r *UserRepository) CreateLocation(location *models.Location) error {
	return r.db.Create(location).Error
}

// UpdateLocation updates a location
func (r *UserRepository) UpdateLocation(location *models.Location) error {
	return r.db.Save(location).Error
}

// DeleteLocation deletes a location
func (r *UserRepository) DeleteLocation(location *models.Location) error {
	return r.db.Delete(location).Error
}

// UnsetDefaultLocations unsets all default locations for a user
func (r *UserRepository) UnsetDefaultLocations(userID string) error {
	return r.db.Model(&models.Location{}).Where("user_id = ?", userID).Update("is_default", false).Error
}
