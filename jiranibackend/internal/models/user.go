package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID           string `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email        string `gorm:"type:varchar(255);not null"`
	PasswordHash string `gorm:"type:varchar(255);not null"`
	FullName     string `gorm:"type:varchar(255);not null"`
	PhoneNumber  string `gorm:"type:varchar(20);not null"`
	ProfileImage string `gorm:"type:varchar(255)"`
	IsActive     bool   `gorm:"default:true"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	Roles           []Role                `gorm:"many2many:user_roles"`
	Orders          []Order               `gorm:"foreignKey:UserID"`
	Rides           []Ride                `gorm:"foreignKey:UserID"`
	Payments        []Payment             `gorm:"foreignKey:UserID"`
	Locations       []Location            `gorm:"foreignKey:UserID"`
	LocationHistory []UserLocationHistory `gorm:"foreignKey:UserID"`
}

// Role represents a user role in the system
type Role struct {
	ID          uint   `gorm:"primaryKey"`
	Name        string `gorm:"type:varchar(50);uniqueIndex;not null"`
	Description string `gorm:"type:varchar(255)"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time

	// Relationships
	Users []User `gorm:"many2many:user_roles"`
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID    string `gorm:"type:uuid;primaryKey"`
	RoleID    uint   `gorm:"primaryKey"`
	CreatedAt time.Time
}

// Location represents a saved location for a user
type Location struct {
	ID        uint    `gorm:"primaryKey"`
	UserID    string  `gorm:"type:uuid;index;not null"`
	Name      string  `gorm:"type:varchar(100);not null"`
	Address   string  `gorm:"type:varchar(255);not null"`
	Latitude  float64 `gorm:"type:decimal(10,7);not null"`
	Longitude float64 `gorm:"type:decimal(10,7);not null"`
	IsDefault bool    `gorm:"default:false"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time

	// Relationships
	User User `gorm:"foreignKey:UserID"`
}

// LocationType represents the type of location in history
type LocationType string

const (
	LocationTypePickup      LocationType = "pickup"
	LocationTypeDestination LocationType = "destination"
	LocationTypeFavorite    LocationType = "favorite"
	LocationTypeHome        LocationType = "home"
	LocationTypeWork        LocationType = "work"
)

// UserLocationHistory represents a user's location history for quick selection
type UserLocationHistory struct {
	ID           string       `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       string       `gorm:"type:uuid;not null;index"`
	Latitude     float64      `gorm:"type:decimal(10,8);not null"`
	Longitude    float64      `gorm:"type:decimal(11,8);not null"`
	Address      string       `gorm:"type:text;not null"`
	LocationType LocationType `gorm:"type:varchar(20);not null"`
	UsageCount   int          `gorm:"default:1"`
	LastUsedAt   time.Time    `gorm:"default:CURRENT_TIMESTAMP"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time

	// Relationships
	User User `gorm:"foreignKey:UserID"`
}

// TableName overrides the table name used by UserLocationHistory to `user_location_history`
func (UserLocationHistory) TableName() string {
	return "user_location_history"
}
