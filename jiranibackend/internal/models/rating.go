package models

import (
	"time"

	"gorm.io/gorm"
)

// RideRating represents a rating for a ride
type RideRating struct {
	ID          uint   `gorm:"primaryKey"`
	RideID      string `gorm:"type:uuid;uniqueIndex;not null"`
	UserID      string `gorm:"type:uuid;index;not null"`
	DriverID    string `gorm:"type:uuid;index;not null"`
	Rating      float32 `gorm:"type:decimal(3,2);not null"`
	Comment     string `gorm:"type:text"`
	
	// Specific ratings
	SafetyRating      float32 `gorm:"type:decimal(3,2)"`
	PunctualityRating float32 `gorm:"type:decimal(3,2)"`
	CleanlinessRating float32 `gorm:"type:decimal(3,2)"`
	
	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
	
	// Relationships
	Ride   Ride   `gorm:"foreignKey:RideID"`
	User   User   `gorm:"foreignKey:UserID"`
	Driver Driver `gorm:"foreignKey:DriverID"`
}

// DriverRating represents a rating for a driver (from the rider)
type DriverRating struct {
	ID          uint   `gorm:"primaryKey"`
	RideID      string `gorm:"type:uuid;uniqueIndex;not null"`
	UserID      string `gorm:"type:uuid;index;not null"`
	DriverID    string `gorm:"type:uuid;index;not null"`
	Rating      float32 `gorm:"type:decimal(3,2);not null"`
	Comment     string `gorm:"type:text"`
	
	// Specific ratings
	DrivingSkillRating float32 `gorm:"type:decimal(3,2)"`
	ProfessionalismRating float32 `gorm:"type:decimal(3,2)"`
	VehicleRating float32 `gorm:"type:decimal(3,2)"`
	
	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
	
	// Relationships
	Ride   Ride   `gorm:"foreignKey:RideID"`
	User   User   `gorm:"foreignKey:UserID"`
	Driver Driver `gorm:"foreignKey:DriverID"`
}

// RiderRating represents a rating for a rider (from the driver)
type RiderRating struct {
	ID          uint   `gorm:"primaryKey"`
	RideID      string `gorm:"type:uuid;uniqueIndex;not null"`
	UserID      string `gorm:"type:uuid;index;not null"`
	DriverID    string `gorm:"type:uuid;index;not null"`
	Rating      float32 `gorm:"type:decimal(3,2);not null"`
	Comment     string `gorm:"type:text"`
	
	// Specific ratings
	BehaviorRating float32 `gorm:"type:decimal(3,2)"`
	PunctualityRating float32 `gorm:"type:decimal(3,2)"`
	
	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
	
	// Relationships
	Ride   Ride   `gorm:"foreignKey:RideID"`
	User   User   `gorm:"foreignKey:UserID"`
	Driver Driver `gorm:"foreignKey:DriverID"`
}
