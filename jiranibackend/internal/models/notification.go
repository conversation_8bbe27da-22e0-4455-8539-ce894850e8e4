package models

import (
	"time"

	"gorm.io/gorm"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	// Ride notifications
	RideRequested     NotificationType = "ride_requested"
	RideAccepted      NotificationType = "ride_accepted"
	RideCancelled     NotificationType = "ride_cancelled"
	RideDriverArrived NotificationType = "ride_driver_arrived"
	RideStarted       NotificationType = "ride_started"
	RideCompleted     NotificationType = "ride_completed"
	
	// Payment notifications
	PaymentRequested  NotificationType = "payment_requested"
	PaymentSuccessful NotificationType = "payment_successful"
	PaymentFailed     NotificationType = "payment_failed"
	
	// Rating notifications
	RatingRequested   NotificationType = "rating_requested"
	
	// Emergency notifications
	EmergencyTriggered NotificationType = "emergency_triggered"
	
	// System notifications
	SystemMessage     NotificationType = "system_message"
)

// Notification represents a notification sent to a user
type Notification struct {
	ID        uint            `gorm:"primaryKey"`
	UserID    string          `gorm:"type:uuid;index;not null"`
	Type      NotificationType `gorm:"type:varchar(50);not null"`
	Title     string          `gorm:"type:varchar(255);not null"`
	Message   string          `gorm:"type:text;not null"`
	Data      string          `gorm:"type:jsonb"` // JSON data for the notification
	IsRead    bool            `gorm:"default:false"`
	ReadAt    *time.Time
	
	// Related entities
	RideID    string          `gorm:"type:uuid;index"`
	PaymentID string          `gorm:"type:uuid;index"`
	
	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
	
	// Relationships
	User    User    `gorm:"foreignKey:UserID"`
	Ride    Ride    `gorm:"foreignKey:RideID"`
	Payment Payment `gorm:"foreignKey:PaymentID"`
}

// DeviceToken represents a device token for push notifications
type DeviceToken struct {
	ID        uint   `gorm:"primaryKey"`
	UserID    string `gorm:"type:uuid;index;not null"`
	Token     string `gorm:"type:varchar(255);not null;uniqueIndex"`
	DeviceType string `gorm:"type:varchar(50);not null"` // ios, android, web
	IsActive  bool   `gorm:"default:true"`
	
	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	LastUsedAt time.Time
	
	// Relationships
	User User `gorm:"foreignKey:UserID"`
}
