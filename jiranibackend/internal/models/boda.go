package models

import (
	"time"

	"gorm.io/gorm"
)

// Driver represents a boda boda driver
type Driver struct {
	ID                 string  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID             string  `gorm:"type:uuid;uniqueIndex;not null"`
	LicenseNumber      string  `gorm:"type:varchar(50);uniqueIndex;not null"`
	IdNumber           string  `gorm:"type:varchar(20);uniqueIndex;not null"`
	Rating             float32 `gorm:"type:decimal(3,2);default:0"`
	IsAvailable        bool    `gorm:"default:true"`
	IsVerified         bool    `gorm:"default:false"`
	CurrentLatitude    float64 `gorm:"type:decimal(10,7)"`
	CurrentLongitude   float64 `gorm:"type:decimal(10,7)"`
	LastLocationUpdate time.Time

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	User     User      `gorm:"foreignKey:UserID"`
	Vehicles []Vehicle `gorm:"foreignKey:DriverID"`
	Rides    []Ride    `gorm:"foreignKey:DriverID"`
}

// Vehicle represents a driver's motor description
type Vehicle struct {
	ID           uint   `gorm:"primaryKey"`
	DriverID     string `gorm:"type:uuid;index;not null"`
	Type         string `gorm:"type:varchar(50);not null"`
	Make         string `gorm:"type:varchar(50);not null"`
	Model        string `gorm:"type:varchar(50);not null"`
	Year         int    `gorm:"not null"`
	Color        string `gorm:"type:varchar(20);not null"`
	LicensePlate string `gorm:"type:varchar(20);uniqueIndex;not null"`
	IsActive     bool   `gorm:"default:true"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	Driver Driver `gorm:"foreignKey:DriverID"`
	Rides  []Ride `gorm:"foreignKey:VehicleID"`
}

// RideStatus represents the status of a ride
type RideStatus string

const (
	RideStatusRequested       RideStatus = "requested"
	RideStatusAccepted        RideStatus = "accepted"
	RideStatusEnRouteToPickup RideStatus = "en_route_to_pickup"
	RideStatusArrivedAtPickup RideStatus = "arrived_at_pickup"
	RideStatusInProgress      RideStatus = "in_progress"
	RideStatusCompleted       RideStatus = "completed"
	RideStatusCancelled       RideStatus = "cancelled"
)

// PaymentStatus represents the status of a ride payment
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusRefunded  PaymentStatus = "refunded"
)

// Ride represents a boda boda ride
type Ride struct {
	ID               string        `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID           string        `gorm:"type:uuid;index;not null"`
	DriverID         *string       `gorm:"type:uuid;index"`
	VehicleID        *uint         `gorm:"index"`
	Status           RideStatus    `gorm:"type:varchar(20);not null;default:'requested'"`
	PickupAddress    string        `gorm:"type:varchar(255);not null"`
	PickupLatitude   float64       `gorm:"type:decimal(10,7);not null"`
	PickupLongitude  float64       `gorm:"type:decimal(10,7);not null"`
	DropoffAddress   string        `gorm:"type:varchar(255);not null"`
	DropoffLatitude  float64       `gorm:"type:decimal(10,7);not null"`
	DropoffLongitude float64       `gorm:"type:decimal(10,7);not null"`
	Distance         float64       `gorm:"type:decimal(10,2);not null"`   // in kilometers
	Duration         int           `gorm:"not null"`                      // in minutes
	EstimatedFare    float64       `gorm:"type:decimal(10,2);not null"`   // Estimated fare before ride
	ActualFare       float64       `gorm:"type:decimal(10,2)"`            // Actual fare after ride completion
	SurgeFactor      float64       `gorm:"type:decimal(3,2);default:1.0"` // Surge pricing factor
	PaymentMethod    string        `gorm:"type:varchar(50)"`
	PaymentStatus    PaymentStatus `gorm:"type:varchar(20);default:'pending'"`
	Notes            string        `gorm:"type:text"`

	// Current location tracking
	CurrentLatitude    float64 `gorm:"type:decimal(10,7)"`
	CurrentLongitude   float64 `gorm:"type:decimal(10,7)"`
	LastLocationUpdate time.Time

	// ETA information
	EstimatedPickupTime  *time.Time
	EstimatedArrivalTime *time.Time

	// Emergency flag
	EmergencyTriggered bool   `gorm:"default:false"`
	EmergencyDetails   string `gorm:"type:text"`
	EmergencyTime      *time.Time

	// Timestamps
	CreatedAt         time.Time
	UpdatedAt         time.Time
	AcceptedAt        *time.Time
	EnRouteToPickupAt *time.Time
	ArrivedAtPickupAt *time.Time
	PickupAt          *time.Time
	DropoffAt         *time.Time
	CancelledAt       *time.Time
	DeletedAt         gorm.DeletedAt `gorm:"index"`

	// Relationships
	User    User    `gorm:"foreignKey:UserID"`
	Driver  Driver  `gorm:"foreignKey:DriverID"`
	Vehicle Vehicle `gorm:"foreignKey:VehicleID"`
	// Remove Payment relationship to break circular dependency
	// Payment will still reference Ride
}
