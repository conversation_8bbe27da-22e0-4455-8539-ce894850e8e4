package models

import (
	"time"

	"gorm.io/gorm"
)

// Restaurant represents a food vendor
type Restaurant struct {
	ID          uint    `gorm:"primaryKey"`
	Name        string  `gorm:"type:varchar(100);not null"`
	Description string  `gorm:"type:text"`
	Address     string  `gorm:"type:varchar(255);not null"`
	PhoneNumber string  `gorm:"type:varchar(20);not null"`
	Email       string  `gorm:"type:varchar(255)"`
	Logo        string  `gorm:"type:varchar(255)"`
	CoverImage  string  `gorm:"type:varchar(255)"`
	Latitude    float64 `gorm:"type:decimal(10,7);not null"`
	Longitude   float64 `gorm:"type:decimal(10,7);not null"`
	IsActive    bool    `gorm:"default:true"`

	// Opening hours
	OpeningTime string `gorm:"type:varchar(5);not null;default:'08:00'"`
	ClosingTime string `gorm:"type:varchar(5);not null;default:'20:00'"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	MenuItems []MenuItem `gorm:"foreignKey:RestaurantID"`
	Orders    []Order    `gorm:"foreignKey:RestaurantID"`
}

// MenuItem represents a food item on a restaurant's menu
type MenuItem struct {
	ID           uint    `gorm:"primaryKey"`
	RestaurantID uint    `gorm:"index;not null"`
	Name         string  `gorm:"type:varchar(100);not null"`
	Description  string  `gorm:"type:text"`
	Price        float64 `gorm:"type:decimal(10,2);not null"`
	Image        string  `gorm:"type:varchar(255)"`
	Category     string  `gorm:"type:varchar(50);not null"`
	IsAvailable  bool    `gorm:"default:true"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	Restaurant Restaurant  `gorm:"foreignKey:RestaurantID"`
	OrderItems []OrderItem `gorm:"foreignKey:MenuItemID"`
}

// Order represents a food order
type Order struct {
	ID                string  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID            string  `gorm:"type:uuid;index;not null"`
	RestaurantID      uint    `gorm:"index;not null"`
	Status            string  `gorm:"type:varchar(20);not null;default:'pending'"`
	TotalAmount       float64 `gorm:"type:decimal(10,2);not null"`
	DeliveryFee       float64 `gorm:"type:decimal(10,2);not null"`
	DeliveryAddress   string  `gorm:"type:varchar(255);not null"`
	DeliveryLatitude  float64 `gorm:"type:decimal(10,7);not null"`
	DeliveryLongitude float64 `gorm:"type:decimal(10,7);not null"`
	Notes             string  `gorm:"type:text"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	User       User        `gorm:"foreignKey:UserID"`
	Restaurant Restaurant  `gorm:"foreignKey:RestaurantID"`
	OrderItems []OrderItem `gorm:"foreignKey:OrderID"`
	// Remove Payment relationship to break circular dependency
	// Payment will still reference Order
}

// OrderItem represents an item in an order
type OrderItem struct {
	ID         uint    `gorm:"primaryKey"`
	OrderID    string  `gorm:"type:uuid;index;not null"`
	MenuItemID uint    `gorm:"index;not null"`
	Quantity   int     `gorm:"not null"`
	UnitPrice  float64 `gorm:"type:decimal(10,2);not null"`
	Subtotal   float64 `gorm:"type:decimal(10,2);not null"`
	Notes      string  `gorm:"type:text"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time

	// Relationships
	Order    Order    `gorm:"foreignKey:OrderID"`
	MenuItem MenuItem `gorm:"foreignKey:MenuItemID"`
}
