package models

import (
	"time"

	"gorm.io/gorm"
)

// Payment represents a payment transaction
type Payment struct {
	ID            string  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID        string  `gorm:"type:uuid;index;not null"`
	OrderID       string  `gorm:"type:uuid;index"`
	RideID        string  `gorm:"type:uuid;index"`
	Amount        float64 `gorm:"type:decimal(10,2);not null"`
	Currency      string  `gorm:"type:varchar(3);not null;default:'KES'"`
	PaymentMethod string  `gorm:"type:varchar(50);not null"`
	Status        string  `gorm:"type:varchar(20);not null;default:'pending'"`
	TransactionID string  `gorm:"type:varchar(100);uniqueIndex"`
	PaymentDate   time.Time

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Relationships
	User  User  `gorm:"foreignKey:UserID"`
	Order Order `gorm:"foreignKey:OrderID"`
	Ride  Ride  `gorm:"foreignKey:RideID"`
}
