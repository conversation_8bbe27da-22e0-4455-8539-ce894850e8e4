package utils

import (
	"math"
)

// CalculateDistance calculates the distance between two points in kilometers using the Haversine formula
func CalculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const earthRadius = 6371.0 // Earth radius in kilometers

	// Convert latitude and longitude from degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	// Haversine formula
	dLat := lat2Rad - lat1Rad
	dLon := lon2Rad - lon1Rad
	a := math.Sin(dLat/2)*math.Sin(dLat/2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Sin(dLon/2)*math.Sin(dLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadius * c

	return distance
}

// CalculateETA calculates the estimated time of arrival in minutes based on distance and average speed
func CalculateETA(distanceKm float64, averageSpeedKmh float64) int {
	if averageSpeedKmh <= 0 {
		averageSpeedKmh = 30.0 // Default average speed in km/h
	}
	
	// Calculate time in hours
	timeHours := distanceKm / averageSpeedKmh
	
	// Convert to minutes
	timeMinutes := int(timeHours * 60)
	
	return timeMinutes
}

// GetBoundingBox returns a bounding box around a point with the given radius in kilometers
func GetBoundingBox(lat, lon float64, radiusKm float64) (minLat, minLon, maxLat, maxLon float64) {
	// Earth's radius in kilometers
	const earthRadius = 6371.0
	
	// Convert latitude and longitude from degrees to radians
	latRad := lat * math.Pi / 180
	
	// Angular distance in radians on a great circle
	radDist := radiusKm / earthRadius
	
	// Calculate min and max latitudes
	minLat = lat - (radDist * 180 / math.Pi)
	maxLat = lat + (radDist * 180 / math.Pi)
	
	// Calculate min and max longitudes
	// The delta longitude gets smaller as we move away from the equator
	deltaLon := math.Asin(math.Sin(radDist) / math.Cos(latRad)) * 180 / math.Pi
	minLon = lon - deltaLon
	maxLon = lon + deltaLon
	
	// Ensure valid latitude and longitude values
	if minLat < -90 {
		minLat = -90
	}
	if maxLat > 90 {
		maxLat = 90
	}
	if minLon < -180 {
		minLon = -180
	}
	if maxLon > 180 {
		maxLon = 180
	}
	
	return minLat, minLon, maxLat, maxLon
}
