package utils

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
)

// Validate is a validator instance
var Validate = validator.New()

// ValidatorErrors formats validation errors
func ValidatorErrors(err error) map[string]string {
	fields := map[string]string{}

	for _, err := range err.(validator.ValidationErrors) {
		field := strings.ToLower(err.Field())
		switch err.Tag() {
		case "required":
			fields[field] = fmt.Sprintf("The %s field is required", field)
		case "email":
			fields[field] = fmt.Sprintf("The %s field must be a valid email", field)
		case "min":
			fields[field] = fmt.Sprintf("The %s field must be at least %s characters", field, err.Param())
		case "max":
			fields[field] = fmt.Sprintf("The %s field must not exceed %s characters", field, err.Param())
		default:
			fields[field] = fmt.Sprintf("The %s field is invalid", field)
		}
	}

	return fields
}
