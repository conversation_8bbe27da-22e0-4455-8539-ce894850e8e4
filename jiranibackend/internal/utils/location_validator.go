package utils

import (
	"errors"
	"fmt"
	"math"
	"strings"
)

// LocationBounds represents geographical boundaries for Kenya
type LocationBounds struct {
	MinLatitude  float64
	MaxLatitude  float64
	MinLongitude float64
	MaxLongitude float64
}

// KenyaBounds defines the geographical boundaries of Kenya
var KenyaBounds = LocationBounds{
	MinLatitude:  -4.8, // Southern border (approximate)
	MaxLatitude:  5.0,  // Northern border (approximate)
	MinLongitude: 33.9, // Western border (approximate)
	MaxLongitude: 41.9, // Eastern border (approximate)
}

// NairobiBounds defines the boundaries of Nairobi and surrounding areas
var NairobiBounds = LocationBounds{
	MinLatitude:  -1.45, // South of Nairobi
	MaxLatitude:  -1.15, // North of Nairobi
	MinLongitude: 36.65, // West of Nairobi
	MaxLongitude: 37.1,  // East of Nairobi
}

// LocationValidationError represents location validation errors
type LocationValidationError struct {
	Field   string
	Message string
}

func (e LocationValidationError) Error() string {
	return e.Message
}

// ValidateLocationCoordinates validates latitude and longitude coordinates
func ValidateLocationCoordinates(latitude, longitude float64) error {
	// Check if coordinates are valid numbers
	if math.IsNaN(latitude) || math.IsNaN(longitude) {
		return LocationValidationError{
			Field:   "coordinates",
			Message: "Invalid coordinates: latitude or longitude is not a number",
		}
	}

	if math.IsInf(latitude, 0) || math.IsInf(longitude, 0) {
		return LocationValidationError{
			Field:   "coordinates",
			Message: "Invalid coordinates: latitude or longitude is infinite",
		}
	}

	// Check latitude bounds (-90 to 90)
	if latitude < -90 || latitude > 90 {
		return LocationValidationError{
			Field:   "latitude",
			Message: "Latitude must be between -90 and 90 degrees",
		}
	}

	// Check longitude bounds (-180 to 180)
	if longitude < -180 || longitude > 180 {
		return LocationValidationError{
			Field:   "longitude",
			Message: "Longitude must be between -180 and 180 degrees",
		}
	}

	return nil
}

// ValidateLocationInKenya validates if coordinates are within Kenya's boundaries
func ValidateLocationInKenya(latitude, longitude float64) error {
	// First validate basic coordinates
	if err := ValidateLocationCoordinates(latitude, longitude); err != nil {
		return err
	}

	// Check if location is within Kenya's boundaries
	if latitude < KenyaBounds.MinLatitude || latitude > KenyaBounds.MaxLatitude ||
		longitude < KenyaBounds.MinLongitude || longitude > KenyaBounds.MaxLongitude {
		return LocationValidationError{
			Field:   "location",
			Message: "Location must be within Kenya's geographical boundaries",
		}
	}

	return nil
}

// ValidateLocationInNairobi validates if coordinates are within Nairobi area
func ValidateLocationInNairobi(latitude, longitude float64) error {
	// First validate basic coordinates
	if err := ValidateLocationCoordinates(latitude, longitude); err != nil {
		return err
	}

	// Check if location is within Nairobi's boundaries
	if latitude < NairobiBounds.MinLatitude || latitude > NairobiBounds.MaxLatitude ||
		longitude < NairobiBounds.MinLongitude || longitude > NairobiBounds.MaxLongitude {
		return LocationValidationError{
			Field:   "location",
			Message: "Location must be within Nairobi area for BodaBoda service",
		}
	}

	return nil
}

// Note: CalculateDistance function is available in geo_utils.go

// ValidateLocationAccuracy validates if two locations are within acceptable accuracy range
func ValidateLocationAccuracy(lat1, lon1, lat2, lon2 float64, maxDistanceKm float64) error {
	distance := CalculateDistance(lat1, lon1, lat2, lon2)

	if distance > maxDistanceKm {
		return LocationValidationError{
			Field:   "accuracy",
			Message: "Location accuracy is too low - coordinates are too far apart",
		}
	}

	return nil
}

// ValidatePickupDropoffDistance validates the distance between pickup and dropoff locations
func ValidatePickupDropoffDistance(pickupLat, pickupLon, dropoffLat, dropoffLon float64) error {
	// Validate individual coordinates
	if err := ValidateLocationInKenya(pickupLat, pickupLon); err != nil {
		return errors.New("invalid pickup location: " + err.Error())
	}

	if err := ValidateLocationInKenya(dropoffLat, dropoffLon); err != nil {
		return errors.New("invalid dropoff location: " + err.Error())
	}

	// Calculate distance
	distance := CalculateDistance(pickupLat, pickupLon, dropoffLat, dropoffLon)

	// Validate minimum distance (prevent very short rides)
	if distance < 0.1 { // 100 meters minimum
		return LocationValidationError{
			Field:   "distance",
			Message: "Pickup and dropoff locations are too close (minimum 100 meters)",
		}
	}

	// Validate maximum distance (prevent extremely long rides)
	if distance > 100 { // 100 km maximum
		return LocationValidationError{
			Field:   "distance",
			Message: "Pickup and dropoff locations are too far apart (maximum 100 km)",
		}
	}

	return nil
}

// ValidateDriverLocation validates driver location for service availability
func ValidateDriverLocation(latitude, longitude float64) error {
	// For now, validate within Nairobi area
	// This can be expanded to other service areas in the future
	return ValidateLocationInNairobi(latitude, longitude)
}

// IsLocationSimilar checks if two locations are similar (within 50 meters)
func IsLocationSimilar(lat1, lon1, lat2, lon2 float64) bool {
	distance := CalculateDistance(lat1, lon1, lat2, lon2)
	return distance < 0.05 // 50 meters
}

// GetLocationPrecision returns the precision level of coordinates
func GetLocationPrecision(latitude, longitude float64) string {
	// Count decimal places to determine precision
	latStr := fmt.Sprintf("%.10f", latitude)
	lonStr := fmt.Sprintf("%.10f", longitude)

	latDecimals := len(strings.Split(latStr, ".")[1])
	lonDecimals := len(strings.Split(lonStr, ".")[1])

	minDecimals := latDecimals
	if lonDecimals < latDecimals {
		minDecimals = lonDecimals
	}

	switch {
	case minDecimals >= 6:
		return "high" // ~0.1 meter accuracy
	case minDecimals >= 4:
		return "medium" // ~10 meter accuracy
	case minDecimals >= 2:
		return "low" // ~1 km accuracy
	default:
		return "very_low" // >1 km accuracy
	}
}
