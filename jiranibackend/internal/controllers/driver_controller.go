package controllers

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
	"github.com/jirani/backend/internal/websocket"
)

// RegisterAsDriver registers a user as a driver
func RegisterAsDriver(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req RegisterAsDriverRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Check if user is already a driver
	var existingDriver models.Driver
	result := database.DB.Where("user_id = ?", userID).First(&existingDriver)
	if result.RowsAffected > 0 {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "User is already registered as a driver",
		})
	}

	// Check if license number is already taken
	result = database.DB.Where("license_number = ?", req.LicenseNumber).First(&existingDriver)
	if result.RowsAffected > 0 {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "License number is already registered",
		})
	}

	// Check if ID number is already taken
	result = database.DB.Where("id_number = ?", req.IdNumber).First(&existingDriver)
	if result.RowsAffected > 0 {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "ID number is already registered",
		})
	}

	// Create driver
	driver := models.Driver{
		UserID:        userID,
		LicenseNumber: req.LicenseNumber,
		IdNumber:      req.IdNumber,
		IsAvailable:   false,
		IsVerified:    false,
	}

	if err := database.DB.Create(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to register as driver",
		})
	}

	// Assign driver role to user
	var driverRole models.Role
	if err := database.DB.Where("name = ?", "driver").First(&driverRole).Error; err != nil {
		// Create the role if it doesn't exist
		driverRole = models.Role{
			Name:        "driver",
			Description: "Boda boda driver",
		}
		if err := database.DB.Create(&driverRole).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to create driver role",
			})
		}
	}

	// Check if user already has the driver role
	var userRole models.UserRole
	result = database.DB.Where("user_id = ? AND role_id = ?", userID, driverRole.ID).First(&userRole)
	if result.RowsAffected == 0 {
		// Assign role to user
		if err := database.DB.Create(&models.UserRole{
			UserID: userID,
			RoleID: driverRole.ID,
		}).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to assign driver role",
			})
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Registered as driver successfully. Your account is pending verification.",
		"driver":  driver,
	})
}

// GetDriverProfile returns the authenticated driver's profile
func GetDriverProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Load user
	database.DB.Model(&driver).Association("User").Find(&driver.User)

	return c.JSON(fiber.Map{
		"driver": driver,
	})
}

// UpdateDriverProfile updates the authenticated driver's profile
func UpdateDriverProfile(c *fiber.Ctx) error {
	// This is a placeholder - implement driver profile update logic
	return c.JSON(fiber.Map{
		"message": "Driver profile updated successfully",
	})
}

// AddDriverVehicle adds a new vehicle for the authenticated driver
func AddDriverVehicle(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req AddVehicleRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Check if license plate is already registered
	var existingVehicle models.Vehicle
	result := database.DB.Where("license_plate = ?", req.LicensePlate).First(&existingVehicle)
	if result.RowsAffected > 0 {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "License plate is already registered",
		})
	}

	// Create vehicle
	vehicle := models.Vehicle{
		DriverID:     driver.ID,
		Type:         req.Type,
		Make:         req.Make,
		Model:        req.Model,
		Year:         req.Year,
		Color:        req.Color,
		LicensePlate: req.LicensePlate,
		IsActive:     true,
	}

	if err := database.DB.Create(&vehicle).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to add vehicle",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Vehicle added successfully",
		"vehicle": vehicle,
	})
}

// GetDriverVehicles returns all vehicles for the authenticated driver
func GetDriverVehicles(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Get vehicles
	var vehicles []models.Vehicle
	if err := database.DB.Where("driver_id = ?", driver.ID).Find(&vehicles).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get vehicles",
		})
	}

	return c.JSON(fiber.Map{
		"vehicles": vehicles,
	})
}

// UpdateDriverStatus updates the driver's availability status
func UpdateDriverStatus(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req struct {
		IsAvailable bool `json:"is_available"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Check if driver is verified
	if !driver.IsVerified {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Driver is not verified",
		})
	}

	// Update status
	driver.IsAvailable = req.IsAvailable
	if err := database.DB.Save(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update status",
		})
	}

	return c.JSON(fiber.Map{
		"message":      "Status updated successfully",
		"is_available": driver.IsAvailable,
	})
}

// UpdateDriverLocation updates the driver's current location
func UpdateDriverLocation(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req UpdateDriverLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Update location
	driver.CurrentLatitude = req.Latitude
	driver.CurrentLongitude = req.Longitude
	driver.LastLocationUpdate = time.Now()
	if err := database.DB.Save(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update location",
		})
	}

	// Broadcast driver location update via WebSocket for any active rides
	// Find any active rides for this driver
	var activeRides []models.Ride
	database.DB.Where("driver_id = ? AND status IN (?)", driver.ID, []string{
		string(models.RideStatusAccepted),
		string(models.RideStatusEnRouteToPickup),
		string(models.RideStatusArrivedAtPickup),
		string(models.RideStatusInProgress),
	}).Find(&activeRides)

	// Broadcast location update for each active ride
	for _, ride := range activeRides {
		locationData := map[string]interface{}{
			"driver_id": driver.ID,
			"user_id":   driver.UserID,
			"latitude":  req.Latitude,
			"longitude": req.Longitude,
			"timestamp": time.Now().Unix(),
			"ride_id":   ride.ID,
		}
		// Broadcast location update via WebSocket
		websocket.BroadcastLocationUpdate(ride.ID, locationData)
	}

	return c.JSON(fiber.Map{
		"message": "Location updated successfully",
	})
}

// GetDriverRides returns all rides for the authenticated driver
func GetDriverRides(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Get rides
	var rides []models.Ride
	if err := database.DB.Where("driver_id = ?", driver.ID).Order("created_at DESC").Find(&rides).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get rides",
		})
	}

	return c.JSON(fiber.Map{
		"rides": rides,
	})
}

// AcceptRide accepts a ride request
func AcceptRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Parse request body
	var req struct {
		VehicleID uint `json:"vehicle_id" validate:"required"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Check if driver is verified and available
	if !driver.IsVerified {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Driver is not verified",
		})
	}
	if !driver.IsAvailable {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Driver is not available",
		})
	}

	// Check if vehicle belongs to driver
	var vehicle models.Vehicle
	if err := database.DB.Where("id = ? AND driver_id = ?", req.VehicleID, driver.ID).First(&vehicle).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Vehicle not found",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND status = ?", rideID, "requested").First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or already accepted",
		})
	}

	// Update ride
	now := time.Now()
	ride.DriverID = &driver.ID
	ride.VehicleID = &vehicle.ID
	ride.Status = models.RideStatusAccepted
	ride.AcceptedAt = &now

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to accept ride",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Ride accepted successfully",
		"ride":    ride,
	})
}

// PickupRider marks a ride as picked up
func PickupRider(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND driver_id = ? AND status = ?", rideID, driver.ID, "accepted").First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or not in accepted status",
		})
	}

	// Update ride
	now := time.Now()
	ride.Status = models.RideStatusInProgress
	ride.PickupAt = &now

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update ride",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Rider picked up successfully",
		"ride":    ride,
	})
}

// CompleteRide marks a ride as completed
func CompleteRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND driver_id = ? AND status = ?", rideID, driver.ID, "in_progress").First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or not in progress",
		})
	}

	// Update ride
	now := time.Now()
	ride.Status = models.RideStatusCompleted
	ride.DropoffAt = &now

	// Set actual fare (may be different from estimated fare)
	ride.ActualFare = ride.EstimatedFare
	ride.PaymentStatus = models.PaymentStatusPending

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to complete ride",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Ride completed successfully",
		"ride":    ride,
	})
}
