package controllers

import (
	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
)

// RateRideRequest represents the request body for rating a ride
type RateRideRequest struct {
	Rating            float32 `json:"rating" validate:"required,min=1,max=5"`
	Comment           string  `json:"comment"`
	SafetyRating      float32 `json:"safety_rating" validate:"min=1,max=5"`
	PunctualityRating float32 `json:"punctuality_rating" validate:"min=1,max=5"`
	CleanlinessRating float32 `json:"cleanliness_rating" validate:"min=1,max=5"`
}

// RateDriverRequest represents the request body for rating a driver
type RateDriverRequest struct {
	Rating                float32 `json:"rating" validate:"required,min=1,max=5"`
	Comment               string  `json:"comment"`
	DrivingSkillRating    float32 `json:"driving_skill_rating" validate:"min=1,max=5"`
	ProfessionalismRating float32 `json:"professionalism_rating" validate:"min=1,max=5"`
	VehicleRating         float32 `json:"vehicle_rating" validate:"min=1,max=5"`
}

// RateRiderRequest represents the request body for rating a rider
type RateRiderRequest struct {
	Rating            float32 `json:"rating" validate:"required,min=1,max=5"`
	Comment           string  `json:"comment"`
	BehaviorRating    float32 `json:"behavior_rating" validate:"min=1,max=5"`
	PunctualityRating float32 `json:"punctuality_rating" validate:"min=1,max=5"`
}

// RateRide allows a user to rate their ride
func RateRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Parse request body
	var req RateRideRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND user_id = ? AND status = ?", rideID, userID, models.RideStatusCompleted).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or not completed",
		})
	}

	// Check if ride has already been rated
	var existingRating models.RideRating
	result := database.DB.Where("ride_id = ?", rideID).First(&existingRating)
	if result.RowsAffected > 0 {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "Ride has already been rated",
		})
	}

	// Create rating
	rating := models.RideRating{
		RideID:            rideID,
		UserID:            userID,
		DriverID:          *ride.DriverID,
		Rating:            req.Rating,
		Comment:           req.Comment,
		SafetyRating:      req.SafetyRating,
		PunctualityRating: req.PunctualityRating,
		CleanlinessRating: req.CleanlinessRating,
	}

	if err := database.DB.Create(&rating).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create rating",
		})
	}

	// Update driver's average rating
	var driverRatings []models.RideRating
	if err := database.DB.Where("driver_id = ?", ride.DriverID).Find(&driverRatings).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get driver ratings",
		})
	}

	var totalRating float32
	for _, r := range driverRatings {
		totalRating += r.Rating
	}

	averageRating := totalRating / float32(len(driverRatings))

	var driver models.Driver
	if err := database.DB.Where("id = ?", ride.DriverID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get driver",
		})
	}

	driver.Rating = averageRating
	if err := database.DB.Save(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update driver rating",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Rating submitted successfully",
		"rating":  rating,
	})
}

// GetRideRating returns the rating for a specific ride
func GetRideRating(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND (user_id = ? OR driver_id IN (SELECT id FROM drivers WHERE user_id = ?))", rideID, userID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Get rating
	var rating models.RideRating
	if err := database.DB.Where("ride_id = ?", rideID).First(&rating).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Rating not found",
		})
	}

	return c.JSON(fiber.Map{
		"rating": rating,
	})
}

// GetUserRatings returns all ratings given by the authenticated user
func GetUserRatings(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ratings
	var ratings []models.RideRating
	if err := database.DB.Where("user_id = ?", userID).Find(&ratings).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get ratings",
		})
	}

	return c.JSON(fiber.Map{
		"ratings": ratings,
	})
}

// GetDriverRatings returns all ratings for the authenticated driver
func GetDriverRatings(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Get ratings
	var ratings []models.RideRating
	if err := database.DB.Where("driver_id = ?", driver.ID).Find(&ratings).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get ratings",
		})
	}

	return c.JSON(fiber.Map{
		"ratings": ratings,
	})
}
