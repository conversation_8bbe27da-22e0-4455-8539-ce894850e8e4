package controllers

import (
	"fmt"
	"io"
	"log"
	"path/filepath"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/services"
	"github.com/jirani/backend/internal/utils"
)

// UpdateUserProfileRequest represents the request body for updating a user profile
type UpdateUserProfileRequest struct {
	FullName     string `json:"full_name" validate:"required"`
	PhoneNumber  string `json:"phone_number" validate:"required"`
	ProfileImage string `json:"profile_image"`
}

// ChangePasswordRequest represents the request body for changing a user's password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

// AddLocationRequest represents the request body for adding a user location
type AddLocationRequest struct {
	Name      string  `json:"name" validate:"required"`
	Address   string  `json:"address" validate:"required"`
	Latitude  float64 `json:"latitude" validate:"required"`
	Longitude float64 `json:"longitude" validate:"required"`
	IsDefault bool    `json:"is_default"`
}

// GetUserProfile returns the authenticated user's profile
func GetUserProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Create user service
	userService := services.NewUserService()

	// Get user profile
	user, err := userService.GetUserProfile(userID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	return c.JSON(fiber.Map{
		"user": fiber.Map{
			"id":            user.ID,
			"email":         user.Email,
			"full_name":     user.FullName,
			"phone_number":  user.PhoneNumber,
			"profile_image": user.ProfileImage,
		},
	})
}

// UpdateUserProfile updates the authenticated user's profile
func UpdateUserProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req UpdateUserProfileRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create user service
	userService := services.NewUserService()

	// Update user profile
	user, err := userService.UpdateUserProfile(userID, req.FullName, req.PhoneNumber, req.ProfileImage)
	if err != nil {
		// Handle specific errors
		switch err.Error() {
		case "user not found":
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "User not found",
			})
		case "phone number is already taken":
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"error": "Phone number is already taken",
			})
		default:
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}
	}

	return c.JSON(fiber.Map{
		"message": "User profile updated successfully",
		"user": fiber.Map{
			"id":            user.ID,
			"email":         user.Email,
			"full_name":     user.FullName,
			"phone_number":  user.PhoneNumber,
			"profile_image": user.ProfileImage,
		},
	})
}

// ChangePassword changes the authenticated user's password
func ChangePassword(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req ChangePasswordRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create auth service
	authService := services.NewAuthService()

	// Change password
	err := authService.ChangePassword(userID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		// Handle specific errors
		switch err.Error() {
		case "user not found":
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "User not found",
			})
		case "current password is incorrect":
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "Current password is incorrect",
			})
		default:
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}
	}

	return c.JSON(fiber.Map{
		"message": "Password changed successfully",
	})
}

// UploadProfileImage uploads a profile image for the authenticated user
func UploadProfileImage(c *fiber.Ctx) error {
	// Log the request path and method for debugging
	fmt.Printf("Received request: %s %s\n", c.Method(), c.Path())

	// Get user ID from context
	userID, ok := c.Locals("user_id").(string)
	if !ok {
		fmt.Printf("Error: user_id not found in context\n")
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User ID not found in context. Authentication may have failed.",
		})
	}
	fmt.Printf("Processing profile image upload for user ID: %s\n", userID)

	// Get file from form
	file, err := c.FormFile("image")
	if err != nil {
		fmt.Printf("Error getting file from form: %v\n\n", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": fmt.Sprintf("No image provided or error processing form: %v", err),
		})
	}
	fmt.Printf("Received file: %s, size: %d bytes\n", file.Filename, file.Size)

	// Check file size (max 5MB)
	if file.Size > 5*1024*1024 {
		fmt.Printf("File size too large: %d bytes\n", file.Size)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": fmt.Sprintf("Image size exceeds 5MB limit. Current size: %d bytes", file.Size),
		})
	}

	// Check file type
	ext := filepath.Ext(file.Filename)
	allowedExts := map[string]bool{".jpg": true, ".jpeg": true, ".png": true}
	if !allowedExts[ext] {
		log.Printf("Invalid file extension: %s", ext)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": fmt.Sprintf("Only JPG, JPEG, and PNG images are allowed. Received: %s", ext),
		})
	}

	// Open file
	fileHandle, err := file.Open()
	if err != nil {
		log.Printf("Error opening file: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to open uploaded file: %v", err),
		})
	}
	defer fileHandle.Close()

	// Read file content
	fileBytes, err := io.ReadAll(fileHandle)
	if err != nil {
		log.Printf("Error reading file: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to read uploaded file: %v", err),
		})
	}
	log.Printf("Successfully read %d bytes from file", len(fileBytes))

	// Create storage service
	storageService, err := services.NewStorageService()
	if err != nil {
		log.Printf("Error creating storage service: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to initialize storage service: %v", err),
		})
	}
	log.Printf("Storage service initialized successfully")

	// Upload file to MinIO
	imageURL, err := storageService.UploadFile(fileBytes, file.Filename)
	if err != nil {
		log.Printf("Error uploading file to MinIO: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to upload image: %v", err),
		})
	}
	log.Printf("File uploaded successfully. URL: %s", imageURL)

	// Update user profile with image URL
	userService := services.NewUserService()
	user, err := userService.GetUserProfile(userID)
	if err != nil {
		log.Printf("Error getting user profile: %v", err)
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": fmt.Sprintf("User not found: %v", err),
		})
	}
	log.Printf("Retrieved user profile for user ID: %s", userID)

	// Delete old profile image if exists
	if user.ProfileImage != "" {
		log.Printf("Deleting old profile image: %s", user.ProfileImage)
		// Attempt to delete but don't fail if it doesn't work
		if err := storageService.DeleteFile(user.ProfileImage); err != nil {
			log.Printf("Warning: Failed to delete old profile image: %v", err)
		}
	}

	// Update user profile with new image URL
	user, err = userService.UpdateUserProfile(userID, user.FullName, user.PhoneNumber, imageURL)
	if err != nil {
		log.Printf("Error updating user profile: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to update profile: %v", err),
		})
	}
	log.Printf("User profile updated successfully with new image URL")

	return c.JSON(fiber.Map{
		"message":   "Profile image uploaded successfully",
		"image_url": imageURL,
		"user": fiber.Map{
			"id":            user.ID,
			"email":         user.Email,
			"full_name":     user.FullName,
			"phone_number":  user.PhoneNumber,
			"profile_image": user.ProfileImage,
		},
	})
}

// AddUserLocation adds a new location for the authenticated user
func AddUserLocation(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req AddLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create user service
	userService := services.NewUserService()

	// Add location
	location, err := userService.AddUserLocation(userID, req.Name, req.Address, req.Latitude, req.Longitude, req.IsDefault)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message":  "Location added successfully",
		"location": location,
	})
}

// GetUserLocations returns all locations for the authenticated user
func GetUserLocations(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Create user service
	userService := services.NewUserService()

	// Get locations
	locations, err := userService.GetUserLocations(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"locations": locations,
	})
}

// DeleteUserLocation deletes a location for the authenticated user
func DeleteUserLocation(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get location ID from params
	locationID := c.Params("id")
	if locationID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Location ID is required",
		})
	}

	// Convert location ID to uint
	var locationIDUint uint
	if _, err := fmt.Sscanf(locationID, "%d", &locationIDUint); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid location ID",
		})
	}

	// Create user service
	userService := services.NewUserService()

	// Delete location
	if err := userService.DeleteUserLocation(userID, locationIDUint); err != nil {
		if err.Error() == "location not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "Location not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"message": "Location deleted successfully",
	})
}
