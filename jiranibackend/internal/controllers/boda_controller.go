package controllers

import (
	"fmt"
	"math"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
	"github.com/jirani/backend/internal/websocket"
)

// RequestRideRequest represents the request body for requesting a ride
type RequestRideRequest struct {
	PickupAddress    string  `json:"pickup_address" validate:"required"`
	PickupLatitude   float64 `json:"pickup_latitude" validate:"required"`
	PickupLongitude  float64 `json:"pickup_longitude" validate:"required"`
	DropoffAddress   string  `json:"dropoff_address" validate:"required"`
	DropoffLatitude  float64 `json:"dropoff_latitude" validate:"required"`
	DropoffLongitude float64 `json:"dropoff_longitude" validate:"required"`
	Notes            string  `json:"notes"`
}

// UpdateDriverLocationRequest represents the request body for updating a driver's location
type UpdateDriverLocationRequest struct {
	Latitude  float64 `json:"latitude" validate:"required"`
	Longitude float64 `json:"longitude" validate:"required"`
}

// RegisterAsDriverRequest represents the request body for registering as a driver
type RegisterAsDriverRequest struct {
	LicenseNumber string `json:"license_number" validate:"required"`
	IdNumber      string `json:"id_number" validate:"required"`
}

// AddVehicleRequest represents the request body for adding a vehicle
type AddVehicleRequest struct {
	Type         string `json:"type" validate:"required"`
	Make         string `json:"make" validate:"required"`
	Model        string `json:"model" validate:"required"`
	Year         int    `json:"year" validate:"required"`
	Color        string `json:"color" validate:"required"`
	LicensePlate string `json:"license_plate" validate:"required"`
}

// ListNearbyDrivers returns a list of nearby drivers
func ListNearbyDrivers(c *fiber.Ctx) error {
	// Get query parameters
	latitude := c.QueryFloat("latitude", 0)
	longitude := c.QueryFloat("longitude", 0)
	radius := c.QueryFloat("radius", 5) // Default radius: 5km

	if latitude == 0 || longitude == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Latitude and longitude are required",
		})
	}

	// Check if database connection is working
	if database.DB == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Database connection is nil",
		})
	}

	// Find nearby drivers
	var drivers []models.Driver
	if err := database.DB.Where("is_available = ? AND is_verified = ?", true, true).Find(&drivers).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get drivers: " + err.Error(),
		})
	}

	// Filter drivers by distance
	nearbyDrivers := []models.Driver{}
	for _, driver := range drivers {
		// Skip drivers without location
		if driver.CurrentLatitude == 0 && driver.CurrentLongitude == 0 {
			continue
		}

		// Calculate distance
		distance := calculateDistance(
			latitude, longitude,
			driver.CurrentLatitude, driver.CurrentLongitude,
		)

		// Add driver if within radius
		if distance <= radius {
			nearbyDrivers = append(nearbyDrivers, driver)
		}
	}

	// Load driver details
	for i := range nearbyDrivers {
		database.DB.Model(&nearbyDrivers[i]).Association("User").Find(&nearbyDrivers[i].User)
		database.DB.Model(&nearbyDrivers[i]).Association("Vehicles").Find(&nearbyDrivers[i].Vehicles)
	}

	return c.JSON(fiber.Map{
		"drivers": nearbyDrivers,
	})
}

// RequestRide creates a new ride request
func RequestRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req RequestRideRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Calculate distance and duration
	distance := calculateDistance(
		req.PickupLatitude, req.PickupLongitude,
		req.DropoffLatitude, req.DropoffLongitude,
	)
	duration := int(distance * 3) // Estimate: 3 minutes per km

	// Calculate fare
	baseFare := 50.0  // Base fare in KES
	perKmRate := 30.0 // Rate per km in KES
	fare := baseFare + (distance * perKmRate)

	// Create ride
	ride := models.Ride{
		UserID:           userID,
		Status:           models.RideStatusRequested,
		PickupAddress:    req.PickupAddress,
		PickupLatitude:   req.PickupLatitude,
		PickupLongitude:  req.PickupLongitude,
		DropoffAddress:   req.DropoffAddress,
		DropoffLatitude:  req.DropoffLatitude,
		DropoffLongitude: req.DropoffLongitude,
		Distance:         distance,
		Duration:         duration,
		EstimatedFare:    fare,
		PaymentStatus:    models.PaymentStatusPending,
		Notes:            req.Notes,
		// Don't set DriverID and VehicleID - they should remain nil/zero until assigned
	}

	if err := database.DB.Create(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create ride",
		})
	}

	// Find nearby drivers to broadcast the ride request
	nearbyDrivers, err := findNearbyDrivers(req.PickupLatitude, req.PickupLongitude, 5.0)
	if err == nil && len(nearbyDrivers) > 0 {
		// Extract driver user IDs
		var driverIDs []string
		for _, driver := range nearbyDrivers {
			driverIDs = append(driverIDs, driver.UserID)
		}

		// Broadcast ride request to nearby drivers
		rideData := map[string]interface{}{
			"ride_id":           ride.ID,
			"user_id":           ride.UserID,
			"pickup_address":    ride.PickupAddress,
			"pickup_latitude":   ride.PickupLatitude,
			"pickup_longitude":  ride.PickupLongitude,
			"dropoff_address":   ride.DropoffAddress,
			"dropoff_latitude":  ride.DropoffLatitude,
			"dropoff_longitude": ride.DropoffLongitude,
			"distance":          ride.Distance,
			"estimated_fare":    ride.EstimatedFare,
			"notes":             ride.Notes,
		}
		websocket.BroadcastRideRequest(driverIDs, rideData)
	}

	// Notify the user that the ride request was created
	websocket.BroadcastNotification(userID, map[string]interface{}{
		"title":   "Ride Requested",
		"message": "Your ride request has been sent to nearby drivers",
		"ride_id": ride.ID,
	})

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Ride requested successfully",
		"ride":    ride,
	})
}

// GetUserRides returns all rides for the authenticated user
func GetUserRides(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Check if database connection is working
	if database.DB == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Database connection is nil",
		})
	}

	// Get rides
	var rides []models.Ride
	if err := database.DB.Where("user_id = ?", userID).Order("created_at DESC").Find(&rides).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get rides: " + err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"rides": rides,
	})
}

// GetRideDetails returns details of a specific ride
func GetRideDetails(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND user_id = ?", rideID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Load associations
	if ride.DriverID != nil && *ride.DriverID != "" {
		database.DB.Model(&ride).Association("Driver").Find(&ride.Driver)
		database.DB.Model(&ride.Driver).Association("User").Find(&ride.Driver.User)
	}
	if ride.VehicleID != nil && *ride.VehicleID != 0 {
		database.DB.Model(&ride).Association("Vehicle").Find(&ride.Vehicle)
	}

	return c.JSON(fiber.Map{
		"ride": ride,
	})
}

// CancelRide cancels a ride
func CancelRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND user_id = ?", rideID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Check if ride can be cancelled
	if ride.Status != models.RideStatusRequested &&
		ride.Status != models.RideStatusAccepted &&
		ride.Status != models.RideStatusEnRouteToPickup {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride cannot be cancelled",
		})
	}

	// Update ride status
	now := time.Now()
	ride.Status = models.RideStatusCancelled
	ride.CancelledAt = &now

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to cancel ride",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Ride cancelled successfully",
	})
}

// findNearbyDrivers is a helper function to find nearby drivers
func findNearbyDrivers(latitude, longitude, radius float64) ([]models.Driver, error) {
	// Check if database connection is working
	if database.DB == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	// Find nearby drivers
	var drivers []models.Driver
	if err := database.DB.Where("is_available = ? AND is_verified = ?", true, true).Find(&drivers).Error; err != nil {
		return nil, fmt.Errorf("failed to get drivers: %v", err)
	}

	// Filter drivers by distance
	nearbyDrivers := []models.Driver{}
	for _, driver := range drivers {
		// Skip drivers without location
		if driver.CurrentLatitude == 0 && driver.CurrentLongitude == 0 {
			continue
		}

		// Calculate distance
		distance := calculateDistance(
			latitude, longitude,
			driver.CurrentLatitude, driver.CurrentLongitude,
		)

		// Add driver if within radius
		if distance <= radius {
			nearbyDrivers = append(nearbyDrivers, driver)
		}
	}

	// Load driver details
	for i := range nearbyDrivers {
		database.DB.Model(&nearbyDrivers[i]).Association("User").Find(&nearbyDrivers[i].User)
		database.DB.Model(&nearbyDrivers[i]).Association("Vehicles").Find(&nearbyDrivers[i].Vehicles)
	}

	return nearbyDrivers, nil
}

// Helper function to calculate distance between two coordinates using the Haversine formula
func calculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const earthRadius = 6371 // Earth radius in kilometers

	// Convert degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	// Haversine formula
	dLat := lat2Rad - lat1Rad
	dLon := lon2Rad - lon1Rad
	a := math.Sin(dLat/2)*math.Sin(dLat/2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Sin(dLon/2)*math.Sin(dLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadius * c

	return math.Round(distance*100) / 100 // Round to 2 decimal places
}
