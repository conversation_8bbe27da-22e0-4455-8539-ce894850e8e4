package controllers

import (
	"fmt"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/services"
	"github.com/jirani/backend/internal/utils"
)

// RegisterRequest represents the request body for user registration
type RegisterRequest struct {
	Email       string `json:"email" validate:"required,email"`
	Password    string `json:"password" validate:"required,min=8"`
	FullName    string `json:"full_name" validate:"required"`
	PhoneNumber string `json:"phone_number" validate:"required"`
}

// LoginRequest represents the request body for user login
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// Register handles user registration
func Register(c *fiber.Ctx) error {
	// Parse request body
	var req RegisterRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body: " + err.Error(),
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Log the registration attempt
	fmt.Printf("Registration attempt: %s, %s\n", req.Email, req.PhoneNumber)

	// Create auth service
	authService := services.NewAuthService()

	// Register user
	user, token, err := authService.RegisterUser(req.Email, req.Password, req.FullName, req.PhoneNumber)
	if err != nil {
		// Log the error
		fmt.Printf("Registration error: %v\n", err)

		// Handle specific errors
		switch err.Error() {
		case "user with this email already exists":
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"error": err.Error(),
			})
		case "user with this phone number already exists":
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"error": err.Error(),
			})
		default:
			// For debugging purposes, return the actual error
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "User registered successfully",
		"user": fiber.Map{
			"id":           user.ID,
			"email":        user.Email,
			"full_name":    user.FullName,
			"phone_number": user.PhoneNumber,
		},
		"token": token,
	})
}

// Login handles user login
func Login(c *fiber.Ctx) error {
	// Parse request body
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create auth service
	authService := services.NewAuthService()

	// Login user
	user, token, err := authService.LoginUser(req.Email, req.Password)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"message": "Login successful",
		"user": fiber.Map{
			"id":           user.ID,
			"email":        user.Email,
			"full_name":    user.FullName,
			"phone_number": user.PhoneNumber,
			"roles":        user.Roles,
		},
		"token": token,
	})
}

// RefreshToken handles token refresh
func RefreshToken(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Create auth service
	authService := services.NewAuthService()

	// Refresh token
	token, err := authService.RefreshToken(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"message": "Token refreshed successfully",
		"token":   token,
	})
}

// ForgotPassword handles password reset requests
func ForgotPassword(c *fiber.Ctx) error {
	// Parse request body
	var req struct {
		Email string `json:"email" validate:"required,email"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create auth service
	authService := services.NewAuthService()

	// Request password reset
	if err := authService.RequestPasswordReset(req.Email); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Always return success to prevent email enumeration
	return c.JSON(fiber.Map{
		"message": "If your email is registered, you will receive a password reset link",
		"success": true,
	})
}

// ResetPassword handles password reset
func ResetPassword(c *fiber.Ctx) error {
	// Parse request body
	var req struct {
		Token       string `json:"token" validate:"required"`
		NewPassword string `json:"new_password" validate:"required,min=8"`
	}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Create auth service
	authService := services.NewAuthService()

	// Reset password
	if err := authService.ResetPassword(req.Token, req.NewPassword); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"message": "Password reset successful",
		"success": true,
	})
}
