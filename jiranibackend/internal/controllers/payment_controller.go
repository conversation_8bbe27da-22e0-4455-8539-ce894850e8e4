package controllers

import (
	"github.com/gofiber/fiber/v2"
)

// CreatePayment creates a new payment
func CreatePayment(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Create payment endpoint",
	})
}

// GetUserPayments gets all payments for a user
func GetUserPayments(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Get user payments endpoint",
	})
}

// GetPaymentDetails gets details for a payment
func GetPaymentDetails(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Get payment details endpoint",
	})
}
