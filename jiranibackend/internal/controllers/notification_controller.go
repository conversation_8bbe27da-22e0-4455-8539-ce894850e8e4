package controllers

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
)

// RegisterDeviceRequest represents the request body for registering a device token
type RegisterDeviceRequest struct {
	Token      string `json:"token" validate:"required"`
	DeviceType string `json:"device_type" validate:"required,oneof=ios android web"`
}

// RegisterDeviceToken registers a device token for push notifications
func RegisterDeviceToken(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req RegisterDeviceRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Check if token already exists
	var existingToken models.DeviceToken
	result := database.DB.Where("token = ?", req.Token).First(&existingToken)

	if result.RowsAffected > 0 {
		// Update existing token
		existingToken.UserID = userID
		existingToken.DeviceType = req.DeviceType
		existingToken.IsActive = true
		existingToken.LastUsedAt = time.Now()

		if err := database.DB.Save(&existingToken).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to update device token",
			})
		}

		return c.JSON(fiber.Map{
			"message": "Device token updated successfully",
			"token":   existingToken,
		})
	}

	// Create new token
	token := models.DeviceToken{
		UserID:     userID,
		Token:      req.Token,
		DeviceType: req.DeviceType,
		IsActive:   true,
		LastUsedAt: time.Now(),
	}

	if err := database.DB.Create(&token).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to register device token",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Device token registered successfully",
		"token":   token,
	})
}

// UnregisterDeviceToken unregisters a device token
func UnregisterDeviceToken(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get token from params
	token := c.Query("token")
	if token == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Token is required",
		})
	}

	// Find token
	var deviceToken models.DeviceToken
	if err := database.DB.Where("token = ? AND user_id = ?", token, userID).First(&deviceToken).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Token not found",
		})
	}

	// Update token
	deviceToken.IsActive = false
	if err := database.DB.Save(&deviceToken).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to unregister device token",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Device token unregistered successfully",
	})
}

// GetUserNotifications returns all notifications for the authenticated user
func GetUserNotifications(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get query parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 20)
	offset := (page - 1) * limit

	// Check if database connection is working
	if database.DB == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Database connection is nil",
		})
	}

	// Get notifications
	var notifications []models.Notification
	if err := database.DB.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit).Offset(offset).Find(&notifications).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get notifications: " + err.Error(),
		})
	}

	// Get total count
	var count int64
	if err := database.DB.Model(&models.Notification{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count notifications",
		})
	}

	return c.JSON(fiber.Map{
		"notifications": notifications,
		"meta": fiber.Map{
			"total": count,
			"page":  page,
			"limit": limit,
		},
	})
}

// MarkNotificationAsRead marks a notification as read
func MarkNotificationAsRead(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get notification ID from params
	notificationID := c.Params("id")
	if notificationID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Notification ID is required",
		})
	}

	// Find notification
	var notification models.Notification
	if err := database.DB.Where("id = ? AND user_id = ?", notificationID, userID).First(&notification).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Notification not found",
		})
	}

	// Update notification
	now := time.Now()
	notification.IsRead = true
	notification.ReadAt = &now
	if err := database.DB.Save(&notification).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to mark notification as read",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Notification marked as read",
	})
}

// MarkAllNotificationsAsRead marks all notifications as read
func MarkAllNotificationsAsRead(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Update notifications
	now := time.Now()
	if err := database.DB.Model(&models.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Updates(map[string]interface{}{
		"is_read": true,
		"read_at": now,
	}).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to mark notifications as read",
		})
	}

	return c.JSON(fiber.Map{
		"message": "All notifications marked as read",
	})
}

// GetUnreadNotificationCount returns the count of unread notifications
func GetUnreadNotificationCount(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get count
	var count int64
	if err := database.DB.Model(&models.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count notifications",
		})
	}

	return c.JSON(fiber.Map{
		"count": count,
	})
}
