package controllers

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
)

// EnRouteToPickup marks a ride as en route to pickup
func EnRouteToPickup(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)
	
	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}
	
	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}
	
	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND driver_id = ? AND status = ?", rideID, driver.ID, models.RideStatusAccepted).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or not in accepted status",
		})
	}
	
	// Update ride
	now := time.Now()
	ride.Status = models.RideStatusEnRouteToPickup
	ride.EnRouteToPickupAt = &now
	
	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update ride",
		})
	}
	
	// Create notification for user
	notification := models.Notification{
		UserID:  ride.UserID,
		Type:    models.RideDriverArrived,
		Title:   "Driver En Route",
		Message: "Your driver is on the way to pick you up.",
		RideID:  ride.ID,
	}
	
	if err := database.DB.Create(&notification).Error; err != nil {
		// Log error but continue
	}
	
	return c.JSON(fiber.Map{
		"message": "Driver en route to pickup",
		"ride":    ride,
	})
}

// ArrivedAtPickup marks a ride as arrived at pickup
func ArrivedAtPickup(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)
	
	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}
	
	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}
	
	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND driver_id = ? AND status = ?", rideID, driver.ID, models.RideStatusEnRouteToPickup).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found or not in en route status",
		})
	}
	
	// Update ride
	now := time.Now()
	ride.Status = models.RideStatusArrivedAtPickup
	ride.ArrivedAtPickupAt = &now
	
	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update ride",
		})
	}
	
	// Create notification for user
	notification := models.Notification{
		UserID:  ride.UserID,
		Type:    models.RideDriverArrived,
		Title:   "Driver Arrived",
		Message: "Your driver has arrived at the pickup location.",
		RideID:  ride.ID,
	}
	
	if err := database.DB.Create(&notification).Error; err != nil {
		// Log error but continue
	}
	
	return c.JSON(fiber.Map{
		"message": "Driver arrived at pickup",
		"ride":    ride,
	})
}
