package controllers

import (
	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
)

// ListUsers returns a list of all users
func ListUsers(c *fiber.Ctx) error {
	// Get query parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit

	// Get users
	var users []models.User
	if err := database.DB.Limit(limit).Offset(offset).Find(&users).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get users",
		})
	}

	// Get total count
	var count int64
	if err := database.DB.Model(&models.User{}).Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count users",
		})
	}

	return c.JSON(fiber.Map{
		"users": users,
		"meta": fiber.Map{
			"total": count,
			"page":  page,
			"limit": limit,
		},
	})
}

// ListAllOrders returns a list of all orders
func ListAllOrders(c *fiber.Ctx) error {
	// Get query parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit
	status := c.Query("status")

	//   Build query
	query := database.DB.Model(&models.Order{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get orders
	var orders []models.Order
	if err := query.Limit(limit).Offset(offset).Order("created_at DESC").Find(&orders).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get orders",
		})
	}

	// Get total count
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count orders",
		})
	}

	// Load associations
	for i := range orders {
		database.DB.Model(&orders[i]).Association("User").Find(&orders[i].User)
		database.DB.Model(&orders[i]).Association("Restaurant").Find(&orders[i].Restaurant)
	}

	return c.JSON(fiber.Map{
		"orders": orders,
		"meta": fiber.Map{
			"total": count,
			"page":  page,
			"limit": limit,
		},
	})
}

// ListAllRides returns a list of all rides
func ListAllRides(c *fiber.Ctx) error {
	// Get query parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit
	status := c.Query("status")

	// Build query
	query := database.DB.Model(&models.Ride{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get rides
	var rides []models.Ride
	if err := query.Limit(limit).Offset(offset).Order("created_at DESC").Find(&rides).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get rides",
		})
	}

	// Get total count
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count rides",
		})
	}

	// Load associations
	for i := range rides {
		database.DB.Model(&rides[i]).Association("User").Find(&rides[i].User)
		if rides[i].DriverID != nil && *rides[i].DriverID != "" {
			database.DB.Model(&rides[i]).Association("Driver").Find(&rides[i].Driver)
			database.DB.Model(&rides[i].Driver).Association("User").Find(&rides[i].Driver.User)
		}
	}

	return c.JSON(fiber.Map{
		"rides": rides,
		"meta": fiber.Map{
			"total": count,
			"page":  page,
			"limit": limit,
		},
	})
}

// ListAllDrivers returns a list of all drivers
func ListAllDrivers(c *fiber.Ctx) error {
	// Get query parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit
	verified := c.QueryBool("verified", false)

	// Build query
	query := database.DB.Model(&models.Driver{})
	if verified {
		query = query.Where("is_verified = ?", true)
	}

	// Get drivers
	var drivers []models.Driver
	if err := query.Limit(limit).Offset(offset).Find(&drivers).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get drivers",
		})
	}

	// Get total count
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to count drivers",
		})
	}

	// Load associations
	for i := range drivers {
		database.DB.Model(&drivers[i]).Association("User").Find(&drivers[i].User)
		database.DB.Model(&drivers[i]).Association("Vehicles").Find(&drivers[i].Vehicles)
	}

	return c.JSON(fiber.Map{
		"drivers": drivers,
		"meta": fiber.Map{
			"total": count,
			"page":  page,
			"limit": limit,
		},
	})
}

// VerifyDriver verifies a driver
func VerifyDriver(c *fiber.Ctx) error {
	// Get driver ID from params
	driverID := c.Params("id")
	if driverID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Driver ID is required",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("id = ?", driverID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Update driver
	driver.IsVerified = true
	if err := database.DB.Save(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to verify driver",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Driver verified successfully",
		"driver":  driver,
	})
}
