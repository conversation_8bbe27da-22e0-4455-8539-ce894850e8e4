package controllers

import (
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
	"github.com/jirani/backend/internal/websocket"
)

// UpdateLocationRequest represents the request body for updating a driver's location
type UpdateLocationRequest struct {
	Latitude  float64 `json:"latitude" validate:"required"`
	Longitude float64 `json:"longitude" validate:"required"`
	RideID    string  `json:"ride_id"`
}

// UpdateDriverLocationWithRide updates a driver's location with ride information
func UpdateDriverLocationWithRide(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req UpdateLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Validate location coordinates and bounds - PRP-LOCATION-ENH-001
	if err := utils.ValidateDriverLocation(req.Latitude, req.Longitude); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Update driver location
	driver.CurrentLatitude = req.Latitude
	driver.CurrentLongitude = req.Longitude
	driver.LastLocationUpdate = time.Now()

	if err := database.DB.Save(&driver).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update driver location",
		})
	}

	// If ride ID is provided, update ride location as well
	if req.RideID != "" {
		var ride models.Ride
		if err := database.DB.Where("id = ? AND driver_id = ?", req.RideID, driver.ID).First(&ride).Error; err != nil {
			// Ride not found or doesn't belong to this driver, ignore
		} else {
			// Update ride location
			ride.CurrentLatitude = req.Latitude
			ride.CurrentLongitude = req.Longitude
			ride.LastLocationUpdate = time.Now()

			// Update ETA based on current location
			// This would typically involve calling a directions API to get updated ETA
			// For now, we'll just update the timestamp to indicate that we've updated the location

			if err := database.DB.Save(&ride).Error; err != nil {
				// Log error but continue
				if err := database.DB.Create(&models.Notification{
					UserID:  userID,
					Type:    models.SystemMessage,
					Title:   "Location Update Error",
					Message: "Failed to update ride location",
				}).Error; err != nil {
					// Ignore error
				}
			} else {
				// Broadcast driver location update via WebSocket
				locationData := map[string]interface{}{
					"driver_id": driver.ID,
					"user_id":   driver.UserID,
					"latitude":  req.Latitude,
					"longitude": req.Longitude,
					"timestamp": time.Now().Unix(),
					"ride_id":   req.RideID,
				}
				websocket.BroadcastLocationUpdate(req.RideID, locationData)
			}

			// If the driver is en route to pickup and close to pickup location, update status
			if ride.Status == models.RideStatusEnRouteToPickup {
				// Calculate distance to pickup
				pickupLat := ride.PickupLatitude
				pickupLng := ride.PickupLongitude
				distance := utils.CalculateDistance(req.Latitude, req.Longitude, pickupLat, pickupLng)

				// If within 100 meters of pickup, update status
				if distance <= 0.1 { // 0.1 km = 100 meters
					now := time.Now()
					ride.Status = models.RideStatusArrivedAtPickup
					ride.ArrivedAtPickupAt = &now

					if err := database.DB.Save(&ride).Error; err != nil {
						// Log error but continue
						if err := database.DB.Create(&models.Notification{
							UserID:  userID,
							Type:    models.SystemMessage,
							Title:   "Status Update Error",
							Message: "Failed to update ride status",
						}).Error; err != nil {
							// Ignore error
						}
					}

					// Create notification for user
					userNotification := models.Notification{
						UserID:  ride.UserID,
						Type:    models.RideDriverArrived,
						Title:   "Driver Arrived",
						Message: "Your driver has arrived at the pickup location.",
						RideID:  ride.ID,
					}

					if err := database.DB.Create(&userNotification).Error; err != nil {
						// Ignore error
					}
				}
			}
		}
	}

	return c.JSON(fiber.Map{
		"message": "Location updated successfully",
	})
}

// GetDriverLocation returns a driver's current location
func GetDriverLocation(c *fiber.Ctx) error {
	// Get driver ID from params
	driverID := c.Params("id")
	if driverID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Driver ID is required",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("id = ?", driverID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	return c.JSON(fiber.Map{
		"latitude":   driver.CurrentLatitude,
		"longitude":  driver.CurrentLongitude,
		"updated_at": driver.LastLocationUpdate,
	})
}

// GetRideLocation returns the current location for a ride
func GetRideLocation(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND (user_id = ? OR driver_id IN (SELECT id FROM drivers WHERE user_id = ?))", rideID, userID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// If ride has current location, return it
	if ride.LastLocationUpdate.After(time.Time{}) {
		return c.JSON(fiber.Map{
			"latitude":   ride.CurrentLatitude,
			"longitude":  ride.CurrentLongitude,
			"updated_at": ride.LastLocationUpdate,
		})
	}

	// Otherwise, get driver's location
	if ride.DriverID == nil || *ride.DriverID == "" {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "No driver assigned to this ride",
		})
	}

	var driver models.Driver
	if err := database.DB.Where("id = ?", *ride.DriverID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	return c.JSON(fiber.Map{
		"latitude":   driver.CurrentLatitude,
		"longitude":  driver.CurrentLongitude,
		"updated_at": driver.LastLocationUpdate,
	})
}

// UpdateRideETA updates the ETA for a ride
func UpdateRideETA(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Parse request body
	type ETARequest struct {
		EstimatedPickupTime  string `json:"estimated_pickup_time"`
		EstimatedArrivalTime string `json:"estimated_arrival_time"`
	}

	var req ETARequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Find driver
	var driver models.Driver
	if err := database.DB.Where("user_id = ?", userID).First(&driver).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Driver not found",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND driver_id = ?", rideID, driver.ID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Update ETA
	if req.EstimatedPickupTime != "" {
		pickupTime, err := time.Parse(time.RFC3339, req.EstimatedPickupTime)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid pickup time format",
			})
		}
		ride.EstimatedPickupTime = &pickupTime
	}

	if req.EstimatedArrivalTime != "" {
		arrivalTime, err := time.Parse(time.RFC3339, req.EstimatedArrivalTime)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid arrival time format",
			})
		}
		ride.EstimatedArrivalTime = &arrivalTime
	}

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update ETA",
		})
	}

	return c.JSON(fiber.Map{
		"message": "ETA updated successfully",
	})
}

// ============================================================================
// LOCATION HISTORY ENDPOINTS - PRP-LOCATION-ENH-001
// ============================================================================

// SaveLocationRequest represents the request body for saving a location to history
type SaveLocationRequest struct {
	Latitude     float64             `json:"latitude" validate:"required"`
	Longitude    float64             `json:"longitude" validate:"required"`
	Address      string              `json:"address" validate:"required"`
	LocationType models.LocationType `json:"location_type" validate:"required"`
}

// GetLocationHistoryResponse represents the response for location history
type GetLocationHistoryResponse struct {
	ID           string              `json:"id"`
	Latitude     float64             `json:"latitude"`
	Longitude    float64             `json:"longitude"`
	Address      string              `json:"address"`
	LocationType models.LocationType `json:"location_type"`
	UsageCount   int                 `json:"usage_count"`
	LastUsedAt   time.Time           `json:"last_used_at"`
	CreatedAt    time.Time           `json:"created_at"`
}

// SaveLocationToHistory saves a location to user's history
func SaveLocationToHistory(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req SaveLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Validate location coordinates and bounds - PRP-LOCATION-ENH-001
	if err := utils.ValidateLocationInKenya(req.Latitude, req.Longitude); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Validate location type
	validTypes := []models.LocationType{
		models.LocationTypePickup,
		models.LocationTypeDestination,
		models.LocationTypeFavorite,
		models.LocationTypeHome,
		models.LocationTypeWork,
	}

	isValidType := false
	for _, validType := range validTypes {
		if req.LocationType == validType {
			isValidType = true
			break
		}
	}

	if !isValidType {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid location type",
		})
	}

	// Check if location already exists for this user
	var existingLocation models.UserLocationHistory
	err := database.DB.Where("user_id = ? AND latitude = ? AND longitude = ?",
		userID, req.Latitude, req.Longitude).First(&existingLocation).Error

	if err == nil {
		// Location exists, update usage count and last used time
		existingLocation.UsageCount++
		existingLocation.LastUsedAt = time.Now()
		existingLocation.LocationType = req.LocationType // Update type if changed
		existingLocation.Address = req.Address           // Update address if changed

		if err := database.DB.Save(&existingLocation).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to update location history",
			})
		}

		return c.JSON(fiber.Map{
			"message": "Location history updated successfully",
			"location": GetLocationHistoryResponse{
				ID:           existingLocation.ID,
				Latitude:     existingLocation.Latitude,
				Longitude:    existingLocation.Longitude,
				Address:      existingLocation.Address,
				LocationType: existingLocation.LocationType,
				UsageCount:   existingLocation.UsageCount,
				LastUsedAt:   existingLocation.LastUsedAt,
				CreatedAt:    existingLocation.CreatedAt,
			},
		})
	}

	// Create new location history entry
	locationHistory := models.UserLocationHistory{
		UserID:       userID,
		Latitude:     req.Latitude,
		Longitude:    req.Longitude,
		Address:      req.Address,
		LocationType: req.LocationType,
		UsageCount:   1,
		LastUsedAt:   time.Now(),
	}

	if err := database.DB.Create(&locationHistory).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to save location to history",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "Location saved to history successfully",
		"location": GetLocationHistoryResponse{
			ID:           locationHistory.ID,
			Latitude:     locationHistory.Latitude,
			Longitude:    locationHistory.Longitude,
			Address:      locationHistory.Address,
			LocationType: locationHistory.LocationType,
			UsageCount:   locationHistory.UsageCount,
			LastUsedAt:   locationHistory.LastUsedAt,
			CreatedAt:    locationHistory.CreatedAt,
		},
	})
}

// GetLocationHistory retrieves user's location history
func GetLocationHistory(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get query parameters
	limitStr := c.Query("limit", "20")
	locationTypeFilter := c.Query("type", "")

	// Parse limit
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20 // Default limit
	}

	// Build query
	query := database.DB.Where("user_id = ?", userID)

	// Add location type filter if provided
	if locationTypeFilter != "" {
		query = query.Where("location_type = ?", locationTypeFilter)
	}

	// Get location history ordered by usage count and last used time
	var locationHistory []models.UserLocationHistory
	if err := query.Order("usage_count DESC, last_used_at DESC").
		Limit(limit).
		Find(&locationHistory).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to retrieve location history",
		})
	}

	// Convert to response format
	var response []GetLocationHistoryResponse
	for _, location := range locationHistory {
		response = append(response, GetLocationHistoryResponse{
			ID:           location.ID,
			Latitude:     location.Latitude,
			Longitude:    location.Longitude,
			Address:      location.Address,
			LocationType: location.LocationType,
			UsageCount:   location.UsageCount,
			LastUsedAt:   location.LastUsedAt,
			CreatedAt:    location.CreatedAt,
		})
	}

	return c.JSON(fiber.Map{
		"message":   "Location history retrieved successfully",
		"locations": response,
		"count":     len(response),
	})
}

// DeleteLocationFromHistory removes a location from user's history
func DeleteLocationFromHistory(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get location ID from URL parameter
	locationID := c.Params("id")
	if locationID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Location ID is required",
		})
	}

	// Find and delete the location
	var location models.UserLocationHistory
	if err := database.DB.Where("id = ? AND user_id = ?", locationID, userID).
		First(&location).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Location not found",
		})
	}

	if err := database.DB.Delete(&location).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete location from history",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Location deleted from history successfully",
	})
}
