package controllers

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jirani/backend/internal/database"
	"github.com/jirani/backend/internal/models"
	"github.com/jirani/backend/internal/utils"
)

// TriggerEmergencyRequest represents the request body for triggering an emergency
type TriggerEmergencyRequest struct {
	RideID    string  `json:"ride_id" validate:"required"`
	Details   string  `json:"details"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// TriggerEmergency triggers an emergency for a ride
func TriggerEmergency(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Parse request body
	var req TriggerEmergencyRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := utils.Validate.Struct(req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": utils.ValidatorErrors(err),
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND (user_id = ? OR driver_id IN (SELECT id FROM drivers WHERE user_id = ?))", req.RideID, userID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Check if ride is active
	if ride.Status != models.RideStatusAccepted &&
		ride.Status != models.RideStatusEnRouteToPickup &&
		ride.Status != models.RideStatusArrivedAtPickup &&
		ride.Status != models.RideStatusInProgress {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Emergency can only be triggered for active rides",
		})
	}

	// Update ride
	now := time.Now()
	ride.EmergencyTriggered = true
	ride.EmergencyDetails = req.Details
	ride.EmergencyTime = &now

	// Update location if provided
	if req.Latitude != 0 && req.Longitude != 0 {
		ride.CurrentLatitude = req.Latitude
		ride.CurrentLongitude = req.Longitude
		ride.LastLocationUpdate = now
	}

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to trigger emergency",
		})
	}

	// Create notification for user
	userNotification := models.Notification{
		UserID:  ride.UserID,
		Type:    models.EmergencyTriggered,
		Title:   "Emergency Triggered",
		Message: "Emergency services have been notified. Stay safe.",
		RideID:  ride.ID,
	}

	if err := database.DB.Create(&userNotification).Error; err != nil {
		// Log error but continue
		if err := database.DB.Create(&models.Notification{
			UserID:  userID,
			Type:    models.SystemMessage,
			Title:   "Notification Error",
			Message: "Failed to create emergency notification",
		}).Error; err != nil {
			// Ignore error
		}
	}

	// Create notification for driver if the user triggered the emergency
	if userID == ride.UserID && ride.DriverID != nil && *ride.DriverID != "" {
		// Get driver's user ID
		var driver models.Driver
		if err := database.DB.Where("id = ?", *ride.DriverID).First(&driver).Error; err == nil {
			driverNotification := models.Notification{
				UserID:  driver.UserID,
				Type:    models.EmergencyTriggered,
				Title:   "Emergency Triggered",
				Message: "A passenger has triggered an emergency alert. Emergency services have been notified.",
				RideID:  ride.ID,
			}

			if err := database.DB.Create(&driverNotification).Error; err != nil {
				// Ignore error
			}
		}
	}

	// Create notification for user if the driver triggered the emergency
	if userID != ride.UserID {
		passengerNotification := models.Notification{
			UserID:  ride.UserID,
			Type:    models.EmergencyTriggered,
			Title:   "Emergency Triggered",
			Message: "Your driver has triggered an emergency alert. Emergency services have been notified.",
			RideID:  ride.ID,
		}

		if err := database.DB.Create(&passengerNotification).Error; err != nil {
			// Ignore error
		}
	}

	// TODO: Notify emergency services (this would be implemented with a real emergency service API)

	return c.JSON(fiber.Map{
		"message": "Emergency triggered successfully",
	})
}

// CancelEmergency cancels an emergency for a ride
func CancelEmergency(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND (user_id = ? OR driver_id IN (SELECT id FROM drivers WHERE user_id = ?))", rideID, userID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	// Check if emergency is triggered
	if !ride.EmergencyTriggered {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "No emergency is triggered for this ride",
		})
	}

	// Update ride
	ride.EmergencyTriggered = false
	ride.EmergencyDetails += " [CANCELLED]"

	if err := database.DB.Save(&ride).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to cancel emergency",
		})
	}

	// Create notification for user
	userNotification := models.Notification{
		UserID:  ride.UserID,
		Type:    models.SystemMessage,
		Title:   "Emergency Cancelled",
		Message: "The emergency alert has been cancelled.",
		RideID:  ride.ID,
	}

	if err := database.DB.Create(&userNotification).Error; err != nil {
		// Ignore error
	}

	// Create notification for driver
	if ride.DriverID != nil && *ride.DriverID != "" {
		var driver models.Driver
		if err := database.DB.Where("id = ?", *ride.DriverID).First(&driver).Error; err == nil {
			driverNotification := models.Notification{
				UserID:  driver.UserID,
				Type:    models.SystemMessage,
				Title:   "Emergency Cancelled",
				Message: "The emergency alert has been cancelled.",
				RideID:  ride.ID,
			}

			if err := database.DB.Create(&driverNotification).Error; err != nil {
				// Ignore error
			}
		}
	}

	// TODO: Notify emergency services of cancellation

	return c.JSON(fiber.Map{
		"message": "Emergency cancelled successfully",
	})
}

// GetEmergencyStatus returns the emergency status for a ride
func GetEmergencyStatus(c *fiber.Ctx) error {
	// Get user ID from context
	userID := c.Locals("user_id").(string)

	// Get ride ID from params
	rideID := c.Params("id")
	if rideID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Ride ID is required",
		})
	}

	// Find ride
	var ride models.Ride
	if err := database.DB.Where("id = ? AND (user_id = ? OR driver_id IN (SELECT id FROM drivers WHERE user_id = ?))", rideID, userID, userID).First(&ride).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Ride not found",
		})
	}

	return c.JSON(fiber.Map{
		"emergency_triggered": ride.EmergencyTriggered,
		"emergency_details":   ride.EmergencyDetails,
		"emergency_time":      ride.EmergencyTime,
	})
}
