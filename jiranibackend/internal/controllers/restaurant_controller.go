package controllers

import (
	"github.com/gofiber/fiber/v2"
)

// ListRestaurants lists all restaurants
func ListRestaurants(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "List restaurants endpoint",
	})
}

// GetRestaurant gets a restaurant by ID
func GetRestaurant(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Get restaurant endpoint",
	})
}

// GetRestaurantMenu gets a restaurant's menu
func GetRestaurantMenu(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Get restaurant menu endpoint",
	})
}

// CreateRestaurant creates a new restaurant
func CreateRestaurant(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Create restaurant endpoint",
	})
}

// UpdateRestaurant updates a restaurant
func UpdateRestaurant(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Update restaurant endpoint",
	})
}

// DeleteRestaurant deletes a restaurant
func DeleteRestaurant(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Delete restaurant endpoint",
	})
}
