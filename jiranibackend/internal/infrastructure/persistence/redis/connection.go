package redis

import (
	"context"
	"fmt"
	"log"

	"github.com/jirani/backend/internal/config"
	"github.com/redis/go-redis/v9"
)

var (
	// Client is the global Redis client
	Client *redis.Client
)

// Connect establishes a connection to Redis
func Connect(cfg *config.Config) error {
	// Connect to Redis
	Client = redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Password: cfg.RedisPassword,
		DB:       0,
	})

	// Test Redis connection
	ctx := context.Background()
	if _, err := Client.Ping(ctx).Result(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Println("Connected to Redis successfully")
	return nil
}

// GetClient returns the Redis client
func GetClient() *redis.Client {
	return Client
}
