#!/bin/bash

# Test script for uploading files to the storage service

# Check if a file path was provided
if [ $# -lt 1 ]; then
  echo "Usage: $0 <image_file_path>"
  echo "Example: $0 small_test.png"
  exit 1
fi

# Get the file path
FILE_PATH=$1

# API endpoint for file upload (using the profile image upload endpoint)
API_URL="https://api.jirani.tufiked.live/api/v1/users/me/profileimage"

# First, let's try to login to get an auth token
echo "Logging in to get auth token..."
LOGIN_RESPONSE=$(curl -s -k -X POST "https://api.jirani.tufiked.live/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Kimathi@2022"}')

# Print the login response for debugging
echo "Login response: $LOGIN_RESPONSE"

# Extract the token from the login response
AUTH_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | sed 's/"token":"//')

if [ -z "$AUTH_TOKEN" ]; then
  echo "Failed to get auth token. Using test token instead."
  AUTH_TOKEN="test_token"
else
  echo "Successfully obtained auth token."
fi

# Upload the file
echo "Uploading file to $API_URL..."
UPLOAD_RESPONSE=$(curl -s -k -X POST "$API_URL" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -F "image=@$FILE_PATH")

# Print the response
echo "Upload response:"
echo "$UPLOAD_RESPONSE"

# Check if the upload was successful
if echo "$UPLOAD_RESPONSE" | grep -q "image_url"; then
  echo "Upload successful!"

  # Extract the URL from the response
  FILE_URL=$(echo $UPLOAD_RESPONSE | grep -o '"image_url":"[^"]*' | sed 's/"image_url":"//')

  echo "File URL: $FILE_URL"
  echo "Try opening this URL in your browser to verify the image is accessible."

  # Open the URL in the browser
  if command -v xdg-open >/dev/null 2>&1; then
    xdg-open "$FILE_URL"
  elif command -v open >/dev/null 2>&1; then
    open "$FILE_URL"
  else
    echo "Could not open URL automatically. Please copy and paste it into your browser."
  fi
else
  echo "Upload failed!"
fi