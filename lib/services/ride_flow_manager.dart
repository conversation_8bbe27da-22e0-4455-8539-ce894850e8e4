import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ride.dart';
import '../models/rider.dart';
import '../models/lat_lng.dart' as custom;
import '../models/websocket_message.dart';
import '../providers/websocket_provider.dart';
import '../providers/ride_provider.dart';
import '../services/geofencing_service.dart';
import '../services/notification_service.dart';
import '../services/logging_service.dart';
import '../screens/boda_boda/real_time_tracking_screen.dart';
import '../screens/boda_boda/ride_completion_screen.dart';

/// Manages the complete ride flow from request to completion
class RideFlowManager {
  static final RideFlowManager _instance = RideFlowManager._internal();
  factory RideFlowManager() => _instance;
  RideFlowManager._internal();

  final RideGeofencingManager _geofencingManager = RideGeofencingManager();
  final NotificationService _notificationService = NotificationService();
  
  StreamSubscription<WebSocketMessage>? _rideStatusSubscription;
  StreamSubscription<WebSocketMessage>? _driverLocationSubscription;
  StreamSubscription<GeofenceEvent>? _geofenceSubscription;
  
  Ride? _currentRide;
  Rider? _currentRider;
  BuildContext? _context;
  WidgetRef? _ref;

  /// Initialize the ride flow manager
  Future<void> initialize(BuildContext context, WidgetRef ref) async {
    _context = context;
    _ref = ref;
    
    await _notificationService.initialize();
    _setupWebSocketListeners();
    _setupGeofenceListeners();
    
    LoggingService.i('RideFlowManager initialized');
  }

  /// Start a new ride flow
  Future<void> startRideFlow({
    required Ride ride,
    required Rider rider,
  }) async {
    _currentRide = ride;
    _currentRider = rider;

    // Set up geofences for the ride
    _geofencingManager.setupRideGeofences(
      ride.id,
      ride.pickup.location,
      ride.dropoff.location,
    );

    // Connect to WebSocket for real-time updates
    await _ref?.read(webSocketProvider.notifier).connect();

    // Navigate to tracking screen
    if (_context != null && _currentRider != null) {
      Navigator.of(_context!).pushReplacement(
        MaterialPageRoute(
          builder: (context) => RealTimeTrackingScreen(
            ride: ride,
            rider: _currentRider!,
          ),
        ),
      );
    }

    LoggingService.i('Ride flow started for ride: ${ride.id}');
  }

  /// Setup WebSocket listeners for ride updates
  void _setupWebSocketListeners() {
    // Listen for ride status updates
    _rideStatusSubscription = _ref?.read(rideStatusUpdateProvider.stream).listen(
      (message) {
        if (_currentRide != null && message.rideId == _currentRide!.id) {
          _handleRideStatusUpdate(message);
        }
      },
    );

    // Listen for driver location updates
    _driverLocationSubscription = _ref?.read(driverLocationProvider.stream).listen(
      (message) {
        if (_currentRide != null && message.rideId == _currentRide!.id) {
          _handleDriverLocationUpdate(message);
        }
      },
    );
  }

  /// Setup geofence listeners
  void _setupGeofenceListeners() {
    _geofenceSubscription = _geofencingManager.eventStream.listen(
      (event) => _handleGeofenceEvent(event),
    );
  }

  /// Handle ride status updates
  void _handleRideStatusUpdate(WebSocketMessage message) {
    final status = message.data['status'] as String?;
    if (status == null) return;

    LoggingService.i('Ride status update: $status');

    // Show appropriate notification
    final notification = NotificationService.createRideStatusNotification(
      status: status,
      riderName: _currentRider?.name,
      estimatedTime: message.data['estimated_time'] as String?,
    );
    _notificationService.showRideNotification(notification);

    // Handle specific status changes
    switch (status) {
      case 'rider_assigned':
        _handleRiderAssigned(message);
        break;
      case 'rider_arriving':
        _handleRiderArriving(message);
        break;
      case 'rider_arrived':
        _handleRiderArrived(message);
        break;
      case 'trip_started':
        _handleTripStarted(message);
        break;
      case 'trip_completed':
        _handleTripCompleted(message);
        break;
      case 'ride_cancelled':
        _handleRideCancelled(message);
        break;
    }
  }

  /// Handle driver location updates
  void _handleDriverLocationUpdate(WebSocketMessage message) {
    final latitude = message.data['latitude'] as double?;
    final longitude = message.data['longitude'] as double?;
    
    if (latitude != null && longitude != null) {
      final location = custom.LatLng(latitude, longitude);
      
      // Update geofencing with new location
      _geofencingManager.updateLocation(location);
      
      // Add driver proximity geofence if not already added
      if (_currentRide != null) {
        _geofencingManager.addDriverProximityGeofence(_currentRide!.id, location);
      }
    }
  }

  /// Handle geofence events
  void _handleGeofenceEvent(GeofenceEvent event) {
    LoggingService.i('Geofence event: ${event.geofenceId} - ${event.eventType}');

    switch (event.eventType) {
      case GeofenceEventType.enter:
        _handleGeofenceEnter(event);
        break;
      case GeofenceEventType.exit:
        _handleGeofenceExit(event);
        break;
      case GeofenceEventType.dwell:
        // Handle dwelling in geofence if needed
        break;
    }
  }

  /// Handle entering a geofence
  void _handleGeofenceEnter(GeofenceEvent event) {
    if (event.geofenceId.contains('pickup')) {
      // Driver arrived at pickup
      final notification = NotificationService.createRideStatusNotification(
        status: 'rider_arrived',
        riderName: _currentRider?.name,
      );
      _notificationService.showRideNotification(notification);
    } else if (event.geofenceId.contains('destination')) {
      // Approaching destination
      final notification = RideNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: 'Approaching Destination',
        body: 'You are almost at your destination',
        type: RideNotificationType.general,
        priority: NotificationPriority.medium,
        timestamp: DateTime.now(),
      );
      _notificationService.showRideNotification(notification);
    } else if (event.geofenceId.contains('driver_proximity')) {
      // Driver is nearby
      final notification = RideNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: 'Rider Nearby',
        body: 'Your rider is approaching the pickup location',
        type: RideNotificationType.riderArriving,
        priority: NotificationPriority.medium,
        timestamp: DateTime.now(),
      );
      _notificationService.showRideNotification(notification);
    }
  }

  /// Handle exiting a geofence
  void _handleGeofenceExit(GeofenceEvent event) {
    if (event.geofenceId.contains('pickup')) {
      // Left pickup area - trip started
      final notification = NotificationService.createRideStatusNotification(
        status: 'trip_started',
      );
      _notificationService.showRideNotification(notification);
    }
  }

  /// Handle rider assigned
  void _handleRiderAssigned(WebSocketMessage message) {
    // Update rider information if provided
    final riderData = message.data['rider'] as Map<String, dynamic>?;
    if (riderData != null) {
      // Update current rider with new data
      // This would parse the rider data and update _currentRider
    }
  }

  /// Handle rider arriving
  void _handleRiderArriving(WebSocketMessage message) {
    final estimatedTime = message.data['estimated_time'] as String?;
    LoggingService.i('Rider arriving in: $estimatedTime');
  }

  /// Handle rider arrived
  void _handleRiderArrived(WebSocketMessage message) {
    // Rider has arrived at pickup location
    LoggingService.i('Rider arrived at pickup location');
  }

  /// Handle trip started
  void _handleTripStarted(WebSocketMessage message) {
    // Trip has begun
    LoggingService.i('Trip started');
  }

  /// Handle trip completed
  void _handleTripCompleted(WebSocketMessage message) {
    // Navigate to completion screen
    if (_context != null && _currentRide != null) {
      Navigator.of(_context!).pushReplacement(
        MaterialPageRoute(
          builder: (context) => RideCompletionScreen(
            ride: _currentRide!,
            rider: _currentRider,
          ),
        ),
      );
    }
    
    // Clean up
    _cleanupRide();
    LoggingService.i('Trip completed');
  }

  /// Handle ride cancelled
  void _handleRideCancelled(WebSocketMessage message) {
    final reason = message.data['reason'] as String?;
    
    // Show cancellation dialog
    if (_context != null) {
      showDialog(
        context: _context!,
        builder: (context) => AlertDialog(
          title: const Text('Ride Cancelled'),
          content: Text(reason ?? 'Your ride has been cancelled'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
    
    // Clean up
    _cleanupRide();
    LoggingService.i('Ride cancelled: $reason');
  }

  /// Clean up ride resources
  void _cleanupRide() {
    if (_currentRide != null) {
      _geofencingManager.cleanupRideGeofences(_currentRide!.id);
    }
    
    _currentRide = null;
    _currentRider = null;
  }

  /// Handle emergency situation
  void handleEmergency(String rideId) {
    // Send emergency message via WebSocket
    _ref?.read(webSocketProvider.notifier).sendMessage(
      WebSocketMessage(
        type: WebSocketMessageType.emergency,
        rideId: rideId,
        data: {
          'emergency': true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        timestamp: DateTime.now().millisecondsSinceEpoch,
      ),
    );

    // Show emergency notification
    final notification = NotificationService.createEmergencyNotification(
      message: 'Emergency alert has been sent to authorities and emergency contacts',
    );
    _notificationService.showRideNotification(notification);

    LoggingService.i('Emergency triggered for ride: $rideId');
  }

  /// Dispose resources
  void dispose() {
    _rideStatusSubscription?.cancel();
    _driverLocationSubscription?.cancel();
    _geofenceSubscription?.cancel();
    _geofencingManager.dispose();
    _notificationService.dispose();
    
    _currentRide = null;
    _currentRider = null;
    _context = null;
    _ref = null;
    
    LoggingService.i('RideFlowManager disposed');
  }
}

/// Provider for the ride flow manager
final rideFlowManagerProvider = Provider<RideFlowManager>((ref) {
  return RideFlowManager();
});
