# Makefile for <PERSON>rani Backend

# Variables
APP_NAME := jirani-backend
MAIN_PATH := ./main.go
BUILD_DIR := ./build
DOCKER_COMPOSE := docker-compose
GO := go
GOTEST := $(GO) test
GOVET := $(GO) vet
GOLINT := golint
GOFMT := gofmt
GOMOD := $(GO) mod
GOGET := $(GO) get
GOBUILD := $(GO) build
GORUN := $(GO) run

# Default target
.PHONY: all
all: clean build

# Build the application
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_PATH)
	@echo "Build complete!"

# Run the application
.PHONY: run
run:
	@echo "Running $(APP_NAME)..."
	$(GORUN) $(MAIN_PATH)

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@echo "Clean complete!"

# Test the application
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -cover -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html

# Lint the code
.PHONY: lint
lint:
	@echo "Linting code..."
	$(GOVET) ./...
	@if command -v $(GOLINT) > /dev/null; then \
		$(GOLINT) ./...; \
	else \
		echo "golint not installed. Run: go install golang.org/x/lint/golint@latest"; \
	fi

# Format the code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) -w .

# Update dependencies
.PHONY: deps
deps:
	@echo "Updating dependencies..."
	$(GOMOD) tidy
	$(GOGET) -u ./...

# Docker commands
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME) .

.PHONY: docker-up
docker-up:
	@echo "Starting Docker containers..."
	$(DOCKER_COMPOSE) up -d

.PHONY: docker-down
docker-down:
	@echo "Stopping Docker containers..."
	$(DOCKER_COMPOSE) down

.PHONY: docker-logs
docker-logs:
	@echo "Showing Docker logs..."
	$(DOCKER_COMPOSE) logs -f

# Database migrations
.PHONY: migrate-up
migrate-up:
	@echo "Running database migrations..."
	@if [ -d "./migrations" ]; then \
		echo "Using migrate command..."; \
		migrate -path ./migrations -database "postgres://jirani:jirani123@localhost:5432/jiranidb?sslmode=disable" up; \
	else \
		echo "Migrations directory not found"; \
	fi

.PHONY: migrate-down
migrate-down:
	@echo "Rolling back database migrations..."
	@if [ -d "./migrations" ]; then \
		echo "Using migrate command..."; \
		migrate -path ./migrations -database "postgres://jirani:jirani123@localhost:5432/jiranidb?sslmode=disable" down 1; \
	else \
		echo "Migrations directory not found"; \
	fi

# Generate API documentation
.PHONY: swagger
swagger:
	@echo "Generating Swagger documentation..."
	@if command -v swag > /dev/null; then \
		swag init -g $(MAIN_PATH) -o ./api/swagger; \
	else \
		echo "swag not installed. Run: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# Help command
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  make build          - Build the application"
	@echo "  make run            - Run the application"
	@echo "  make clean          - Clean build artifacts"
	@echo "  make test           - Run tests"
	@echo "  make test-coverage  - Run tests with coverage"
	@echo "  make lint           - Lint the code"
	@echo "  make fmt            - Format the code"
	@echo "  make deps           - Update dependencies"
	@echo "  make docker-build   - Build Docker image"
	@echo "  make docker-up      - Start Docker containers"
	@echo "  make docker-down    - Stop Docker containers"
	@echo "  make docker-logs    - Show Docker logs"
	@echo "  make migrate-up     - Run database migrations"
	@echo "  make migrate-down   - Roll back database migrations"
	@echo "  make swagger        - Generate Swagger documentation"
	@echo "  make help           - Show this help message"
