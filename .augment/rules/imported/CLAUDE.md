---
type: "manual"
---

# BodaBoda Context Engineering for Claude AI

## 1. Overview
This document provides comprehensive context engineering guidelines for the BodaBoda feature development in the Jirani app. It ensures consistent, high-quality development across all iterations and builds.

## 2. Core Context Principles

### 2.1 Development Philosophy
- **Context-First Development**: Every component must be developed with full context awareness
- **Validation-Driven**: All implementations must pass predefined validation gates
- **Performance-Conscious**: Real-time features require sub-100ms response times
- **Security-First**: All data transmission and storage must be encrypted and validated
- **User-Centric**: Every feature must enhance user experience and safety

### 2.2 Architecture Context
```
Frontend (Flutter) ↔ WebSocket Hub ↔ Backend (Go Fiber) ↔ Database (PostgreSQL)
                  ↕                                      ↕
            Mapbox APIs                              GCP Services
```

### 2.3 Technology Stack Context
- **Frontend**: Flutter 3.x with Riverpod state management
- **Backend**: Go Fiber framework with WebSocket support
- **Database**: PostgreSQL with GORM ORM
- **Real-time**: WebSocket connections with auto-reconnection
- **Maps**: Mapbox SDK for Flutter with custom styling
- **Cloud**: Google Cloud Platform with Docker deployment
- **Authentication**: JWT tokens with refresh mechanism

## 3. Context Rules for Development

### 3.1 Flutter Development Context

#### State Management Rules
- Use Riverpod providers for all state management
- Implement proper provider scoping to prevent memory leaks
- Use `StateNotifierProvider` for complex state logic
- Use `FutureProvider` for async operations
- Use `StreamProvider` for real-time data streams

#### UI Responsiveness Rules
- No blocking operations on the main thread
- Use `Isolate` for heavy computations
- Implement proper loading states for all async operations
- Use `AnimatedBuilder` for smooth animations
- Implement proper error boundaries with user-friendly messages

#### Memory Management Rules
- Dispose of all controllers, streams, and listeners
- Use `AutoDisposeProvider` for temporary state
- Implement proper widget lifecycle management
- Monitor memory usage during development
- Use `WeakReference` for callback references

#### Battery Optimization Rules
- Implement smart location tracking (motion-based updates)
- Use background processing efficiently
- Implement proper app lifecycle handling
- Optimize WebSocket connection management
- Use efficient data structures and algorithms

### 3.2 Go Fiber Backend Context

#### API Design Rules
- Follow RESTful principles with proper HTTP semantics
- Use consistent response formats across all endpoints
- Implement proper HTTP status codes
- Use middleware for cross-cutting concerns
- Implement comprehensive input validation

#### WebSocket Management Rules
- Implement graceful connection handling and cleanup
- Use connection pooling for scalability
- Implement proper message queuing
- Handle connection failures with exponential backoff
- Implement heartbeat mechanism for connection health

#### Database Transaction Rules
- Use proper transaction boundaries
- Implement rollback handling for failed operations
- Use connection pooling for performance
- Implement query optimization
- Use prepared statements to prevent SQL injection

#### Middleware Stack Rules
- Authentication middleware for protected routes
- Logging middleware for request/response tracking
- Rate limiting middleware for API protection
- CORS middleware for cross-origin requests
- Error handling middleware for consistent error responses

### 3.3 GCP Integration Context

#### Service Authentication Rules
- Use proper IAM roles and service account management
- Implement least privilege access principles
- Use service account keys securely
- Implement proper token rotation
- Monitor access patterns and anomalies

#### Monitoring Rules
- Implement comprehensive logging with structured formats
- Use Cloud Monitoring for metrics collection
- Set up alerting for critical system events
- Implement distributed tracing for request flows
- Monitor resource usage and performance metrics

#### Scaling Rules
- Implement auto-scaling policies for traffic spikes
- Use load balancing for high availability
- Implement circuit breakers for service resilience
- Use caching strategies for performance optimization
- Monitor and optimize resource utilization

## 4. Validation Gates

### 4.1 Pre-Implementation Validation
- [ ] All dependencies are properly configured
- [ ] Development environment is set up correctly
- [ ] Database migrations are prepared and tested
- [ ] GCP services are provisioned and accessible
- [ ] API documentation is comprehensive and up-to-date
- [ ] Security requirements are defined and understood
- [ ] Performance benchmarks are established

### 4.2 Implementation Validation
- [ ] Unit tests pass with >90% coverage
- [ ] Integration tests validate cross-service communication
- [ ] WebSocket connections are stable under load testing
- [ ] Real-time features synchronize correctly across clients
- [ ] Error handling provides meaningful feedback to users
- [ ] Security vulnerabilities are identified and addressed
- [ ] Performance requirements are met or exceeded

### 4.3 Post-Implementation Validation
- [ ] Performance benchmarks are met in production environment
- [ ] Security audit passes with no critical vulnerabilities
- [ ] User acceptance testing is successful
- [ ] Production deployment is smooth with zero downtime
- [ ] Monitoring and alerting are functional and accurate
- [ ] Documentation is updated and comprehensive
- [ ] Team knowledge transfer is complete

## 5. Success Metrics

### 5.1 Technical Metrics
- **Response Time**: <200ms for API calls, <100ms for WebSocket messages
- **Uptime**: >99.9% system availability
- **Error Rate**: <0.1% error rate for critical operations
- **Performance**: <5% CPU usage, <100MB memory usage on mobile
- **Scalability**: Support 1000+ concurrent WebSocket connections

### 5.2 User Experience Metrics
- **User Satisfaction**: >4.5/5 app store rating
- **Completion Rate**: >95% ride completion rate
- **Response Time**: <30 seconds average driver response time
- **Safety**: Zero security incidents, <10 second emergency response
- **Accessibility**: Full compliance with accessibility guidelines

### 5.3 Business Metrics
- **Ride Success Rate**: >98% successful ride completion
- **Driver Utilization**: >80% driver active time
- **Revenue**: Meeting revenue targets with proper payment processing
- **Growth**: Positive user acquisition and retention metrics
- **Cost Efficiency**: Optimized cloud resource usage

## 6. Emergency Procedures

### 6.1 System Downtime
- Automated failover to backup systems
- Immediate notification to development team
- User communication through multiple channels
- Rollback procedures for failed deployments
- Post-incident analysis and documentation

### 6.2 Security Incidents
- Immediate isolation of affected systems
- User notification within required timeframes
- Forensic analysis and evidence preservation
- Regulatory compliance reporting
- Security patch deployment and validation

### 6.3 Performance Degradation
- Auto-scaling activation for increased load
- Load balancing redistribution
- Resource optimization and cleanup
- Performance monitoring and alerting
- Capacity planning and resource provisioning

## 7. Continuous Improvement

### 7.1 Regular Reviews
- Weekly performance metric reviews
- Monthly security audit reviews
- Quarterly architecture reviews
- Annual technology stack reviews
- Continuous user feedback integration

### 7.2 Technology Updates
- Regular dependency updates and security patches
- Framework version upgrades with proper testing
- Cloud service optimization and cost management
- Performance optimization based on metrics
- Feature enhancement based on user feedback

This context engineering framework ensures consistent, high-quality development of the BodaBoda feature across all iterations and builds.
